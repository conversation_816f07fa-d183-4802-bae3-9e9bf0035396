import { expect, test } from '@playwright/test';

import { ROUTES } from '../../src/constants/routes';

test.use({ storageState: { cookies: [], origins: [] } });

test.describe('Log in', () => {
  test('Verify login with valid email and password', async ({ page }) => {
    await page.goto(ROUTES.HOME);
    await expect(page.getByRole('heading', { name: 'A Smarter Way to Trade Crypto' })).toBeVisible();

    await page.getByRole('button', { name: 'Continue' }).click();

    await expect(page.getByRole('heading', { name: 'Manual Trading Made Easy' })).toBeVisible();

    await page.getByRole('button', { name: 'Continue' }).click();

    await expect(page.getByRole('heading', { name: 'Automated Bots for 24/7' })).toBeVisible();

    await page.getByRole('button', { name: 'Continue' }).click();
    await page.getByRole('button', { name: 'Set up wallet' }).click();
    await page.getByRole('tab', { name: 'Login' }).click();

    await page.getByRole('textbox', { name: 'Email address' }).fill(process.env.PLAYWRIGHT_USER ?? '');

    await page.getByRole('textbox', { name: 'Password' }).fill(process.env.PLAYWRIGHT_PASSWORD ?? '');

    await page.getByRole('button', { name: 'Login' }).click();
    await page.waitForURL(ROUTES.HOME);
  });
  test('Verify error message for invalid email format', async ({ page }) => {
    await page.goto(ROUTES.HOME);
    await page.getByRole('button', { name: 'Skip' }).click();

    await page.getByRole('button', { name: 'Set up wallet' }).click();
    await page.getByRole('tab', { name: 'Login' }).click();

    await page.getByRole('textbox', { name: 'Email address' }).fill('invalid.com');

    await page.getByRole('textbox', { name: 'Password' }).fill('xxx');
    await page.getByRole('button', { name: 'Login' }).click();

    await expect(page.getByText('Email is not in the correct format')).toBeVisible();
  });
  test('Show error message when trying to log in while email is not verified', async ({ page }) => {
    const PASSWORD = 'Asdf123$';
    const EMAIL = `lesssleep.moredance+${new Date().getTime()}@gmail.com`;

    await page.goto(ROUTES.HOME);
    await page.getByRole('button', { name: 'Skip' }).click();
    await page.getByRole('button', { name: 'Set up wallet' }).click();

    await page.getByRole('textbox', { name: 'Email address' }).fill(EMAIL);
    await page.getByRole('textbox', { name: 'Password' }).fill(PASSWORD);
    await page.getByRole('button', { name: 'Sign up' }).click();

    await expect(page.getByText('Thank you for signing up!')).toBeVisible();

    await page.getByRole('tab', { name: 'Login' }).click();
    await page.getByRole('textbox', { name: 'Email address' }).fill(EMAIL);
    await page.getByRole('textbox', { name: 'Password' }).fill(PASSWORD);
    await page.getByRole('button', { name: 'Login' }).click();

    await expect(page.getByText('This email address has not been verified. Please check your email.')).toBeVisible();
  });
});
