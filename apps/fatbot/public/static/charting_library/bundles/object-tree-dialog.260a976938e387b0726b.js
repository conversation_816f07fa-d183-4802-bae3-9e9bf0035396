(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[4862,2544],{88276:e=>{e.exports={container:"container-WDZ0PRNh","container-xxsmall":"container-xxsmall-WDZ0PRNh","container-xsmall":"container-xsmall-WDZ0PRNh","container-small":"container-small-WDZ0PRNh","container-medium":"container-medium-WDZ0PRNh","container-large":"container-large-WDZ0PRNh","intent-default":"intent-default-WDZ0PRNh",focused:"focused-WDZ0PRNh",readonly:"readonly-WDZ0PRNh",disabled:"disabled-WDZ0PRNh","with-highlight":"with-highlight-WDZ0PRNh",grouped:"grouped-WDZ0PRNh","adjust-position":"adjust-position-WDZ0PRNh","first-row":"first-row-WDZ0PRNh","first-col":"first-col-WDZ0PRNh",stretch:"stretch-WDZ0PRNh","font-size-medium":"font-size-medium-WDZ0PRNh","font-size-large":"font-size-large-WDZ0PRNh","no-corner-top-left":"no-corner-top-left-WDZ0PRNh","no-corner-top-right":"no-corner-top-right-WDZ0PRNh","no-corner-bottom-right":"no-corner-bottom-right-WDZ0PRNh","no-corner-bottom-left":"no-corner-bottom-left-WDZ0PRNh","size-xxsmall":"size-xxsmall-WDZ0PRNh","size-xsmall":"size-xsmall-WDZ0PRNh","size-small":"size-small-WDZ0PRNh","size-medium":"size-medium-WDZ0PRNh","size-large":"size-large-WDZ0PRNh","intent-success":"intent-success-WDZ0PRNh","intent-warning":"intent-warning-WDZ0PRNh","intent-danger":"intent-danger-WDZ0PRNh","intent-primary":"intent-primary-WDZ0PRNh","border-none":"border-none-WDZ0PRNh","border-thin":"border-thin-WDZ0PRNh","border-thick":"border-thick-WDZ0PRNh",highlight:"highlight-WDZ0PRNh",shown:"shown-WDZ0PRNh"}},73405:e=>{e.exports={"inner-slot":"inner-slot-W53jtLjw",interactive:"interactive-W53jtLjw",icon:"icon-W53jtLjw","inner-middle-slot":"inner-middle-slot-W53jtLjw","before-slot":"before-slot-W53jtLjw","after-slot":"after-slot-W53jtLjw"}},25549:e=>{e.exports={input:"input-RUSovanF","size-xxsmall":"size-xxsmall-RUSovanF","size-xsmall":"size-xsmall-RUSovanF","size-small":"size-small-RUSovanF","size-medium":"size-medium-RUSovanF","size-large":"size-large-RUSovanF","with-start-slot":"with-start-slot-RUSovanF","with-end-slot":"with-end-slot-RUSovanF"}},34869:e=>{e.exports={hidden:"hidden-DgcIT6Uz",fadeInWrapper:"fadeInWrapper-DgcIT6Uz"}},85862:e=>{e.exports={disableSelfPositioning:"disableSelfPositioning-dYiqkKAE"}},8255:e=>{e.exports={removeButton:"removeButton-BadjY5sX",favoriteButton:"favoriteButton-BadjY5sX",itemRow:"itemRow-BadjY5sX",focused:"focused-BadjY5sX",active:"active-BadjY5sX",actionButton:"actionButton-BadjY5sX",mobile:"mobile-BadjY5sX",itemInfo:"itemInfo-BadjY5sX",title:"title-BadjY5sX",details:"details-BadjY5sX",itemInfoWithPadding:"itemInfoWithPadding-BadjY5sX",favorite:"favorite-BadjY5sX",showOnFocus:"showOnFocus-BadjY5sX"}},18561:e=>{e.exports={scrollWrap:"scrollWrap-FaOvTD2r"}},51810:e=>{e.exports={wrap:"wrap-vSb6C0Bj","wrap--horizontal":"wrap--horizontal-vSb6C0Bj",bar:"bar-vSb6C0Bj",barInner:"barInner-vSb6C0Bj","barInner--horizontal":"barInner--horizontal-vSb6C0Bj","bar--horizontal":"bar--horizontal-vSb6C0Bj"}},36612:e=>{e.exports={dropTargetInside:"dropTargetInside-e_nPSSdZ",
dropTarget:"dropTarget-e_nPSSdZ",before:"before-e_nPSSdZ",after:"after-e_nPSSdZ"}},69867:e=>{e.exports={sticky:"sticky-U0YaDVkl",hideSticky:"hideSticky-U0YaDVkl"}},56726:e=>{e.exports={wrap:"wrap-IEe5qpW4",selected:"selected-IEe5qpW4",childOfSelected:"childOfSelected-IEe5qpW4",disabled:"disabled-IEe5qpW4",expandHandle:"expandHandle-IEe5qpW4",expanded:"expanded-IEe5qpW4"}},27802:e=>{e.exports={separator:"separator-MgF6KBas",sticky:"sticky-MgF6KBas",accessible:"accessible-MgF6KBas",tree:"tree-MgF6KBas",overlayScrollWrap:"overlayScrollWrap-MgF6KBas",listContainer:"listContainer-MgF6KBas"}},48452:e=>{e.exports={title:"title-QPktCwTY",container:"container-QPktCwTY",mobile:"mobile-QPktCwTY",empty:"empty-QPktCwTY",image:"image-QPktCwTY",spinner:"spinner-QPktCwTY",contentList:"contentList-QPktCwTY",item:"item-QPktCwTY"}},65441:e=>{e.exports={dialog:"dialog-VUnQLSMH",buttons:"buttons-VUnQLSMH",button:"button-VUnQLSMH",disabled:"disabled-VUnQLSMH"}},43478:e=>{e.exports={title:"title-uNZ8yW1y",withoutIcon:"withoutIcon-uNZ8yW1y",buttons:"buttons-uNZ8yW1y",button:"button-uNZ8yW1y",disabled:"disabled-uNZ8yW1y",spacing:"spacing-uNZ8yW1y",toolbar:"toolbar-uNZ8yW1y"}},6746:e=>{e.exports={wrap:"wrap-C8ln3wvp",dialog:"dialog-C8ln3wvp",mobile:"mobile-C8ln3wvp",offset:"offset-C8ln3wvp",title:"title-C8ln3wvp",main:"main-C8ln3wvp",disabled:"disabled-C8ln3wvp",icon:"icon-C8ln3wvp",pathIcon:"pathIcon-C8ln3wvp",syncIconWrap:"syncIconWrap-C8ln3wvp",syncIcon:"syncIcon-C8ln3wvp",rightButtons:"rightButtons-C8ln3wvp",hover:"hover-C8ln3wvp",expandHandle:"expandHandle-C8ln3wvp",button:"button-C8ln3wvp",selected:"selected-C8ln3wvp",childOfSelected:"childOfSelected-C8ln3wvp",renameInput:"renameInput-C8ln3wvp",warn:"warn-C8ln3wvp",visible:"visible-C8ln3wvp"}},42905:e=>{e.exports={wrap:"wrap-ukH4sVzT",space:"space-ukH4sVzT",tree:"tree-ukH4sVzT"}},36718:e=>{e.exports={"default-drawer-min-top-distance":"100px",wrap:"wrap-_HnK0UIN",positionBottom:"positionBottom-_HnK0UIN",backdrop:"backdrop-_HnK0UIN",drawer:"drawer-_HnK0UIN",positionLeft:"positionLeft-_HnK0UIN"}},22413:e=>{e.exports={favorite:"favorite-_FRQhM5Y",hovered:"hovered-_FRQhM5Y",disabled:"disabled-_FRQhM5Y",focused:"focused-_FRQhM5Y",active:"active-_FRQhM5Y",checked:"checked-_FRQhM5Y"}},8510:e=>{e.exports={button:"button-w6lVe_oI",hovered:"hovered-w6lVe_oI",disabled:"disabled-w6lVe_oI"}},67797:e=>{e.exports={menuWrap:"menuWrap-Kq3ruQo8",isMeasuring:"isMeasuring-Kq3ruQo8",scrollWrap:"scrollWrap-Kq3ruQo8",momentumBased:"momentumBased-Kq3ruQo8",menuBox:"menuBox-Kq3ruQo8",isHidden:"isHidden-Kq3ruQo8"}},35990:e=>{e.exports={button:"button-iLKiGOdQ",hovered:"hovered-iLKiGOdQ",disabled:"disabled-iLKiGOdQ",focused:"focused-iLKiGOdQ",active:"active-iLKiGOdQ",hidden:"hidden-iLKiGOdQ"}},93524:e=>{e.exports={linkItem:"linkItem-zMVwkifW"}},90854:e=>{e.exports={roundTabButton:"roundTabButton-JbssaNvk",disableFocusOutline:"disableFocusOutline-JbssaNvk",enableCursorPointer:"enableCursorPointer-JbssaNvk",large:"large-JbssaNvk",withStartIcon:"withStartIcon-JbssaNvk",iconOnly:"iconOnly-JbssaNvk",
withEndIcon:"withEndIcon-JbssaNvk",startIconWrap:"startIconWrap-JbssaNvk",endIconWrap:"endIconWrap-JbssaNvk",small:"small-JbssaNvk",xsmall:"xsmall-JbssaNvk",primary:"primary-JbssaNvk",selected:"selected-JbssaNvk",disableActiveStateStyles:"disableActiveStateStyles-JbssaNvk",ghost:"ghost-JbssaNvk",fake:"fake-JbssaNvk",caret:"caret-JbssaNvk",visuallyHidden:"visuallyHidden-JbssaNvk"}},76912:e=>{e.exports={scrollWrap:"scrollWrap-vgCB17hK",overflowScroll:"overflowScroll-vgCB17hK",roundTabs:"roundTabs-vgCB17hK",center:"center-vgCB17hK",overflowWrap:"overflowWrap-vgCB17hK",start:"start-vgCB17hK"}},49128:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},95604:(e,t,n)=>{"use strict";function o(e){let t=0;return e.isTop&&e.isLeft||(t+=1),e.isTop&&e.isRight||(t+=2),e.isBottom&&e.isLeft||(t+=8),e.isBottom&&e.isRight||(t+=4),t}n.d(t,{getGroupCellRemoveRoundBorders:()=>o})},67029:(e,t,n)=>{"use strict";n.d(t,{ControlSkeleton:()=>b,InputClasses:()=>f});var o=n(50959),r=n(97754),i=n(50151),s=n(38528),a=n(90186),l=n(86332),c=n(95604);var u=n(88276),d=n.n(u);function h(e){let t="";return 0!==e&&(1&e&&(t=r(t,d()["no-corner-top-left"])),2&e&&(t=r(t,d()["no-corner-top-right"])),4&e&&(t=r(t,d()["no-corner-bottom-right"])),8&e&&(t=r(t,d()["no-corner-bottom-left"]))),t}function p(e,t,n,o){const{removeRoundBorder:i,className:s,intent:a="default",borderStyle:l="thin",size:u,highlight:p,disabled:m,readonly:f,stretch:g,noReadonlyStyles:v,isFocused:b}=e,_=h(i??(0,c.getGroupCellRemoveRoundBorders)(n));return r(d().container,d()[`container-${u}`],d()[`intent-${a}`],d()[`border-${l}`],u&&d()[`size-${u}`],_,p&&d()["with-highlight"],m&&d().disabled,f&&!v&&d().readonly,b&&d().focused,g&&d().stretch,t&&d().grouped,!o&&d()["adjust-position"],n.isTop&&d()["first-row"],n.isLeft&&d()["first-col"],s)}function m(e,t,n){const{highlight:o,highlightRemoveRoundBorder:i}=e;if(!o)return d().highlight;const s=h(i??(0,c.getGroupCellRemoveRoundBorders)(t));return r(d().highlight,d().shown,d()[`size-${n}`],s)}const f={FontSizeMedium:(0,i.ensureDefined)(d()["font-size-medium"]),FontSizeLarge:(0,i.ensureDefined)(d()["font-size-large"])},g={passive:!1};function v(e,t){const{style:n,id:r,role:i,onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:f,onMouseUp:v,onKeyDown:b,onClick:_,tabIndex:y,startSlot:S,middleSlot:w,endSlot:C,onWheel:E,onWheelNoPassive:T=null,size:I,tag:M="span",type:D}=e,{isGrouped:x,cellState:N,disablePositionAdjustment:k=!1}=(0,o.useContext)(l.ControlGroupContext),R=function(e,t=null,n){const r=(0,o.useRef)(null),i=(0,o.useRef)(null),s=(0,o.useCallback)((()=>{if(null===r.current||null===i.current)return;const[e,t,n]=i.current;null!==t&&r.current.addEventListener(e,t,n)}),[]),a=(0,o.useCallback)((()=>{if(null===r.current||null===i.current)return;const[e,t,n]=i.current;null!==t&&r.current.removeEventListener(e,t,n)}),[]),l=(0,o.useCallback)((e=>{a(),r.current=e,s()}),[]);return(0,o.useEffect)((()=>(i.current=[e,t,n],s(),a)),[e,t,n]),l}("wheel",T,g),B=M;return o.createElement(B,{type:D,style:n,id:r,role:i,className:p(e,x,N,k),
tabIndex:y,ref:(0,s.useMergedRefs)([t,R]),onFocus:c,onBlur:u,onMouseOver:d,onMouseOut:h,onMouseDown:f,onMouseUp:v,onKeyDown:b,onClick:_,onWheel:E,...(0,a.filterDataProps)(e),...(0,a.filterAriaProps)(e)},S,w,C,o.createElement("span",{className:m(e,N,I)}))}v.displayName="ControlSkeleton";const b=o.forwardRef(v)},78274:(e,t,n)=>{"use strict";n.d(t,{AfterSlot:()=>u,EndSlot:()=>c,MiddleSlot:()=>l,StartSlot:()=>a});var o=n(50959),r=n(97754),i=n(73405),s=n.n(i);function a(e){const{className:t,interactive:n=!0,icon:i=!1,children:a}=e;return o.createElement("span",{className:r(s()["inner-slot"],n&&s().interactive,i&&s().icon,t)},a)}function l(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(s()["inner-slot"],s()["inner-middle-slot"],t)},n)}function c(e){const{className:t,interactive:n=!0,icon:i=!1,children:a}=e;return o.createElement("span",{className:r(s()["inner-slot"],n&&s().interactive,i&&s().icon,t)},a)}function u(e){const{className:t,children:n}=e;return o.createElement("span",{className:r(s()["after-slot"],t)},n)}},31261:(e,t,n)=>{"use strict";n.d(t,{InputControl:()=>b});var o=n(50959),r=n(97754),i=n(90186),s=n(47201),a=n(48907),l=n(38528),c=n(48027),u=n(29202),d=n(45812),h=n(67029),p=n(78274),m=n(25549),f=n.n(m);function g(e){return!(0,i.isAriaAttribute)(e)&&!(0,i.isDataAttribute)(e)}function v(e){const{id:t,title:n,role:s,tabIndex:a,placeholder:l,name:c,type:u,value:d,defaultValue:m,draggable:v,autoComplete:b,autoFocus:_,autoCapitalize:y,autoCorrect:S,maxLength:w,min:C,max:E,step:T,pattern:I,inputMode:M,onSelect:D,onFocus:x,onBlur:N,onKeyDown:k,onKeyUp:R,onKeyPress:B,onChange:L,onDragStart:O,size:A="small",className:P,inputClassName:F,disabled:z,readonly:W,containerTabIndex:H,startSlot:j,endSlot:V,reference:G,containerReference:U,onContainerFocus:K,...Z}=e,Q=(0,i.filterProps)(Z,g),q={...(0,i.filterAriaProps)(Z),...(0,i.filterDataProps)(Z),id:t,title:n,role:s,tabIndex:a,placeholder:l,name:c,type:u,value:d,defaultValue:m,draggable:v,autoComplete:b,autoFocus:_,autoCapitalize:y,autoCorrect:S,maxLength:w,min:C,max:E,step:T,pattern:I,inputMode:M,onSelect:D,onFocus:x,onBlur:N,onKeyDown:k,onKeyUp:R,onKeyPress:B,onChange:L,onDragStart:O};return o.createElement(h.ControlSkeleton,{...Q,disabled:z,readonly:W,tabIndex:H,className:r(f().container,P),size:A,ref:U,onFocus:K,startSlot:j,middleSlot:o.createElement(p.MiddleSlot,null,o.createElement("input",{...q,className:r(f().input,f()[`size-${A}`],F,j&&f()["with-start-slot"],V&&f()["with-end-slot"]),disabled:z,readOnly:W,ref:G})),endSlot:V})}function b(e){e=(0,c.useControl)(e);const{disabled:t,autoSelectOnFocus:n,tabIndex:r=0,onFocus:i,onBlur:h,reference:p,containerReference:m=null}=e,f=(0,o.useRef)(null),g=(0,o.useRef)(null),[b,_]=(0,u.useFocus)(),y=t?void 0:b?-1:r,S=t?void 0:b?r:-1,{isMouseDown:w,handleMouseDown:C,handleMouseUp:E}=(0,d.useIsMouseDown)(),T=(0,s.createSafeMulticastEventHandler)(_.onFocus,(function(e){n&&!w.current&&(0,a.selectAllContent)(e.currentTarget)}),i),I=(0,s.createSafeMulticastEventHandler)(_.onBlur,h),M=(0,o.useCallback)((e=>{
f.current=e,p&&("function"==typeof p&&p(e),"object"==typeof p&&(p.current=e))}),[f,p]);return o.createElement(v,{...e,isFocused:b,containerTabIndex:y,tabIndex:S,onContainerFocus:function(e){g.current===e.target&&null!==f.current&&f.current.focus()},onFocus:T,onBlur:I,reference:M,containerReference:(0,l.useMergedRefs)([g,m]),onMouseDown:C,onMouseUp:E})}},48027:(e,t,n)=>{"use strict";n.d(t,{useControl:()=>i});var o=n(47201),r=n(29202);function i(e){const{onFocus:t,onBlur:n,intent:i,highlight:s,disabled:a}=e,[l,c]=(0,r.useFocus)(void 0,a),u=(0,o.createSafeMulticastEventHandler)(a?void 0:c.onFocus,t),d=(0,o.createSafeMulticastEventHandler)(a?void 0:c.onBlur,n);return{...e,intent:i||(l?"primary":"default"),highlight:s??l,onFocus:u,onBlur:d}}},125:(e,t,n)=>{"use strict";n.d(t,{useForceUpdate:()=>r});var o=n(50959);const r=()=>{const[,e]=(0,o.useReducer)((e=>e+1),0);return e}},76974:(e,t,n)=>{"use strict";n.d(t,{useIsMounted:()=>r});var o=n(50959);const r=()=>{const e=(0,o.useRef)(!1);return(0,o.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}},45812:(e,t,n)=>{"use strict";n.d(t,{useIsMouseDown:()=>r});var o=n(50959);function r(){const e=(0,o.useRef)(!1),t=(0,o.useCallback)((()=>{e.current=!0}),[e]),n=(0,o.useCallback)((()=>{e.current=!1}),[e]);return{isMouseDown:e,handleMouseDown:t,handleMouseUp:n}}},43010:(e,t,n)=>{"use strict";n.d(t,{useIsomorphicLayoutEffect:()=>r});var o=n(50959);function r(e,t){("undefined"==typeof window?o.useEffect:o.useLayoutEffect)(e,t)}},36383:(e,t,n)=>{"use strict";n.d(t,{useOutsideEvent:()=>s});var o=n(50959),r=n(43010),i=n(27267);function s(e){const{click:t,mouseDown:n,touchEnd:s,touchStart:a,handler:l,reference:c}=e,u=(0,o.useRef)(null),d=(0,o.useRef)("undefined"==typeof window?0:new window.CustomEvent("timestamp").timeStamp);return(0,r.useIsomorphicLayoutEffect)((()=>{const e={click:t,mouseDown:n,touchEnd:s,touchStart:a},o=c?c.current:u.current;return(0,i.addOutsideEventListener)(d.current,o,l,document,e)}),[t,n,s,a,l]),c||u}},9745:(e,t,n)=>{"use strict";n.d(t,{Icon:()=>r});var o=n(50959);const r=o.forwardRef(((e,t)=>{const{icon:n="",title:r,ariaLabel:i,ariaLabelledby:s,ariaHidden:a,...l}=e,c=!!(r||i||s);return o.createElement("span",{role:"img",...l,ref:t,"aria-label":i,"aria-labelledby":s,"aria-hidden":a||!c,title:r,dangerouslySetInnerHTML:{__html:n}})}))},83021:(e,t,n)=>{"use strict";n.d(t,{SubmenuContext:()=>r,SubmenuHandler:()=>i});var o=n(50959);const r=o.createContext(null);function i(e){const[t,n]=(0,o.useState)(null),i=(0,o.useRef)(null),s=(0,o.useRef)(new Map);return(0,o.useEffect)((()=>()=>{null!==i.current&&clearTimeout(i.current)}),[]),o.createElement(r.Provider,{value:{current:t,setCurrent:function(e){null!==i.current&&(clearTimeout(i.current),i.current=null);null===t?n(e):i.current=setTimeout((()=>{i.current=null,n(e)}),100)},registerSubmenu:function(e,t){return s.current.set(e,t),()=>{s.current.delete(e)}},isSubmenuNode:function(e){return Array.from(s.current.values()).some((t=>t(e)))}}},e.children)}},99663:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>r,SlotContext:()=>i})
;var o=n(50959);class r extends o.Component{shouldComponentUpdate(){return!1}render(){return o.createElement("div",{style:{position:"fixed",zIndex:150,left:0,top:0},ref:this.props.reference})}}const i=o.createContext(null)},47930:(e,t,n)=>{"use strict";n.d(t,{formatTime:()=>h,isValidTimeOptionsDateStyle:()=>d,isValidTimeOptionsRange:()=>u});const o={calendar:"gregory",numberingSystem:"latn",hour12:!1},r={year:"numeric",month:"short",day:"numeric"},i={year:"numeric",month:"2-digit",day:"2-digit"},s={hour:"2-digit",minute:"2-digit",second:"2-digit"},a={timeZoneName:"shortOffset",weekday:"short"},l={year:0,month:1,day:2,hour:3,minute:4,second:5};const c=["year","month","day","hour","minute","second"];function u(e){return c.includes(e)}function d(e){return"numeric"===e||"short"===e}function h(e,t,n="year",c="day",u){const d=function(e="year",t="day",n={}){[e,t]=l[t]>l[e]?[e,t]:[t,e];const c={..."numeric"===n.dateStyle?i:r,...s},u=n.fractionalSecondDigits,d={...o,fractionalSecondDigits:void 0===u?void 0:Math.floor(Math.min(Math.max(1,u),3)),timeZone:n.timeZone,weekday:n.weekday?a.weekday:void 0,timeZoneName:n.timeZoneName?a.timeZoneName:void 0};return Object.keys(c).forEach((n=>{l[n]>=l[e]&&l[n]<=l[t]&&(d[n]=c[n])})),d}(n,c,u),h=new Intl.DateTimeFormat(t,d),p=new Date(e);return h.format(p)}},48907:(e,t,n)=>{"use strict";function o(e){null!==e&&e.setSelectionRange(0,e.value.length)}n.d(t,{selectAllContent:()=>o})},67961:(e,t,n)=>{"use strict";n.d(t,{OverlapManager:()=>s,getRootOverlapManager:()=>l});var o=n(50151),r=n(34811);class i{constructor(){this._storage=[]}add(e){this._storage.push(e)}remove(e){this._storage=this._storage.filter((t=>e!==t))}has(e){return this._storage.includes(e)}getItems(){return this._storage}}class s{constructor(e=document){this._storage=new i,this._windows=new Map,this._index=0,this._document=e,this._container=e.createDocumentFragment()}setContainer(e){const t=this._container,n=null===e?this._document.createDocumentFragment():e;!function(e,t){Array.from(e.childNodes).forEach((e=>{e.nodeType===Node.ELEMENT_NODE&&t.appendChild(e)}))}(t,n),this._container=n}registerWindow(e){this._storage.has(e)||this._storage.add(e)}ensureWindow(e,t={position:"fixed",direction:"normal"}){const n=this._windows.get(e);if(void 0!==n)return n;this.registerWindow(e);const o=this._document.createElement("div");if(o.style.position=t.position,o.style.zIndex=this._index.toString(),o.dataset.id=e,void 0!==t.index){const e=this._container.childNodes.length;if(t.index>=e)this._container.appendChild(o);else if(t.index<=0)this._container.insertBefore(o,this._container.firstChild);else{const e=this._container.childNodes[t.index];this._container.insertBefore(o,e)}}else"reverse"===t.direction?this._container.insertBefore(o,this._container.firstChild):this._container.appendChild(o);return this._windows.set(e,o),++this._index,o}unregisterWindow(e){this._storage.remove(e);const t=this._windows.get(e);void 0!==t&&(null!==t.parentElement&&t.parentElement.removeChild(t),this._windows.delete(e))}getZindex(e){const t=this.ensureWindow(e)
;return parseInt(t.style.zIndex||"0")}moveLastWindowToTop(){const e=this._storage.getItems(),t=e[e.length-1];t&&this.moveToTop(t)}moveToTop(e){if(this.getZindex(e)!==this._index){const t=this.ensureWindow(e);this._windows.forEach(((e,n)=>{e.hasAttribute(r.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(r.FOCUS_TRAP_DATA_ATTRIBUTE,e===t?"true":"false")})),t.style.zIndex=(++this._index).toString()}}removeWindow(e){this.unregisterWindow(e)}}const a=new WeakMap;function l(e=document){const t=e.getElementById("overlap-manager-root");if(null!==t)return(0,o.ensureDefined)(a.get(t));{const t=new s(e),n=function(e){const t=e.createElement("div");return t.style.position="absolute",t.style.zIndex=150..toString(),t.style.top="0px",t.style.left="0px",t.id="overlap-manager-root",t}(e);return a.set(n,t),t.setContainer(n),e.body.appendChild(n),t}}var c;!function(e){e[e.BaseZindex=150]="BaseZindex"}(c||(c={}))},99054:(e,t,n)=>{"use strict";n.d(t,{setFixedBodyState:()=>c});const o=(()=>{let e;return()=>{if(void 0===e){const t=document.createElement("div"),n=t.style;n.visibility="hidden",n.width="100px",n.msOverflowStyle="scrollbar",document.body.appendChild(t);const o=t.offsetWidth;t.style.overflow="scroll";const r=document.createElement("div");r.style.width="100%",t.appendChild(r);const i=r.offsetWidth;t.parentNode?.removeChild(t),e=o-i}return e}})();function r(e,t,n){null!==e&&e.style.setProperty(t,n)}function i(e,t){return getComputedStyle(e,null).getPropertyValue(t)}function s(e,t){return parseInt(i(e,t))}let a=0,l=!1;function c(e){const{body:t}=document,n=t.querySelector(".widgetbar-wrap");if(e&&1==++a){const e=i(t,"overflow"),a=s(t,"padding-right");"hidden"!==e.toLowerCase()&&t.scrollHeight>t.offsetHeight&&(r(n,"right",`${o()}px`),t.style.paddingRight=`${a+o()}px`,l=!0),t.classList.add("i-no-scroll")}else if(!e&&a>0&&0==--a&&(t.classList.remove("i-no-scroll"),l)){r(n,"right","0px");let e=0;0,t.scrollHeight<=t.clientHeight&&(e-=o()),t.style.paddingRight=(e<0?0:e)+"px",l=!1}}},58653:(e,t,n)=>{"use strict";n.d(t,{getLocaleIso:()=>s});var o=n(50151)
;const r=JSON.parse('{"ar_AE":{"language":"ar","language_name":"العربية","flag":"sa","geoip_code":"sa","countries_with_this_language":["ae","bh","dj","dz","eg","er","iq","jo","km","kw","lb","ly","ma","mr","om","qa","sa","sd","so","sy","td","tn","ye"],"priority":500,"dir":"rtl","iso":"ar","iso_639_3":"arb","show_on_widgets":true,"global_name":"Arabic"},"br":{"language":"pt","language_name":"Português","flag":"br","geoip_code":"br","priority":650,"iso":"pt","iso_639_3":"por","show_on_widgets":true,"global_name":"Portuguese"},"ca_ES":{"language":"ca_ES","language_name":"Català","flag":"es","geoip_code":"es","priority":745,"iso":"ca","iso_639_3":"cat","disabled":true,"show_on_widgets":true,"global_name":"Catalan"},"cs":{"language":"cs","language_name":"Czech","flag":"cz","geoip_code":"cz","priority":718,"iso":"cs","iso_639_3":"ces","show_on_widgets":true,"global_name":"Czech","is_in_european_union":true,"isBattle":true},"de_DE":{"language":"de","language_name":"Deutsch","flag":"de","geoip_code":"de","countries_with_this_language":["at","ch"],"priority":756,"iso":"de","iso_639_3":"deu","show_on_widgets":true,"global_name":"German","is_in_european_union":true},"en":{"language":"en","language_name":"English","flag":"us","geoip_code":"us","priority":1000,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"English","is_only_recommended_tw_autorepost":true},"es":{"language":"es","language_name":"Español","flag":"es","geoip_code":"es","countries_with_this_language":["mx","ar","ve","cl","co","pe","uy","py","cr","gt","c","bo","pa","pr"],"priority":744,"iso":"es","iso_639_3":"spa","show_on_widgets":true,"global_name":"Spanish","is_in_european_union":true},"fr":{"language":"fr","language_name":"Français","flag":"fr","geoip_code":"fr","priority":750,"iso":"fr","iso_639_3":"fra","show_on_widgets":true,"global_name":"French","is_in_european_union":true},"he_IL":{"language":"he_IL","language_name":"עברית","flag":"il","geoip_code":"il","priority":490,"dir":"rtl","iso":"he","iso_639_3":"heb","show_on_widgets":true,"global_name":"Israeli"},"hu_HU":{"language":"hu_HU","language_name":"Magyar","flag":"hu","geoip_code":"hu","priority":724,"iso":"hu","iso_639_3":"hun","show_on_widgets":true,"global_name":"Hungarian","is_in_european_union":true,"disabled":true},"id":{"language":"id_ID","language_name":"Bahasa Indonesia","flag":"id","geoip_code":"id","priority":648,"iso":"id","iso_639_3":"ind","show_on_widgets":true,"global_name":"Indonesian"},"in":{"language":"en","language_name":"English ‎(India)‎","flag":"in","geoip_code":"in","priority":800,"iso":"en","iso_639_3":"eng","show_on_widgets":true,"global_name":"Indian"},"it":{"language":"it","language_name":"Italiano","flag":"it","geoip_code":"it","priority":737,"iso":"it","iso_639_3":"ita","show_on_widgets":true,"global_name":"Italian","is_in_european_union":true},"ja":{"language":"ja","language_name":"日本語","flag":"jp","geoip_code":"jp","priority":600,"iso":"ja","iso_639_3":"jpn","show_on_widgets":true,"global_name":"Japanese"},"kr":{"language":"ko","language_name":"한국어","flag":"kr","geoip_code":"kr","priority":550,"iso":"ko","iso_639_3":"kor","show_on_widgets":true,"global_name":"Korean"},"ms_MY":{"language":"ms_MY","language_name":"Bahasa Melayu","flag":"my","geoip_code":"my","priority":647,"iso":"ms","iso_639_3":"zlm","show_on_widgets":true,"global_name":"Malaysian"},"pl":{"language":"pl","language_name":"Polski","flag":"pl","geoip_code":"pl","priority":725,"iso":"pl","iso_639_3":"pol","show_on_widgets":true,"global_name":"Polish","is_in_european_union":true},"ru":{"language":"ru","language_name":"Русский","flag":"ru","geoip_code":"ru","countries_with_this_language":["am","by","kg","kz","md","tj","tm","uz"],"priority":700,"iso":"ru","iso_639_3":"rus","show_on_widgets":true,"global_name":"Russian","is_only_recommended_tw_autorepost":true},"sv_SE":{"language":"sv","language_name":"Svenska","flag":"se","geoip_code":"se","priority":723,"iso":"sv","iso_639_3":"swe","show_on_widgets":true,"global_name":"Swedish","is_in_european_union":true,"disabled":true},"th_TH":{"language":"th","language_name":"ภาษาไทย","flag":"th","geoip_code":"th","priority":646,"iso":"th","iso_639_3":"tha","show_on_widgets":true,"global_name":"Thai"},"tr":{"language":"tr","language_name":"Türkçe","flag":"tr","geoip_code":"tr","priority":713,"iso":"tr","iso_639_3":"tur","show_on_widgets":true,"global_name":"Turkish","is_only_recommended_tw_autorepost":true},"vi_VN":{"language":"vi","language_name":"Tiếng Việt","flag":"vn","geoip_code":"vn","priority":645,"iso":"vi","iso_639_3":"vie","show_on_widgets":true,"global_name":"Vietnamese"},"zh_CN":{"language":"zh","language_name":"简体中文","flag":"cn","geoip_code":"cn","countries_with_this_language":["zh"],"priority":537,"iso":"zh-Hans","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Chinese"},"zh_TW":{"language":"zh_TW","language_name":"繁體中文","flag":"tw","geoip_code":"tw","countries_with_this_language":["hk"],"priority":536,"iso":"zh-Hant","iso_639_3":"cmn","show_on_widgets":true,"global_name":"Taiwanese"},"el":{"language":"el","language_name":"Greek","flag":"gr","geoip_code":"gr","priority":625,"iso":"el","iso_639_3":"ell","global_name":"Greek","is_in_european_union":true,"isBattle":true},"nl_NL":{"language":"nl_NL","language_name":"Dutch","flag":"nl","geoip_code":"nl","priority":731,"iso":"nl","iso_639_3":"nld","global_name":"Dutch","is_in_european_union":true,"isBattle":true},"ro":{"language":"ro","language_name":"Romanian","flag":"ro","geoip_code":"ro","priority":707,"iso":"ro","iso_639_3":"ron","global_name":"Romanian","is_in_european_union":true,"isBattle":true}}'),i=function(){
const e=document.querySelectorAll("link[rel~=link-locale][data-locale]");if(0===e.length)return r;const t={};return e.forEach((e=>{const n=(0,o.ensureNotNull)(e.getAttribute("data-locale"));t[n]={...r[n],href:e.href}})),t}();function s(e){return e=e||window.locale,i[e]?.iso}},64530:(e,t,n)=>{"use strict";n.d(t,{DialogContentItem:()=>h});var o=n(50959),r=n(97754),i=n.n(r),s=n(49483),a=n(36189),l=n(96040);function c(e){const{url:t,...n}=e;return t?o.createElement("a",{...n,href:t}):o.createElement("div",{...n})}var u=n(60925),d=n(8255);function h(e){const{title:t,subtitle:n,removeBtnLabel:r,onClick:h,onClickFavorite:m,onClickRemove:f,isActive:g,isFavorite:v,isFocused:b,isMobile:_=!1,showFavorite:y=!0,focusedActionIndex:S,className:w,tabIndex:C,index:E,focusVisible:T,getElementId:I,...M}=e,D=[y&&m?"favorite":null,"remove"].filter((e=>null!==e)),x={favorite:D.indexOf("favorite"),remove:D.indexOf("remove")},N=(0,o.useId)();return o.createElement(c,{...M,role:"row",id:I?.(E),className:i()(d.itemRow,g&&d.active,_&&d.mobile,b&&T&&null===S&&d.focused,w),tabIndex:C,onClick:p.bind(null,h),"data-role":"list-item","data-active":g,"aria-labelledby":N},y&&m&&o.createElement(a.FavoriteButton,{id:I?.(E,x.favorite),role:"cell",className:i()(d.favoriteButton,d.actionButton,v&&d.favorite,s.CheckMobile.any()&&d.mobile,b&&S===x.favorite&&d.focused,b&&T&&d.showOnFocus),isActive:g,isFilled:v,onClick:p.bind(null,m),"data-name":"list-item-favorite-button","data-role":"list-item-action","data-favorite":v}),o.createElement("div",{id:N,role:"cell",className:i()(d.itemInfo,!y&&d.itemInfoWithPadding)},o.createElement("div",{className:i()(d.title,g&&d.active,_&&d.mobile),"data-name":"list-item-title"},t),o.createElement("div",{className:i()(d.details,g&&d.active,_&&d.mobile)},n)),o.createElement(l.RemoveButton,{id:I?.(E,x.remove),role:"cell",className:i()(d.removeButton,d.actionButton,b&&S===x.remove&&d.focused,b&&T&&d.showOnFocus),isActive:g,onClick:p.bind(null,f),"data-name":"list-item-remove-button","data-role":"list-item-action",title:r,icon:u}))}function p(e,t){t.defaultPrevented||(t.preventDefault(),e(t))}},3085:(e,t,n)=>{"use strict";n.d(t,{OverlayScrollContainer:()=>v});var o=n(50959),r=n(97754),i=n.n(r),s=n(63273),a=n(50151),l=n(9859);const c=n(51810);var u;!function(e){e[e.Vertical=0]="Vertical",e[e.Horizontal=1]="Horizontal",e[e.HorizontalRtl=2]="HorizontalRtl"}(u||(u={}));const d={0:{isHorizontal:!1,isNegative:!1,sizePropName:"height",minSizePropName:"minHeight",startPointPropName:"top",currentMousePointPropName:"clientY",progressBarTransform:"translateY"},1:{isHorizontal:!0,isNegative:!1,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"left",currentMousePointPropName:"clientX",progressBarTransform:"translateX"},2:{isHorizontal:!0,isNegative:!0,sizePropName:"width",minSizePropName:"minWidth",startPointPropName:"right",currentMousePointPropName:"clientX",progressBarTransform:"translateX"}},h=40;function p(e){
const{size:t,scrollSize:n,clientSize:r,scrollProgress:s,onScrollProgressChange:u,scrollMode:p,theme:m=c,onDragStart:f,onDragEnd:g,minBarSize:v=h}=e,b=(0,o.useRef)(null),_=(0,o.useRef)(null),[y,S]=(0,o.useState)(!1),w=(0,o.useRef)(0),{isHorizontal:C,isNegative:E,sizePropName:T,minSizePropName:I,startPointPropName:M,currentMousePointPropName:D,progressBarTransform:x}=d[p];(0,o.useEffect)((()=>{const e=(0,a.ensureNotNull)(b.current).ownerDocument;return y?(f&&f(),e&&(e.addEventListener("mousemove",F),e.addEventListener("mouseup",z))):g&&g(),()=>{e&&(e.removeEventListener("mousemove",F),e.removeEventListener("mouseup",z))}}),[y]);const N=t/n||0,k=r*N||0,R=Math.max(k,v),B=(t-R)/(t-k),L=n-t,O=E?-L:0,A=E?0:L,P=H((0,l.clamp)(s,O,A))||0;return o.createElement("div",{ref:b,className:i()(m.wrap,C&&m["wrap--horizontal"]),style:{[T]:t},onMouseDown:function(e){if(e.isDefaultPrevented())return;e.preventDefault();const t=W(e.nativeEvent,(0,a.ensureNotNull)(b.current)),n=Math.sign(t),o=(0,a.ensureNotNull)(_.current).getBoundingClientRect();w.current=n*o[T]/2;let r=Math.abs(t)-Math.abs(w.current);const i=H(L);r<0?(r=0,w.current=t):r>i&&(r=i,w.current=t-n*i);u(j(n*r)),S(!0)}},o.createElement("div",{ref:_,className:i()(m.bar,C&&m["bar--horizontal"]),style:{[I]:v,[T]:R,transform:`${x}(${P}px)`},onMouseDown:function(e){e.preventDefault(),w.current=W(e.nativeEvent,(0,a.ensureNotNull)(_.current)),S(!0)}},o.createElement("div",{className:i()(m.barInner,C&&m["barInner--horizontal"])})));function F(e){const t=W(e,(0,a.ensureNotNull)(b.current))-w.current;u(j(t))}function z(){S(!1)}function W(e,t){const n=t.getBoundingClientRect()[M];return e[D]-n}function H(e){return e*N*B}function j(e){return e/N/B}}var m=n(70412),f=n(18561);const g=8;function v(e){const{reference:t,className:n,containerHeight:i=0,containerWidth:a=0,contentHeight:l=0,contentWidth:c=0,scrollPosTop:u=0,scrollPosLeft:d=0,onVerticalChange:h,onHorizontalChange:v,visible:b}=e,[_,y]=(0,m.useHover)(),[S,w]=(0,o.useState)(!1),C=i<l,E=a<c,T=C&&E?g:0;return o.createElement("div",{...y,ref:t,className:r(n,f.scrollWrap),style:{visibility:b||_||S?"visible":"hidden"}},C&&o.createElement(p,{size:i-T,scrollSize:l-T,clientSize:i-T,scrollProgress:u,onScrollProgressChange:function(e){h&&h(e)},onDragStart:I,onDragEnd:M,scrollMode:0}),E&&o.createElement(p,{size:a-T,scrollSize:c-T,clientSize:a-T,scrollProgress:d,onScrollProgressChange:function(e){v&&v(e)},onDragStart:I,onDragEnd:M,scrollMode:(0,s.isRtl)()?2:1}));function I(){w(!0)}function M(){w(!1)}}},74670:(e,t,n)=>{"use strict";n.d(t,{useActiveDescendant:()=>i});var o=n(50959),r=n(39416);function i(e,t=[]){const[n,i]=(0,o.useState)(!1),s=(0,r.useFunctionalRefObject)(e);return(0,o.useLayoutEffect)((()=>{const e=s.current;if(null===e)return;const t=e=>{switch(e.type){case"active-descendant-focus":i(!0);break;case"active-descendant-blur":i(!1)}};return e.addEventListener("active-descendant-focus",t),e.addEventListener("active-descendant-blur",t),()=>{e.removeEventListener("active-descendant-focus",t),
e.removeEventListener("active-descendant-blur",t)}}),t),[s,n]}},50238:(e,t,n)=>{"use strict";n.d(t,{useRovingTabindexElement:()=>i});var o=n(50959),r=n(39416);function i(e,t=[]){const[n,i]=(0,o.useState)(!1),s=(0,r.useFunctionalRefObject)(e);return(0,o.useLayoutEffect)((()=>{const e=s.current;if(null===e)return;const t=e=>{switch(e.type){case"roving-tabindex:main-element":i(!0);break;case"roving-tabindex:secondary-element":i(!1)}};return e.addEventListener("roving-tabindex:main-element",t),e.addEventListener("roving-tabindex:secondary-element",t),()=>{e.removeEventListener("roving-tabindex:main-element",t),e.removeEventListener("roving-tabindex:secondary-element",t)}}),t),[s,n?0:-1]}},37404:(e,t,n)=>{"use strict";n.d(t,{showManageDrawingsDialog:()=>r});let o=null;function r(e){return Promise.all([n.e(4166),n.e(8692),n.e(3693),n.e(7159),n.e(1702)]).then(n.bind(n,41662)).then((t=>{const n=new(0,t.ManageDrawingsDialogRenderer)(e);return null!==o&&o.hide(),n.show(),o=n,n}))}},11386:(e,t,n)=>{"use strict";n.d(t,{ManageDrawings:()=>k});var o=n(50959),r=n(20057),i=n(97754),s=n.n(i),a=n(9745),l=n(11542),c=n(45126),u=n(64147),d=n(9343),h=n(32755),p=n(64530),m=n(37265),f=n(63932),g=n(47308);const v=l.t(null,void 0,n(92931)),b=l.t(null,void 0,n(41870)),_=l.t(null,void 0,n(80996));function y(e){const{sharingMode:t,onTabClick:n}=e,r=o.useMemo((()=>[{children:v,id:"2"},{children:b,id:"1"},{children:_,id:"0"}]),[]);return o.createElement(g.RoundButtonTabs,{id:"manage-drawings-tabs",isActive:e=>parseInt(e.id)===t,onActivate:function(e){n(parseInt(e.id))},overflowBehaviour:"scroll",items:r})}var S=n(29540),w=n(48452);const C=(0,d.getLogger)("Chart.ManageDrawings"),E=new Map;function T(e){let t=E.get(e);return void 0===t&&(t=new u.WatchedValue([]),E.set(e,t)),t}const I=new c.TranslatedString("remove all line tools for {symbol}",l.t(null,void 0,n(58407))),M=e=>l.t(null,{plural:"{drawingsCount} drawings",count:e},n(90755)).format({drawingsCount:e.toString()}),D=l.t(null,void 0,n(8182)),x=l.t(null,void 0,n(84212));function N(e){const[t,n]=o.useState(null),[i,s]=o.useState(null),[a,l]=o.useState(null),[c,u]=(o.useRef(null),o.useState([]));return o.useEffect((()=>{let t;const o=()=>{t&&s(t.mainSeries().proSymbol())};return e.withModel(null,(()=>{t=e.model(),n(t),o(),t.mainSeries().symbolResolved().subscribe(null,o)})),()=>{t?.mainSeries().symbolResolved().unsubscribe(null,o),n(null)}}),[e]),o.useEffect((()=>{if(null!==t){const e={},n=(0,r.default)(g,250,{leading:!1});return g(),t.model().dataSourceCollectionChanged().subscribe(e,n),()=>{t.model().dataSourceCollectionChanged().unsubscribe(e,n)}}}),[t]),o.useEffect((()=>{if(null!==t){const e=T(t.model().id()).spawn();return u([...e.value()]),e.subscribe((()=>u([...e.value()]))),()=>e?.destroy()}}),[t]),o.useMemo((()=>({currentSymbol:i,symbolDrawingsMaps:a,removeSymbolDrawings:d,changeSymbol:p,hiddenSymbols:c})),[i,a,d,p,c]);async function d(e,n){if(t&&a){const o=a[n].get(e);if(o){const n=Array.from(o).map((e=>t.model().dataSourceForId(e))).filter(m.notNull)
;n.length>0&&t.removeSources(n,!1,I.format({symbol:e}));const r=T(t.model().id());r.setValue([...r.value(),e]);try{await g()}catch(e){C.logError(`Error removing line tools: ${e}`)}r.setValue(r.value().filter((t=>t!==e)))}}}function p(n){e.setSymbol(n),null!==t&&s(n)}async function f(e){const t=function(e){const t=[new Map,new Map,new Map];return e.forEach((e=>{if((0,h.isLineTool)(e)&&e.showInObjectTree()){const n=e.symbol()??"",o=e.sharingMode().value();t[o].set(n,(t[o].get(n)||new Set).add(e.id()))}})),t}(e),n=await async function(){const e=[new Map,new Map,new Map];return e}();return n.forEach(((e,n)=>{const o=t[n];e.forEach(((e,t)=>{const n=o.get(t)||new Set;e.forEach((e=>n.add(e))),o.set(t,n)}))})),t}async function g(){null!==t&&l(await f(t.dataSources()))}}function k(e){const{isMobile:t,isSmallWidth:n,chartWidget:r,onClose:i,onInitialized:l}=e,{currentSymbol:c,symbolDrawingsMaps:u,removeSymbolDrawings:d,changeSymbol:h,hiddenSymbols:m}=N(r),[g,v]=o.useState(null),[b,_]=o.useMemo((()=>{if(null!==c&&null!==u){const e=[];let t=g;if(null===t)for(t=2;t>0&&!((u[t].get(c)?.size||0)>0);)t--;return u[t].forEach(((t,n)=>{m.includes(n)||e.push({symbol:n,drawingsCount:t.size,onRemove:()=>function(e){d(e,_)}(n),onClick:()=>function(e){""!==e&&(h(e),i?.())}(n)})})),e.sort(((e,t)=>e.drawingsCount===t.drawingsCount?e.symbol.localeCompare(t.symbol):e.drawingsCount>t.drawingsCount?-1:1)),[e,t]}return[[],0]}),[c,g,u,m]);return o.useEffect((()=>{null!==u&&l?.()}),[u]),o.createElement(o.Fragment,null,o.createElement("div",{className:s()(w.container,(n||t)&&w.mobile)},o.createElement(y,{sharingMode:_,onTabClick:v})),0===b.length?null===u?o.createElement(f.Spinner,{className:w.spinner}):o.createElement("div",{className:w.empty},o.createElement(a.Icon,{className:w.image,icon:S}),o.createElement("span",null,x)):b.map((({symbol:e,drawingsCount:n,onRemove:r,onClick:i},s)=>o.createElement(p.DialogContentItem,{key:e,index:s,title:e,subtitle:M(n),removeBtnLabel:D,isActive:e===c,isMobile:t,onClick:i,onClickRemove:r,showFavorite:!1,className:w.item}))))}},34489:(e,t,n)=>{"use strict";n.r(t),n.d(t,{ObjectTreeDialogRenderer:()=>Cr});var o=n(50959);async function r(e,t,n){let o;for(let r=0;r<t;++r)try{return await e(o)}catch(e){o=e,await n(r)}throw o}async function i(e,t){return r(e,t,(()=>Promise.resolve()))}var s=n(9343);const a=(0,s.getLogger)("DataSourcesIcons");let l=null;function c(){const e=n.c[98626];return e?Promise.resolve(e.exports.lineToolsIcons):n.e(1890).then(n.bind(n,98626)).then((e=>e.lineToolsIcons))}function u(){const e=n.c[23076];return e?Promise.resolve(e.exports.SERIES_ICONS):n.e(9685).then(n.bind(n,23076)).then((e=>e.SERIES_ICONS))}let d=null;function h(){return null===d&&(d=function(){const e=i(c,2).then((e=>e)).catch((e=>(a.logWarn(e),{}))),t=i(u,2).then((e=>e)).catch((e=>(a.logWarn(e),{})));return Promise.all([e,t])}()),d.then((e=>(l={linetool:e[0],series:e[1]},l)))}var p=n(11542),m=n(97754),f=n.n(m),g=n(125),v=n(76974),b=n(41590),_=n(37558),y=n(90692),S=n(79418),w=n(24437),C=n(11386),E=(n(37404),
n(29885)),T=n(32563),I=n(68335),M=n(50151),D=n(9745),x=n(10838),N=n(88811),k=n(36947);const R=o.createContext(null);var B=n(45827),L=n(6190),O=n(19291),A=n(36296),P=n(74059),F=n(80465),z=n(43478),W=n(51768);function H(e){const{hideTitle:t}=e,{viewModel:r}=(0,M.ensureNotNull)((0,o.useContext)(R)),i=(0,k.useForceUpdate)(),s=r.selection();(0,o.useEffect)((()=>{const e={};return r.onChange().subscribe(e,(()=>i())),()=>{r.onChange().unsubscribeAll(e)}}),[r]),(0,o.useEffect)((()=>{const e={};return s.onChange().subscribe(e,(()=>i())),()=>{s.onChange().unsubscribeAll(e)}}),[s]),(0,o.useEffect)((()=>{(0,O.updateTabIndexes)()}),[]);const a=!r.canSelectionBeUnmerged(),l=r.isSelectionCopiable(),c=r.isSelectionCloneable(),u=!l&&!c,d=r.canSelectionBeGrouped(),h=!1;return o.createElement(L.Toolbar,{orientation:"horizontal",className:z.toolbar},!t&&o.createElement("div",{className:m(z.title,z.withoutIcon)},p.t(null,void 0,n(88616)),h),o.createElement("div",{className:z.buttons},o.createElement(B.ToolbarIconButton,{className:m(z.button,!d&&z.disabled),icon:F,onClick:function(){r.createGroupFromSelection()},isDisabled:!d,tooltip:p.t(null,void 0,n(83390)),"data-name":"group-button"}),o.createElement(N.ToolbarMenuButton,{className:m(z.button,u&&z.disabled),isDisabled:u,content:o.createElement(D.Icon,{icon:A}),tooltip:p.t(null,void 0,n(92389)),arrow:!1,isShowTooltip:!0,"data-name":"copy-clone-button"},l&&o.createElement(x.AccessibleMenuItem,{"data-name":"copy",label:p.t(null,void 0,n(49680)),onClick:function(){r.copySelection()}}),c&&o.createElement(x.AccessibleMenuItem,{"data-name":"clone",label:p.t(null,void 0,n(12537)),onClick:function(){r.cloneSelection()}})),o.createElement(N.ToolbarMenuButton,{className:m(z.button,a&&z.disabled),isDisabled:a,content:o.createElement(D.Icon,{icon:P}),tooltip:p.t(null,void 0,n(35049)),arrow:!1,isShowTooltip:!0,"data-name":"move-to-button"},o.createElement(x.AccessibleMenuItem,{"aria-label":p.t(null,void 0,n(15512)),"data-name":"new-pane-above",label:p.t(null,void 0,n(15512)),onClick:function(){r.unmergeSelectionUp()}}),o.createElement(x.AccessibleMenuItem,{"aria-label":p.t(null,void 0,n(52160)),"data-name":"new-pane-below",label:p.t(null,void 0,n(52160)),onClick:function(){r.unmergeSelectionDown()}})),t&&o.createElement(o.Fragment,null,o.createElement("div",{className:z.spacing}),o.createElement(B.ToolbarIconButton,{className:z.button,icon:manageDrawingsIcon,tooltip:p.t(null,void 0,n(81031)),"data-name":"manage-drawings-button",onClick:f}))));function f(){h}}var j=n(46212),V=n(43766),G=n(10170),U=n(7809),K=n(94212);const Z=(Q="OBJECT_TREE",e=>Q+"__"+e);var Q;const q=Z("SET_NODES"),Y=Z("SYNC_NODES"),$=Z("UPDATE_NODE"),J=Z("UPDATE_NODES"),X=Z("RESET_TREE"),ee=Z("SET_SELECTED_IDS"),te=Z("DROP_SELECTION"),ne=Z("SELECT_PREVIOUS"),oe=Z("SELECT_NEXT"),re=Z("MULTI_SELECT_PREVIOUS"),ie=Z("MULTI_SELECT_NEXT"),se=Z("PROCESS_DROP_TARGET"),ae=Z("UPDATE_DROP_TARGET"),le=Z("HIDE_DROP_TARGET"),ce=Z("START_MULTI_SELECT"),ue=Z("STOP_MULTI_SELECT"),de=(Z("REMOVE_NODE"),
Z("SET_FOCUSED_NODE")),he=Z("SCROLL_TO_ID"),pe=Z("SET_IS_SELECTED"),me=Z("SET_IS_EXPANDED"),fe=Z("SET_DISABLED_NODES"),ge=Z("MOVE_NODES"),ve=(Z("START_DRAG"),Z("END_DRAG")),be=()=>({type:ne}),_e=()=>({type:oe}),ye=()=>({type:re}),Se=()=>({type:ie}),we=(e,t,n,o,r)=>({type:se,dropTarget:e,dropType:t,isHoveredLeft:n,boundBox:o,isLastChild:r}),Ce=()=>({type:te}),Ee=e=>({type:ee,ids:e}),Te=(e,t,n)=>({type:ge,ids:e,targetId:t,dropType:n}),Ie=()=>({type:ce}),Me=()=>({type:ue}),De=e=>({type:de,nodeId:e}),xe=e=>({type:he,nodeId:e}),Ne=(e,t,n=0)=>({type:pe,nodeId:e,isSelected:t,mode:n}),ke=(e,t)=>({type:me,nodeId:e,isExpanded:t}),Re=e=>({type:fe,ids:e}),Be=()=>({type:ve});var Le=n(77145);const Oe=e=>e.nodes,Ae=e=>e.selection,Pe=e=>e.dropTarget,Fe=e=>e.expanded,ze=e=>e.scrollToId,We=(e,t)=>t,He=(0,Le.createSelector)([Oe,We],((e,t)=>e[t])),je=(0,Le.createSelector)([Ae,We],((e,t)=>e.ids.includes(t))),Ve=(0,Le.createSelector)([Fe,We],((e,t)=>e.includes(t))),Ge=(0,Le.createSelector)([e=>e.disabled,Ae,We],((e,t,n)=>!t.ids.includes(n)&&e.includes(n))),Ue=(0,Le.createSelector)(Oe,(e=>Object.keys(e))),Ke=(0,Le.createSelector)(Ae,(({ids:e})=>e)),Ze=(0,Le.createSelector)(Ae,(({lastFocusedNodeId:e})=>e)),Qe=(0,Le.createSelector)(Ae,(({isMultiSelecting:e})=>e)),qe=(0,Le.createSelector)([Oe,Ke],((e,t)=>t.map((t=>e[t])))),Ye=(0,Le.createSelector)(Oe,(e=>Object.values(e).filter((e=>0===e.level)))),$e=(0,Le.createSelector)([Oe,Ye],((e,t)=>t.reduce(((t,n)=>[...t,...Je(e,(0,M.ensureDefined)(n))]),[])));function Je(e,t){const n=[];for(const o of t.children)n.push(e[o]),n.push(...Je(e,e[o]));return n}const Xe=(0,Le.createSelector)([Oe,Ye,Fe],((e,t,n)=>{const o=new Set(n);return t.reduce(((t,n)=>[...t,...tt(e,(0,M.ensureDefined)(n),o)]),[])})),et=(0,Le.createSelector)([Oe,Ke,Fe],((e,t,n)=>{const o=new Set(n);return[{id:"drag-list",level:-1,children:t}].reduce(((t,n)=>[...t,...tt(e,(0,M.ensureDefined)(n),o)]),[])}));function tt(e,t,n){const o=[];for(const r of t.children){const t=e[r];void 0!==t&&(o.push(t),n.has(r)&&o.push(...tt(e,t,n)))}return o}function*nt(e){const{selectedIds:t,nodes:n}=yield(0,E.call)(e),o={};for(let e=0;e<n.length;++e){const t=n[e];o[t.id]=t}yield(0,E.put)((e=>({type:q,nodes:e}))(o)),yield(0,E.put)(Ee(t));!Ze(yield(0,E.select)())&&t.length>0&&(yield(0,E.put)(De(t[0])),yield(0,E.put)(xe(t[0])))}function*ot(e){for(;;){if((yield(0,E.take)([ce,ue])).type===ce){const t=Ue(yield(0,E.select)()).filter((t=>!e(t)));yield(0,E.put)(Re(t))}else yield(0,E.put)(Re([]))}}function*rt(){for(;;){const{type:e}=yield(0,E.take)([ie,re]),t=yield(0,E.select)(),n=$e(t),o=n.length,r=Ze(t),i=[...Ke(t)],s=1===i.length&&i[0]!==r,a=n.findIndex((e=>e.id===(s?i[0]:r)));if(e===re&&0===a||e===ie&&a===o-1)continue;const l=dt(t,e===ie?"next":"previous",n,a),{id:c}=l;i.includes(c)&&r?(yield(0,E.put)(Ne(r,!1,1)),yield(0,E.put)(De(c))):yield(0,E.put)(Ne(c,!0,1)),yield(0,E.put)(xe(c))}}function*it(e,t){for(;;){const{type:n}=yield(0,E.take)([oe,ne]),o=yield(0,E.select)(),r=$e(o),i=qe(o),s=Ze(o);if(1===i.length&&i[0].id!==s&&!s){if(n===oe){yield(0,E.put)(De(i[0].id))
;continue}if(n===ne){const e=r.findIndex((e=>e.id===i[0].id)),t=dt(o,"previous",r,e);yield(0,E.put)(De(t.id));continue}}const a=r.findIndex((e=>e.id===s)),l=n===oe?"next":"previous",c=dt(o,l,r,a),{id:u}=c;e?e([u],l):yield(0,E.put)(Ee([u])),t&&t(u),yield(0,E.put)(De(u))}}function*st(e,t=()=>!0){for(;;){const{mode:n,nodeId:o,isSelected:r}=yield(0,E.take)(pe);let i=[...Ke(yield(0,E.select)())];const s=$e(yield(0,E.select)());if(1===n)r?i.push(o):i.splice(i.indexOf(o),1);else if(2===n&&i.length>0){const e=Ze(yield(0,E.select)());let n=s.findIndex((t=>t.id===e));-1===n&&(n=s.reduce(((e,t,n)=>i.includes(t.id)?n:e),-1));const r=s.findIndex((e=>e.id===o));if(n!==r)for(let e=Math.min(n,r);e<=Math.max(n,r);e++){const n=s[e].id;!i.includes(n)&&t(n)&&i.push(n)}}else i=o?[o]:[];const a=new Set(i);i=s.reduce(((e,t)=>(a.has(t.id)&&e.push(t.id),e)),[]),e?e(i):yield(0,E.put)(Ee(i)),yield(0,E.put)(De(o))}}function*at(e=()=>!0,t){const{dropTarget:n,dropType:o,isHoveredLeft:r,boundBox:i,isLastChild:s}=t,a=Pe(yield(0,E.select)()),l=He(yield(0,E.select)(),(0,M.ensureDefined)(n.parentId)),c=s&&"after"===o,u=qe(yield(0,E.select)()),d=!c||!r&&e(u,n,o)?n:l,h=a.node&&a.node.id!==d.id||a.dropType!==o;u.map((e=>e.id)).includes(d.id)?yield(0,E.put)({type:le}):h&&e(u,d,o)&&(yield(0,E.put)(((e,t,n)=>({type:ae,node:e,dropType:t,boundBox:n}))(d,o,i)))}function*lt(e){yield(0,E.throttle)(0,se,at,e)}function*ct(e){for(;;){yield(0,E.take)(te);const t=qe(yield(0,E.select)()),{node:n,dropType:o}=Pe(yield(0,E.select)());if(n&&o){const r=new CustomEvent("tree-node-drop",{detail:{nodes:t,target:n.id,type:o}});if(e&&e(r),!r.defaultPrevented){const e=Ke(yield(0,E.select)());yield(0,E.put)(Te(e,n.id,o))}}}}function*ut(e){for(;;){yield(0,E.take)(ge);e(Oe(yield(0,E.select)()))}}function dt(e,t,n,o){const r=n.length;let i;-1===o&&"previous"===t&&(o=r);let s=0;for(;!i||Math.abs(s)<r&&((a=i).level>1&&!Ve(e,(0,M.ensureDefined)(a.parentId)));)s+="next"===t?1:-1,i=n[(o+s+r)%r];var a;return i}function*ht(e={}){const{saga:t,onDrop:n,canMove:o,onMove:r,onSelect:i,onKeyboardSelect:s,initState:a,canBeAddedToSelection:l}=e,c=[(0,E.fork)(lt,o),(0,E.fork)(ct,n),(0,E.fork)(st,i,l),(0,E.fork)(it,i,s),(0,E.fork)(rt)];for(t&&c.push((0,E.fork)(t)),r&&c.push((0,E.fork)(ut,r)),l&&c.push((0,E.fork)(ot,l));;){a&&(yield(0,E.call)(nt,a));const e=yield(0,E.all)(c);yield(0,E.take)(X);for(const t of e)yield(0,E.cancel)(t)}}var pt=n(6047),mt=n(37265);const ft=(0,s.getLogger)("Platform.GUI.ObjectTree.CallApi");const gt={ids:[],lastFocusedNodeId:void 0,isMultiSelecting:!1};const vt={node:void 0,dropType:void 0,boundBox:void 0};const bt=(0,pt.combineReducers)({nodes:function(e={},t){switch(t.type){case q:return t.nodes;case Y:{const{nodes:n}=t,o=n.map((e=>e.id)),r={...e};for(const t of Object.keys(e))if(!o.includes(t)){const{parentId:e}=r[t];e&&(r[e]={...r[e],children:r[e].children.filter((e=>e!==t))}),delete r[t]}for(const e of n){const t=e.id;if(r.hasOwnProperty(t)){!(0,mt.deepEquals)(r[t].children,e.children)[0]&&(r[t]={...r[t],children:[...e.children]})}else{r[t]=e;const{parentId:n}=e
;if(n&&!r[n].children.includes(t))throw new Error("Not implemented")}}return r}case $:{const{type:n,nodeId:o,...r}=t;return{...e,[o]:{...e[o],...r}}}case J:{const{nodes:n}=t,o={...e};return Object.keys(n).forEach((e=>{o[e]={...o[e],...n[e]}})),{...e,...o}}case ge:{const{ids:n,targetId:o,dropType:r}=t,i=(0,M.ensureDefined)(e[o].parentId),s=e[i],a={};for(const t of n){const n=e[t];if(n.parentId){const o=a[n.parentId]||e[n.parentId];a[n.parentId]={...o,children:o.children.filter((e=>e!==t))}}a[t]={...n,parentId:i,level:s.level+1}}const l=s.children.filter((e=>!n.includes(e)));return l.splice(((e,t,n)=>{switch(n){case"before":return e.indexOf((0,M.ensureDefined)(t));case"inside":return e.length;case"after":return e.indexOf((0,M.ensureDefined)(t))+1;default:return 0}})(l,o,r),0,...n),a[i]={...e[i],children:l,isExpanded:!0},{...e,...a}}default:return e}},selection:function(e=gt,t){switch(t.type){case ee:{const{ids:n}=t;return{...e,ids:n,lastFocusedNodeId:n.length>0?e.lastFocusedNodeId:void 0}}case ce:return{...e,isMultiSelecting:!0};case ue:return{...e,isMultiSelecting:!1};case de:return{...e,lastFocusedNodeId:t.nodeId};case Y:{const n=new Set(t.nodes.map((e=>e.id)));return e.lastFocusedNodeId&&!n.has(e.lastFocusedNodeId)&&delete e.lastFocusedNodeId,{...e,ids:e.ids.filter((e=>n.has(e)))}}default:return e}},dropTarget:function(e=vt,t){switch(t.type){case ae:{const{node:n,dropType:o,boundBox:r}=t;return{...e,node:n,dropType:o,boundBox:r}}case le:case ve:case X:return{...vt};default:return e}},expanded:function(e=[],t){if(t.type===me){const{nodeId:n,isExpanded:o}=t;if(o)return[...e,n];const r=[...e];return r.splice(e.indexOf(n),1),r}return e},disabled:function(e=[],t){return t.type===fe?[...t.ids]:e},scrollToId:function(e=null,t){return t.type===he?null===t.nodeId?null:{id:t.nodeId}:e}});var _t=n(68788),yt=n(69690),St=n(84952),wt=n(49483);var Ct,Et,Tt=n(16812),It=n(98314),Mt=n(47201),Dt=n(70412);!function(e){e[e.Normal=0]="Normal",e[e.Small=1]="Small"}(Ct||(Ct={})),function(e){e[e.Select=0]="Select",e[e.Click=1]="Click"}(Et||(Et={}));const xt=o.createContext({size:0,smallSizeTreeNodeAction:1}),Nt={[I.Modifiers.Mod]:1,[I.Modifiers.Shift]:2};var kt=n(69533),Rt=n(56726);const Bt=()=>{};class Lt extends o.PureComponent{constructor(){super(...arguments),this._ref=null,this._handleRef=e=>{this._ref=e;const{connectDragSource:t,connectDropTarget:n,connectDragPreview:o}=this.props;(0,M.ensureDefined)(n)(this._ref),(0,M.ensureDefined)(t)(this._ref),(0,M.ensureDefined)(o)((0,It.getEmptyImage)(),{captureDraggingState:!0})},this._handleTouchStart=e=>{const t=(e,t)=>{const n=function(e,t){try{const n=document.createEvent("TouchEvent");return n.initTouchEvent(e,!0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,t.touches,t.targetTouches,t.changedTouches),n}catch{return null}}(e,t);if(n)return n;const o=Array.from(t.changedTouches),r=Array.from(t.touches),i=Array.from(t.targetTouches);return new TouchEvent(e,{bubbles:!0,changedTouches:o,touches:r,targetTouches:i})},n=e.target;if(n instanceof Element){const e=e=>{
const o=e;if(!n.isConnected){o.preventDefault();const e=t("touchmove",o);document.body.dispatchEvent(e)}},o=r=>{const i=r;if(!n.isConnected){i.preventDefault();const e=t("touchend",i);document.body.dispatchEvent(e)}n.removeEventListener("touchend",o),n.removeEventListener("touchmove",e)};n.addEventListener("touchend",o),n.addEventListener("touchmove",e)}}}componentDidMount(){this._ref?.addEventListener("touchstart",this._handleTouchStart)}componentWillUnmount(){this._ref?.removeEventListener("touchstart",this._handleTouchStart)}render(){return o.createElement(Ot,{...this.props,reference:this._handleRef})}getNode(){return(0,M.ensureNotNull)(this._ref)}}const Ot=e=>{const{id:t,isSelected:n,isOffset:r,isExpandable:i,setIsSelected:s,isDisabled:a,isExpanded:l,onClick:c,parentId:u,setIsExpanded:d,reference:h,isFirstListItem:p,isLastListItem:f,nodeRenderer:g,isChildOfSelected:v=!1,isStuckNode:b}=e,{size:_,smallSizeTreeNodeAction:y}=(0,o.useContext)(xt),S=(0,o.useRef)(null),w=(0,Mt.createSafeMulticastEventHandler)((e=>S.current=e),h);let[C,E]=(0,Dt.useHover)();return(wt.CheckMobile.any()||wt.CheckMobile.isIPad())&&(C=n,E={onMouseOut:Bt,onMouseOver:Bt}),o.createElement("div",{className:m(Rt.wrap,n&&Rt.selected,v&&Rt.childOfSelected,a&&Rt.disabled,i&&Rt.expandable),onClick:1===_&&0===y?T:function(e){if(e.defaultPrevented)return;const o=Nt[(0,I.modifiersFromEvent)(e)]||0;!a&&s&&s(t,!n,o);c&&0===o&&c(e,t)},onContextMenu:T,ref:w,...E},i&&o.createElement(D.Icon,{icon:kt,className:m(Rt.expandHandle,l&&Rt.expanded),onClick:function(e){e.preventDefault(),i&&d(t,!l)},onMouseDown:function(e){e.preventDefault()}}),g({id:t,isOffset:r,parentId:u,isDisabled:a,isSelected:n,isChildOfSelected:v,isHovered:C,isExpanded:l,isFirstListItem:p,isLastListItem:f,isStuckNode:b}));function T(){a||n||!s||s(t,!0)}},At=o.createContext({});function Pt(e,t){const{id:n}=t,o=He(e,n),r=je(e,n);let i=!1,s=o.parentId;for(;s&&!i;)i=je(e,s),s=He(e,s).parentId;return{...o,isSelected:r,isChildOfSelected:i,isExpanded:o.children.length>0&&Ve(e,n),isExpandable:o.children.length>0,isDisabled:Ge(e,n)}}function Ft(e){return(0,pt.bindActionCreators)({setIsExpanded:ke,processDropTarget:we,dropSelection:Ce,selectNext:_e,selectPrevious:be,setIsSelected:Ne,endDrag:Be},e)}const zt=(0,V.connect)(Pt,Ft,null,{context:At})((function(e){const t=(0,o.useRef)(null),[,n,r]=(0,Tt.useDrag)({type:"node",item:t=>{const{id:n,isDisabled:o,isSelected:r}=e;return o||r||e.setIsSelected(n,!0),e},end:e=>{e.endDrag()}}),[,i]=(0,yt.useDrop)({accept:"node",hover:(n,o)=>{const r=t.current;if(!r)return;if(e.isStuckNode)return;const i=r.getNode(),s=i.getBoundingClientRect(),a=s.bottom-s.top,l=o.getClientOffset();if(l){const t=l.y-s.top;let n,o;if(n=0===e.children.length?t<a/2?"before":"after":t<a/3?"before":e.isExpanded||t>=a/3&&t<2*a/3?"inside":"after",void 0!==e.getContainerElement){const t=e.getContainerElement().getBoundingClientRect();o={top:s.top-t.top,left:s.left-t.left,bottom:s.top-t.top+s.height,right:s.left-t.left+s.width,height:s.height,width:s.width}}else o={top:i.offsetTop,
left:i.offsetLeft,bottom:i.offsetTop+i.offsetHeight,right:i.offsetLeft+i.offsetWidth,height:i.offsetHeight,width:i.offsetWidth};e.processDropTarget(e,n,l.x-s.left<48,o,e.isLastChild)}}});return o.createElement(Lt,{...e,connectDragSource:n,connectDropTarget:i,connectDragPreview:r,ref:t})})),Wt=(0,V.connect)(Pt,Ft,null,{context:At})(Ot);var Ht=n(35749),jt=n(65982),Vt=n(42842);function Gt(e){const t=e(),n=(0,o.useRef)(t);n.current=t;const[r,i]=(0,o.useState)(n.current),s=(0,o.useRef)(null);return(0,o.useEffect)((()=>{null===s.current&&(s.current=requestAnimationFrame((()=>{s.current=null,i(n.current)})))})),(0,o.useEffect)((()=>()=>{s.current&&cancelAnimationFrame(s.current)}),[]),r}function Ut(e){const{dropTargetOffset:t,mousePosition:n}=e;if(!t)return{display:"none"};const{x:o,y:r}=t,i=n&&t?n.y-t.y:0,s=`translate(${o+(n&&t?n.x-t.x:0)}px, ${r+i}px)`;return{transform:s,WebkitTransform:s}}const Kt={top:0,left:0,position:"fixed",pointerEvents:"none",zIndex:100,opacity:.5,width:300,backgroundColor:"red"};function Zt(e){return{isDragging:e.isDragging()&&"node"===e.getItemType(),mousePosition:e.getClientOffset(),dropTargetOffset:e.getSourceClientOffset()}}const Qt=(0,V.connect)((function(e){return{items:et(e)}}),null,null,{context:At})((function(e){const{items:t,isDragging:n,nodeRenderer:r,dragPreviewRenderer:i}=e;return Gt((function(){return n?o.createElement(Vt.Portal,null,o.createElement("div",{style:{...Kt,...Ut(e)}},t.map((e=>{if(i){const t=i;return o.createElement(t,{key:e.id,...e})}return o.createElement(Wt,{id:e.id,key:e.id,nodeRenderer:r,isDragPreview:!0,isOffset:e.level>1})})))):null}))}));function qt(e){return o.createElement(Qt,{...e,...(0,jt.useDragLayer)(Zt)})}var Yt=n(3085),$t=n(33127);const Jt=o.forwardRef(((e,t)=>{const n=(0,o.useRef)(null);return e.connectDropTarget(n),(0,o.useImperativeHandle)(t,(()=>({getNode:()=>(0,M.ensureNotNull)(n.current)})),[]),o.createElement("div",{ref:n,style:{height:"100%",width:"100%"}})}));function Xt(e){const t=(0,o.useRef)(null),[,n]=(0,yt.useDrop)({accept:"node",hover:(n,o)=>{if(!t.current)return;const r=o.getClientOffset();if(null===r)return;const i=e.getOrderedNodes();if(0===i.length)return;const s=t.current.getNode().getBoundingClientRect(),a=e.getContainerElement().getBoundingClientRect();if("first"===e.type){const t={top:s.top-a.top+s.height,left:s.left-a.left,bottom:s.top-a.top+s.height,right:s.left-a.left+s.width,height:0,width:s.width};e.processDropTarget(i[0],"before",!1,t,!1)}if("last"===e.type){const t=r.x-s.left<48,n=i[i.length-1],o=t&&2===n.level?(0,M.ensureDefined)(i.find((e=>e.id===n.parentId))):n,l={top:s.top-a.top,left:s.left-a.left,bottom:s.top-a.top,right:s.left-a.left+s.width,height:s.height,width:s.width};e.processDropTarget(o,"after",t,l,!1)}}});return o.createElement(Jt,{...e,connectDropTarget:n,ref:t})}const en=o.createContext({isOver:!1,transform:void 0});var tn=n(36612);function nn(e){const{dropType:t,boundBox:n}=e,{top:o,bottom:r,left:i}=(0,M.ensureDefined)(n);return[i,"before"===t||"inside"===t?o:r]}function on(e){return{isDragging:e.isDragging()
}}const rn=(0,V.connect)((function(e){const{boundBox:t,dropType:n,node:o}=Pe(e);return{boundBox:t,dropType:n,level:o?o.level:void 0}}),null,null,{context:At})((function(e){const{dropType:t,boundBox:n,isDragging:r,level:i,transform:s=nn}=e;return Gt((function(){if(!r||!t||!n)return null;const a={[tn.dropTarget]:"inside"!==t,[tn.dropTargetInside]:"inside"===t},{width:l,height:c}=n,[u,d]=s(e),h=`translate(${u}px, ${"inside"===t?d:d-1}px)`;return o.createElement("div",{className:m(a),style:{position:"absolute",transform:h,WebkitTransform:h,top:0,left:2===i?"46px":0,width:2===i?l-46+"px":l,height:"inside"===t?c:"2px"}})}))}));function sn(e){const{isDragging:t}=(0,jt.useDragLayer)(on);return o.createElement(rn,{...e,isDragging:t})}const an=o.createContext(null);var ln=n(69867),cn=n.n(ln);const un=o.forwardRef(((e,t)=>{const n=(0,o.useContext)(en),r=(0,o.useContext)(an),i=(0,o.useMemo)((()=>r?.readOnly?Wt:zt),[r?.readOnly]);return o.createElement("div",{...e,ref:t},r?.id?o.createElement("div",{className:f()(cn().sticky,!r.isShowStuck&&cn().hideSticky)},o.createElement(i,{id:r.id,key:r.id,isStuckNode:!0,nodeRenderer:r.nodeRenderer,readOnly:r.readOnly,onClick:r.onClick,getContainerElement:r.getContainerElement})):null,e.children,n.isOver&&o.createElement(sn,{transform:n.transform}))}));var dn=n(63273),hn=n(27802);const pn=38+I.Modifiers.Shift,mn=40+I.Modifiers.Shift;const fn=o.forwardRef((function(e,t){const{navigationKeys:n,renderList:r,stopMultiSelect:i,startMultiSelect:s,isMultiSelecting:a,nodeRenderer:l,dragPreviewRenderer:c,className:u,connectDropTarget:d,readOnly:h,onClick:p,dropLayerTransform:m,setFocusedNode:g,scrollToId:v,rowHeight:b,onMultiSelectPrevious:_,onMultiSelectNext:y,onMoveCursorToNext:S,onMoveCursorToPrevious:w,onKeyDown:C,outerRef:E,width:T,height:D,isOver:x,processDropTarget:N,autofocus:k,accessibleScope:B}=e,[L,O]=(0,o.useState)(),[A,P]=(0,o.useState)(!0),[F,z]=(0,o.useState)(!1),W=(0,o.useContext)(R),H=(0,o.useRef)(null),j=(0,_t.useDragDropManager)();(0,o.useEffect)((()=>{const e=j.getMonitor();e.subscribeToStateChange((()=>P(!e.isDragging())))}),[]),(0,o.useEffect)((()=>{k&&H.current?.focus()}),[]),(0,o.useEffect)((()=>{const e=e=>{[I.Modifiers.Mod,I.Modifiers.Shift].includes((0,I.modifiersFromEvent)(e))&&s()},t=e=>{a&&![I.Modifiers.Mod,I.Modifiers.Shift].includes((0,I.modifiersFromEvent)(e))&&i()};return document.addEventListener("keydown",e),document.addEventListener("keyup",t),document.addEventListener("mousemove",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t),document.removeEventListener("mousemove",t)}}),[a]),function(e){(0,o.useEffect)((()=>{if(wt.isEdge){let t=null;const n=(0,M.ensureNotNull)(e.current),o=e=>{if(e.target instanceof Element){const n=(0,M.ensureNotNull)(e.target.closest("[draggable]"));n instanceof HTMLElement&&(n.style.opacity="0",t=requestAnimationFrame((()=>n.style.opacity="1")))}};return n.addEventListener("dragstart",o),()=>{n.removeEventListener("dragstart",o),null!==t&&cancelAnimationFrame(t)}}return()=>{}}),[])}(H);const V=(0,
o.useCallback)((()=>(0,M.ensureNotNull)(oe.current)),[]),G=(0,o.useCallback)((()=>r),[r]),U=(0,o.useMemo)((()=>{const e=h?Wt:zt,t=[];let n,i;t.push({type:"padding",node:o.createElement(Xt,{type:"first",key:"padding-top",getContainerElement:V,getOrderedNodes:G,processDropTarget:N})});for(let s=0;s<r.length;s++){const a=r[s];a.isSticky&&(i=a.id),1===a.level&&(void 0!==n&&n!==a.parentId&&t.push({type:"separator",node:o.createElement("div",{key:n+"_separator",className:hn.separator})}),n=a.parentId),t.push({type:"node",stickyNodeId:i,isSticky:a.isSticky,node:o.createElement(e,{id:a.id,key:a.id,isFirstListItem:0===s,isLastListItem:s===r.length-1,isExpandable:a.children.length>0,nodeRenderer:l,readOnly:h,onClick:p,isOffset:a.level>1,getContainerElement:V})})}return t.push({type:"padding",node:o.createElement(Xt,{type:"last",key:"padding-bottom",getContainerElement:V,getOrderedNodes:G,processDropTarget:N})}),t}),[r]),K=(0,o.useMemo)((()=>{if(L){const e=U[L];return e&&"stickyNodeId"in e?e.stickyNodeId:void 0}}),[L,U]),Z=(0,o.useRef)([]);Z.current=U;const Q=(0,o.useCallback)((e=>{let{style:t}=e;const{index:n}=e,r=Z.current[n];return n===Z.current.length-1&&(t={...t,bottom:0,minHeight:t.height},delete t.height),"isSticky"in r&&r.isSticky&&(t={...t,zIndex:ee.current?.scrollTop&&t.top&&+t.top<=ee.current?.scrollTop?2:5}),o.createElement("div",{style:t},Z.current[n].node)}),[]),q=(0,o.useCallback)((e=>{const t=Z.current[e];return"padding"===t.type?6:"function"==typeof b?b(e,t):b}),[b]),Y=(0,o.useCallback)((e=>(0,M.ensure)(Z.current[e].node.key)),[]),$=(0,o.useMemo)((()=>null===v?{index:-1}:{index:Z.current.findIndex((e=>e.node.key===v.id))}),[v]);d(H);const[J,X,ee,te]=(0,$t.useOverlayScroll)(),ne=(0,o.useRef)(null);(0,o.useEffect)((()=>(0,M.ensureNotNull)(ne.current).resetAfterIndex(0,!0)),[U]),(0,o.useEffect)((()=>{let e=$.index;const t=Z.current[$.index];L&&e<=L&&t&&"stickyNodeId"in t&&t.stickyNodeId&&(e-=1),(0,M.ensureNotNull)(ne.current).scrollToItem(e)}),[$]);const oe=(0,o.useRef)(null),re=(0,o.useMemo)((()=>({isOver:x,transform:m})),[x,m]),ie=(0,o.useMemo)((()=>({id:K,nodeRenderer:l,getContainerElement:V,onClick:p,isShowStuck:A,readOnly:h})),[K,l,V,A,p,h]),se=(0,o.useRef)(null),ae=(0,o.useRef)({startScroll(e){const t=()=>{null!==ee.current&&(se.current=requestAnimationFrame(t),ee.current.scrollBy({top:e}))};this.stopScroll(),t()},stopScroll(){null!==se.current&&(cancelAnimationFrame(se.current),se.current=null)},getListElement:()=>ee.current});return(0,o.useImperativeHandle)(t,(()=>ae.current),[]),(0,o.useEffect)((()=>()=>ae.current.stopScroll()),[x]),(0,o.useEffect)((()=>{if(!H.current)return;function e(e){if(!t.matches(":focus-visible"))return;if(!W)return;const{viewModel:n}=W,o=n.selection();e.defaultPrevented||e.currentTarget!==e.target||o.selected().length||S()}const t=H.current;return t.addEventListener("focus",e),()=>{t.removeEventListener("focus",e)}}),[H,S,W]),o.createElement(en.Provider,{value:re},o.createElement(an.Provider,{value:ie},o.createElement("div",{...X,className:f()(hn.tree,u,F&&hn.accessible),ref:H,
onFocus:function(e){if(void 0===B)return;B(e)&&z(!0)},onBlur:function(e){z(!1)},"data-name":"tree",tabIndex:0,onKeyDown:function(e){z(!1);const t=(0,I.hashFromEvent)(e);if(e.defaultPrevented||(0,Ht.isNativeUIInteraction)(t,e.target))return;const o=(0,M.ensureDefined)(J.scrollPosTop),r=(0,M.ensureDefined)(J.contentHeight),i=(0,M.ensureDefined)(J.containerHeight);if(i){const n=.875*i,s=o+i===r;switch(t){case 35:s||(e.preventDefault(),le(r));break;case 36:0!==o&&(e.preventDefault(),le(0));break;case 33:0!==o&&(e.preventDefault(),le(Math.max(0,o-n)));break;case 34:s||(e.preventDefault(),le(Math.min(o+n,r)))}}W||t!==pn||(e.preventDefault(),_());W||t!==mn||(e.preventDefault(),y());(38===t||void 0!==n&&"previous"===n[t])&&(e.preventDefault(),w());(40===t||void 0!==n&&"next"===n[t])&&(e.preventDefault(),S());if((8===t||46===t)&&W){const{viewModel:e}=W,t=e.selection(),n=t.selected();if(1!==n.length)return;const o=e.getNextNodeIdAfterRemove(n[0]);if(null===o)return;e.onChange().subscribe(null,(()=>{if(t.selected().length)return;const n=e.entity(o);n&&(t.set([n]),g(o))}),!0)}C?.(e)}},o.createElement(Yt.OverlayScrollContainer,{...J,className:hn.overlayScrollWrap}),o.createElement(St.VariableSizeList,{ref:function(e){ne.current=e},className:hn.listContainer,width:T,height:D,itemCount:U.length,itemSize:q,children:Q,itemKey:Y,outerRef:function(e){ee.current=e,E&&E(e)},innerRef:function(e){oe.current=e},innerElementType:un,onItemsRendered:function({visibleStartIndex:e}){O(e),te()},overscanCount:20,direction:(0,dn.isRtl)()?"rtl":"ltr"}),o.createElement(qt,{dragPreviewRenderer:c,nodeRenderer:l}))));function le(e){ee.current?.scrollTo({left:0,top:e})}}));const gn=(0,V.connect)((function(e){return{renderList:Xe(e),orderedNodes:$e(e),isMultiSelecting:Qe(e),selectedIds:Ke(e),scrollToId:ze(e)}}),(function(e){return(0,pt.bindActionCreators)({startMultiSelect:Ie,stopMultiSelect:Me,setFocusedNode:De,processDropTarget:we,onMoveCursorToNext:_e,onMoveCursorToPrevious:be,onMultiSelectPrevious:ye,onMultiSelectNext:Se},e)}),null,{context:At})((function(e){const t=(0,o.useRef)(null),[{isOver:n},r]=(0,yt.useDrop)({accept:"node",drop:(n,o)=>{("touch"===e.drag||wt.isFF)&&t.current?.stopScroll(),o.getItem().dropSelection()},hover:(n,o)=>{if("touch"!==e.drag&&!wt.isFF)return;const r=o.getClientOffset();if(null===r)return;const i=t.current?.getListElement()??null;if(null===i)return;const s=i.getBoundingClientRect();((n,o,r)=>{const i=Math.abs(n-r),s=Math.abs(n-o);if(s>40&&i>40||i<=40&&s<=40)return void t.current?.stopScroll();var a,l,c,u;a=s>20&&s<=40,c=i<=20,u=s<=20,(l=i>20&&i<=40)||a?"touch"===e.drag?t.current?.startScroll(l?-5:5):t.current?.startScroll(l?-2:2):(c||u)&&("touch"===e.drag?t.current?.startScroll(c?-10:10):t.current?.startScroll(c?-5:5))})(r.y,s.bottom,s.top)},collect:e=>({isOver:e.isOver()})});return o.createElement(fn,{...e,isOver:n,connectDropTarget:r,ref:t})})),vn={delayTouchStart:100};function bn(e){
const{canBeAddedToSelection:t,initState:n,onSelect:r,canMove:i,onDrop:s,onMove:a,nodes:l,selectedIds:c,onKeyboardSelect:u,saga:d,lastFocusedNodeObject:h,lastSyncTimestampRef:p,scrollToId:m,...f}=e,[g,v]=(0,o.useState)(null);return(0,o.useEffect)((()=>{const e=(0,j.default)();v(function(e){const t=(0,pt.applyMiddleware)(e);return(0,pt.createStore)(bt,t)}(e));const o=e.run(ht,{initState:n,onKeyboardSelect:u,saga:d,canMove:i,onMove:a,onDrop:s,onSelect:r,canBeAddedToSelection:t});return()=>o.cancel()}),[]),(0,o.useEffect)((()=>(null!==g&&l&&(p&&(p.current=performance.now()),g.dispatch((e=>({type:Y,nodes:e}))(l))),()=>{})),[g,l]),(0,o.useEffect)((()=>{null!==g&&c&&g.dispatch(Ee(c))}),[g,c]),(0,o.useEffect)((()=>{null!==g&&h?.id&&g.dispatch(De(h.id))}),[g,h]),null===g?null:o.createElement(_n,{store:g,scrollToId:m,...f})}const _n=o.memo((function(e){const{store:t,scrollToId:n,...r}=e,i="touch"===e.drag?U.TouchBackend:G.HTML5Backend;return(0,o.useEffect)((()=>{t.dispatch(xe(n?.id??null))}),[n]),o.createElement(K.DndProvider,{backend:i,options:vn},o.createElement(V.Provider,{store:t,context:At},o.createElement(gn,{...r})))}));function yn(e,t){(0,W.trackEvent)("Object Tree",e,t)}function Sn(e){return e.length>1?"Multi select":e[0].gaLabel()}function wn(e){return(0,j.eventChannel)((t=>{const n={};return e.onChange().subscribe(n,(()=>t({type:X}))),e.onGroupCreated().subscribe(n,(e=>t(ke(e,!0)))),e.selection().onChange().subscribe(n,(e=>t(Ee(e)))),()=>{e.onChange().unsubscribeAll(n),e.selection().onChange().unsubscribeAll(n),e.onGroupCreated().unsubscribeAll(n)}}),j.buffers.expanding())}function*Cn(){for(;;)yield(0,E.take)([oe,ne]),yn("Select","Arrow")}function*En(){for(;;){const{mode:e}=yield(0,E.take)(pe);1===e&&yn("Multi select","Ctrl"),2===e&&yn("Multi select","Shift")}}function*Tn(e){for(;;){yield(0,E.take)(te);const{node:t,dropType:n}=Pe(yield(0,E.select)());if(t){const o=qe(yield(0,E.select)()),r=o.map((t=>(0,M.ensureNotNull)(e.entity(t.id))));let i="Drag";1===t.level&&"inside"!==n&&o.some((e=>2===e.level))?i="From the group":2!==t.level&&"inside"!==n||!o.some((e=>1===e.level))?1===o.length&&o[0].parentId!==t.parentId&&(i="Existing pane"):i="To the group",yn(i,Sn(r))}}}function*In(e){yield(0,E.fork)(Cn),yield(0,E.fork)(En),yield(0,E.fork)(Tn,e)}function*Mn(e){yield(0,E.fork)(In,e);const t=yield(0,E.call)(wn,e);ft.logNormal("Opened object tree data source channel");try{for(;;){const e=yield(0,E.take)(t);yield(0,E.put)(e)}}finally{ft.logNormal("Closed object tree data source channel"),t.close()}}const Dn=o.createContext(null);var xn=n(14729),Nn=n(898),kn=n(42905);const Rn=T.mobiletouch?"touch":"native";function Bn(e){const{viewModel:t,showHeader:n=!0,nodeRenderer:r,isDialog:i=!1,hideHeaderTitle:s=!1}=e,a=(0,o.useRef)(null),l=function(e){const[t,n]=(0,o.useState)(e.getChartId()),r=(0,o.useRef)(t);return r.current=t,(0,o.useEffect)((()=>{return e.onChange().subscribe(null,t),()=>{e.onChange().unsubscribe(null,t)};function t(){const t=e.getChartId();r.current!==t&&n(t)}}),[]),t}(t),[c,u]=(0,Nn.useDimensions)(),[d,h]=(0,
o.useState)(null),p=(0,o.useMemo)((()=>({isTouch:T.touch,isDialog:i})),[i]);return o.createElement(Dn.Provider,{value:p},o.createElement(R.Provider,{value:{viewModel:t}},o.createElement("div",{className:kn.wrap,onContextMenu:xn.preventDefaultForContextMenu},n&&o.createElement(H,{hideTitle:s}),o.createElement("div",{className:kn.space,onClick:function(e){if(e.defaultPrevented)return;if(!(e.target instanceof Element)||null===a.current)return;e.target===a.current&&t.selection().set([])},ref:c},null!==u&&o.createElement(bn,{key:l,height:u.height,width:u.width,canBeAddedToSelection:function(e){const n=t.entity(e);return t.selection().canBeAddedToSelection(n)},nodeRenderer:r,initState:function(){const{nodes:e,selection:n}=t.getState();return{selectedIds:n,nodes:e}},canMove:function(e,n,o){return t.isSelectionDropable(n.id,o)},drag:Rn,rowHeight:Ln,onSelect:function(e){const n=e.map((e=>t.entity(e))).filter((e=>null!==e));t.selection().set(n)},onDrop:function(e){e.preventDefault();const{detail:{target:n,type:o}}=e;t.insertSelection(n,o)},scrollToId:d,saga:function*(){yield(0,E.fork)(Mn,t)},onKeyboardSelect:function(e){h({id:e})},outerRef:function(e){a.current=e},onKeyDown:function(e){if(13===(0,I.hashFromEvent)(e)){e.preventDefault();const n=t.selection().selected(),o=n.length>0?t.entity(n[0]):void 0;o&&t.openProperties(o)}},autofocus:i})))))}function Ln(e,t){switch(t.type){case"node":return 38;case"separator":return 13}}var On,An,Pn=n(56570);!function(e){e.Svg="svg"}(On||(On={})),function(e){e[e.NoSync=0]="NoSync",e[e.SyncInLayout=1]="SyncInLayout",e[e.GlobalSync=2]="GlobalSync"}(An||(An={}));var Fn=n(31261),zn=n(8510);function Wn(e){const{className:t,disabled:n,...r}=e;return o.createElement(D.Icon,{className:f()(zn.button,n&&zn.disabled,t),...r})}var Hn=n(77975);const jn=p.t(null,void 0,n(83390)),Vn=p.t(null,void 0,n(6321)),Gn=p.t(null,void 0,n(99894)),Un=p.t(null,void 0,n(51077)),Kn=p.t(null,void 0,n(27298)),Zn=p.t(null,void 0,n(98334)),Qn=p.t(null,void 0,n(67410));var qn=n(26023),Yn=n(84015),$n=n(60925),Jn=n(52870),Xn=n(49756),eo=n(94007),to=n(62766),no=n(6746);Pn.enabled("saveload_separate_drawings_storage");function oo(e){const{id:t}=e,n=(0,o.useContext)(R),{viewModel:r}=(0,M.ensureNotNull)(n),i=r.entity(t);return null===i?null:o.createElement(so,{...e,entity:i})}const ro=500,io={};function so(e){const{id:t,isOffset:r,isDisabled:i,isSelected:s,isChildOfSelected:a,isHovered:l,parentId:c,entity:u,isExpanded:d}=e,h=(0,o.useContext)(R),{viewModel:f}=(0,M.ensureNotNull)(h),g=(0,o.useContext)(Dn),{size:v}=(0,o.useContext)(xt),[b,_]=(0,o.useState)(!1),y=(0,o.useRef)(null),[S,w]=(0,o.useState)(u.title().value()),[C,E]=(0,o.useState)((()=>u.getIcon())),[T,x]=(0,o.useState)(u.isLocked()),[N,k]=(0,o.useState)(u.isVisible()),[B,L]=(0,o.useState)(u.isActualInterval()),[O,A]=(0,o.useState)(u.getDrawingSyncState()),[P,F]=(0,o.useState)(u.getModificationDate),[z,W]=(0,o.useState)(!1),[H,j]=((0,Hn.useWatchedValueReadonly)({watchedValue:f.getChartLayout()}),(0,o.useState)(!1)),V=(0,o.useRef)(null);(0,o.useEffect)((()=>{const e={}
;u.onLockChanged().subscribe(e,(()=>x(u.isLocked()))),u.onVisibilityChanged().subscribe(e,(()=>k(u.isVisible())));const t=u.title().spawn();t.subscribe((e=>w(e))),u.onIsActualIntervalChange().subscribe(e,(()=>L(u.isActualInterval()))),u.onSyncStateChanged().subscribe(e,(()=>A(u.getDrawingSyncState()))),u.modificationDateChanged().subscribe(e,(()=>F(u.getModificationDate())));const n=u.onIconChanged?u.onIconChanged():void 0;return n&&n.subscribe(e,(()=>E(u.getIcon()))),()=>{u.onIsActualIntervalChange().unsubscribeAll(e),u.onLockChanged().unsubscribeAll(e),u.onVisibilityChanged().unsubscribeAll(e),u.onSyncStateChanged().unsubscribeAll(e),u.modificationDateChanged().unsubscribeAll(e),t.destroy(),V.current&&clearTimeout(V.current),n&&n.unsubscribeAll(e)}}),[u]),(0,o.useEffect)((()=>{b&&y.current&&(y.current.focus(),y.current.setSelectionRange(0,S.length))}),[b]),(0,o.useEffect)((()=>{const e={};return f.hoveredObjectChanged().subscribe(e,te),()=>{f.hoveredObjectChanged().unsubscribeAll(e)}}),[d]),(0,o.useEffect)((()=>{f.setHoveredObject(l?t:null)}),[l]),(0,o.useEffect)((()=>{!s&&V.current&&(clearTimeout(V.current),V.current=null),_(!1)}),[s]);const G={};if(c){const e=f.entity(c);e&&(G["data-parent-name"]=e.title().value()),G["data-type"]=u.hasChildren()?"group":"data-source"}const U=Pn.enabled("test_show_object_tree_debug")?`<${u.id()}> (${u.zOrder()}) ${u.title()}`:u.title().value(),K=(null!==O&&io[O],l||z),Z=b&&s,Q=!!g&&g.isTouch,q=!!g&&g.isDialog,Y=B&&N?eo:to,$=u.hasChildren()?p.t(null,void 0,n(40983)):p.t(null,void 0,n(80014));let J=null;return C&&C.type===On.Svg&&(J=o.createElement(D.Icon,{icon:C.content||"",className:no.icon})),o.createElement("span",{className:m(no.wrap,i&&no.disabled,s&&no.selected,r&&no.offset,a&&no.childOfSelected,z&&!i&&!s&&!a&&no.hover,q&&!i&&!s&&!a&&no.dialog,q&&!i&&!s&&!a&&(0,Yn.isOnMobileAppPage)("any")&&no.mobile,!!P&&["apply-common-tooltip","common-tooltip-vertical"]),onMouseDown:function(e){b&&!(0,M.ensureNotNull)(y.current).contains(e.target)&&j(!0)},onClick:1===v?X:function(e){if(e.defaultPrevented)return;if(0!==(0,I.modifiersFromEvent)(e))return;if(V.current)e.preventDefault(),clearTimeout(V.current),V.current=null,f.openProperties(u),j(!1);else{const e=f.selection().selected();V.current=setTimeout((()=>{V.current=null,s&&!H&&1===e.length&&f.rename(u,(()=>_(!0))),j(!1)}),ro)}},onContextMenu:0===v?X:void 0,"data-tooltip":P?p.t(null,{replace:{modificationDate:P}},n(63944)):void 0},!Z&&o.createElement(o.Fragment,null,J,!1,o.createElement("span",{className:m(no.title,f.isMain(u)&&no.main,(!u.isVisible()||!B)&&no.disabled),...G},U),o.createElement("span",{className:no.rightButtons},u.canBeLocked()&&o.createElement(Wn,{title:T?Gn:Un,icon:T?Jn:Xn,className:m(no.button,(K||T)&&no.visible,"apply-common-tooltip"),onClick:function(e){if(e.defaultPrevented)return;e.preventDefault(),f.setIsLocked(t,!u.isLocked())},"data-role":"button","data-name":"lock","data-active":T}),o.createElement(Wn,{icon:Y,className:m(no.button,!B&&no.warn,(K||!N||!B)&&no.visible,"apply-common-tooltip"),
onClick:B?function(e){if(e.defaultPrevented)return;e.preventDefault(),f.setIsVisible(t,!u.isVisible())}:function(e){if(e.defaultPrevented)return;e.preventDefault(),f.openProperties(u,qn.TabNames.visibility)},title:function(){if(!B)return $;return N?Kn:Zn}(),"data-role":"button","data-name":"hide","data-active":!N}),u.canBeRemoved()&&o.createElement(Wn,{title:Qn,icon:$n,className:m(no.button,(Q||K)&&no.visible,"apply-common-tooltip"),onClick:function(e){if(e.defaultPrevented)return;e.preventDefault(),e.stopPropagation(),f.remove(t)},"data-role":"button","data-name":"remove"}))),Z&&o.createElement(Fn.InputControl,{value:S,onChange:function(e){w(e.currentTarget.value)},onClick:xn.preventDefault,className:no.renameInput,onKeyDown:function(e){27===(0,I.hashFromEvent)(e)?(e.preventDefault(),w(u.title().value()),_(!1)):13===(0,I.hashFromEvent)(e)&&(e.preventDefault(),ee())},reference:function(e){y.current=e},onBlur:ee,onDragStart:function(e){e.preventDefault(),e.stopPropagation()},draggable:!0,stretch:!0}));function X(e){e.defaultPrevented||b||!u.fullyConstructed()||(e.preventDefault(),e.persist(),f.openContextMenu(u,(()=>_(!0)),e))}function ee(){""!==S&&u.setName(S),w(u.title().value()),_(!1)}function te(e){if(u.hasChildren()&&!d){const t=null!==e&&u.childrenIds().has(e);W(t)}else W(t===e)}}var ao=n(56388),lo=n(65441);function co(e){const{viewModel:t,onClose:r,activeChartWidget:i}=e,[s,a]=(0,o.useState)(!1),[l,c]=(0,o.useState)(!1),[u,d]=(0,o.useState)(!1),h=(0,o.useRef)(null),f=(0,g.useForceUpdate)(),E=((0,v.useIsMounted)(),t.selection()),T=t.canSelectionBeGrouped();return(0,o.useEffect)((()=>{const e=window.matchMedia(w.DialogBreakpoints.TabletSmall);return e.addEventListener("change",I),()=>e.removeEventListener("change",I)}),[]),(0,o.useEffect)((()=>(E.onChange().subscribe(null,(()=>f())),()=>{E.onChange().unsubscribeAll(null)})),[E]),(0,o.useEffect)((()=>{const e=e=>d(e);return t.isContextMenuOpened().subscribe(e),()=>{t.isContextMenuOpened().unsubscribe(e)}}),[t]),o.createElement(o.Fragment,null,o.createElement(y.MatchMedia,{rule:w.DialogBreakpoints.TabletSmall},(e=>o.createElement(S.AdaptivePopupDialog,{additionalElementPos:"after",additionalHeaderElement:o.createElement("div",{className:lo.buttons},o.createElement(ao.ToolWidgetIconButton,{className:m(lo.button,!T&&lo.disabled),icon:F,onClick:D,isDisabled:!T,title:p.t(null,void 0,n(83390)),"data-name":"group-button"}),!1),className:lo.dialog,dataName:"object-tree-dialog",isOpened:!0,onClickOutside:l||e||u?()=>{}:r,onClose:r,ref:h,render:()=>o.createElement(uo,{isSmallTablet:e,viewModel:t}),title:p.t(null,void 0,n(88616)),showSeparator:!0}))),o.createElement(_.DrawerManager,null,s&&o.createElement(b.Drawer,{onClose:M,position:"Bottom"},o.createElement(C.ManageDrawings,{onClose:M,chartWidget:i,isMobile:!0}))));function I(){s&&!window.matchMedia(w.DialogBreakpoints.TabletSmall).matches&&a(!1)}function M(){a(!1)}function D(){t.createGroupFromSelection()}}function uo(e){const{isSmallTablet:t,viewModel:n}=e,r=(0,o.useMemo)((()=>({size:t?1:0,smallSizeTreeNodeAction:0
})),[t]);return o.createElement(xt.Provider,{value:r},o.createElement(Bn,{nodeRenderer:oo,showHeader:!1,viewModel:n,isDialog:!0}))}var ho=n(16216),po=n(77788),mo=n(52033);var fo=n(32112);function go(e,t){return`${e}:${t}`}function vo(e){const t=e.split(":");return{persistentId:t[0],instanceId:t[1]}}class bo{constructor(e){this._onChange=new mo.Delegate,this._recalculate=()=>{const e=this._groupModel.groups().map((e=>go(e.id,e.instanceId()))),t=this._selectionApi.allSources();this._selected=this._selected.filter((n=>e.includes(n)||t.includes(n))),this._onChange.fire(this._selected)},this._model=e,this._selectionApi=new fo.SelectionApi(this._model),this._groupModel=this._model.lineToolsGroupModel(),this._selected=this._getSelectedIds(),this._selectionApi.onChanged().subscribe(this,(()=>{this._selected=this._getSelectedIds(),this._onChange.fire(this._selected)})),this._groupModel.onChanged().subscribe(this,this._recalculate)}destroy(){this._selectionApi.onChanged().unsubscribeAll(this),this._groupModel.onChanged().unsubscribeAll(this)}set(e){const t=[];let n=e.map((e=>e.id()));for(const o of e)if(o.hasChildren()){const e=o.childrenIds();t.push(...Array.from(e.values())),n=n.filter((t=>!e.has(t)))}else t.push(o.id());this._selectionApi.set(t.map((e=>vo(e).persistentId))),this._selected=n,this._onChange.fire(this._selected)}canBeAddedToSelection(e){return null!==e&&e.canBeAddedToSelection()}onChange(){return this._onChange}selected(){return this._selected}_getSelectedIds(){return this._selectionApi.allSources().map((e=>this._model.dataSourceForId(e))).filter(mt.notNull).filter((e=>e.showInObjectTree())).map((e=>go(e.id(),e.instanceId())))}}class _o{constructor(e,t){this._controller=e,this._facade=t,this._groupModel=e.model().lineToolsGroupModel()}buildTree(){const e={};for(const t of this._controller.model().panes()){const n=t.sourcesByGroup().allWithoutMultipane().filter((e=>e.showInObjectTree()));e[t.id()]=yo(t.id(),0);for(const n of this._groupModel.groups()){const o=go(n.id,n.instanceId()),r=(0,M.ensureNotNull)(this._facade.getObjectById(o));if(r.pane()===t){const o=[...n.lineTools()].sort(((e,t)=>e.zorder()>t.zorder()?-1:1)).map((e=>go(e.id(),e.instanceId())));e[r.id()]=yo(r.id(),1,t.id(),o),e[t.id()].children.push(r.id());for(const t of o)e[t]=yo(t,2,r.id())}}for(const o of n){const n=go(o.id(),o.instanceId());e[n]||(e[n]=yo(n,1,t.id()),e[t.id()].children.push(n))}e[t.id()].children.sort(((e,t)=>{const n=(0,M.ensureNotNull)(this._facade.getObjectById(e)),o=(0,M.ensureNotNull)(this._facade.getObjectById(t));return(0,M.ensureNotNull)(o.zOrder())-(0,M.ensureNotNull)(n.zOrder())}))}return this._facade.invalidateCache(new Set(Object.keys(e))),e}}function yo(e,t,n,o=[]){return{id:e,level:t,parentId:n,children:o}}var So=n(58653),wo=n(45126),Co=n(64147),Eo=n(32755),To=n(79036),Io=n(22820),Mo=n(47930),Do=n(29137),xo=n(85719),No=n(84425),ko=n(60074),Ro=n(56657),Bo=n(928),Lo=n(19466),Oo=n(7295),Ao=n(28824),Po=n(57674)
;const Fo=new wo.TranslatedString("show {title}",p.t(null,void 0,n(51382))),zo=new wo.TranslatedString("hide {title}",p.t(null,void 0,n(13017))),Wo=new wo.TranslatedString("lock {title}",p.t(null,void 0,n(76104))),Ho=new wo.TranslatedString("unlock {title}",p.t(null,void 0,n(12525))),jo=new wo.TranslatedString("change {sourceTitle} title to {newSourceTitle}",p.t(null,void 0,n(23687))),Vo=new wo.TranslatedString("insert source(s) after",p.t(null,void 0,n(10370)));function Go(e,t){return t.every((t=>!(t.pane()!==e&&!t.allowsMovingBetweenPanes())))}function Uo(e){return e instanceof Do.DataSource&&e.showInObjectTree()?go(e.id(),e.instanceId()):null}function Ko(e){return new wo.TranslatedString(e.name(),e.title(Lo.TitleDisplayTarget.DataWindow))}const Zo=new mo.Delegate,Qo=Pn.enabled("saveload_separate_drawings_storage");function qo(e){return 0===e?0:1===e?1:2}class Yo{constructor(e,t){this._syncStateChanged=new mo.Delegate,this.getModificationDate=()=>{if(!(0,Eo.isLineTool)(this._dataSource))return null;const e=this._dataSource.serverUpdateTime();if(null===e)return null;const t=(0,So.getLocaleIso)();return`${(0,Mo.formatTime)(e,t,"year","day")}\n${(0,Mo.formatTime)(e,t,"hour","minute",{timeZoneName:!0})}`},this._updateSyncState=()=>{this._syncStateChanged.fire((0,M.ensureNotNull)(this.getDrawingSyncState()))},this._undoModel=e,this._dataSource=t,this._isWidgetPane=e.model().paneForSource(t)?.mode()===Io.PaneMode.Widget,(0,Eo.isLineTool)(t)?(t.linkKey().subscribe(this._updateSyncState),t.sharingMode().subscribe(this._updateSyncState),this._title=(0,No.createWVFromGetterAndSubscription)((()=>t.properties().title.value()||t.translatedType()),t.properties().title)):(0,ko.isSymbolSource)(t)?this._title=(0,No.createWVFromGetterAndSubscriptions)((()=>t.symbolTitle(Lo.TitleDisplayTarget.DataWindow,void 0,void 0,(0,wt.onWidget)()?"exchange":"listed_exchange")),[t.symbolChanged(),t.symbolResolved()]):(0,To.isStudy)(t)?this._title=(0,No.createWVFromGetterAndSubscriptions)((()=>t.title(Lo.TitleDisplayTarget.DataWindow)),[t.properties().childs().inputs,this._undoModel.model().properties().childs().paneProperties.childs().legendProperties.childs().showStudyArguments,t.onParentSourcesChanges(),t.series().symbolResolved()]):this._title=new Co.WatchedValue(t.title(Lo.TitleDisplayTarget.DataWindow)).spawn();const n=this._undoModel.lineBeingCreated();null!==n&&n===t&&n.isSynchronizable()&&Bo.isToolCreatingNow.subscribe(this._updateSyncState)}destroy(){(0,Eo.isLineTool)(this._dataSource)&&(this._dataSource.linkKey().unsubscribe(this._updateSyncState),this._dataSource.sharingMode().unsubscribe(this._updateSyncState),this._dataSource.serverUpdateTimeChanged().unsubscribeAll(this)),this._title.destroy(),Bo.isToolCreatingNow.unsubscribe(this._updateSyncState)}id(){return go(this._dataSource.id(),this._dataSource.instanceId())}title(){return this._title}gaLabel(){return(0,To.isStudy)(this._dataSource)?"Study":(0,Eo.isLineTool)(this._dataSource)?"Drawing":"Symbol"}canBeLocked(){return(0,
Eo.isLineTool)(this._dataSource)&&this._dataSource.userEditEnabled()}canBeRemoved(){return this._undoModel.mainSeries()!==this._dataSource&&this._dataSource.isUserDeletable()}canBeHidden(){return this._dataSource.canBeHidden()}canBeRenamed(){return(0,Eo.isLineTool)(this._dataSource)}fullyConstructed(){return this._undoModel.lineBeingCreated()!==this._dataSource}isVisible(){return this._dataSource.properties().visible.value()}isActualInterval(){return!(0,Eo.isLineTool)(this._dataSource)&&!(0,To.isStudy)(this._dataSource)||this._dataSource.isActualInterval()}onIsActualIntervalChange(){return(0,Eo.isLineTool)(this._dataSource)||(0,To.isStudy)(this._dataSource)?this._dataSource.onIsActualIntervalChange():Zo}isLocked(){return!!(0,Eo.isLineTool)(this._dataSource)&&this._dataSource.properties().frozen.value()}onVisibilityChanged(){return this._dataSource.properties().visible.listeners()}onLockChanged(){return(0,Eo.isLineTool)(this._dataSource)?this._dataSource.properties().frozen.listeners():Zo}getIcon(){const e=l,t=this._dataSource.getSourceIcon(),n=(0,To.isStudyStrategy)(this._dataSource);let o={type:On.Svg,content:n?Oo:Ao};if(e&&t)if("loadSvg"===t.type){const[n,r]=t.svgId.split("."),i="linetool"===n?e.linetool[r]:e.series[Number(r)];o={type:On.Svg,content:i||Ao}}else"svgContent"===t.type&&(o={type:On.Svg,content:t.content});return o}onIconChanged(){if(this._dataSource.onSourceIconChanged)return this._dataSource.onSourceIconChanged()}setVisible(e){const t=(e?Fo:zo).format({title:Ko(this._dataSource)});this._undoModel.setProperty(this._dataSource.properties().visible,e,t,xo.lineToolsDoNotAffectChartInvalidation&&(0,Eo.isLineTool)(this._dataSource))}setLocked(e){if((0,Eo.isLineTool)(this._dataSource)){const t=(e?Wo:Ho).format({title:Ko(this._dataSource)});this._undoModel.setProperty(this._dataSource.properties().frozen,e,t,xo.lineToolsDoNotAffectChartInvalidation)}}setName(e){if((0,Eo.isLineTool)(this._dataSource)){const t=jo.format({sourceTitle:this._dataSource.properties().title.value()||Ko(this._dataSource),newSourceTitle:e});this._undoModel.setProperty(this._dataSource.properties().title,e,t,xo.lineToolsDoNotAffectChartInvalidation)}}isCopiable(){return this._dataSource.copiable()}isClonable(){return this._dataSource.cloneable()}zOrder(){return this._dataSource.zorder()}remove(){this._undoModel.removeSource(this._dataSource,!1)}canBeAddedToSelection(){return this._undoModel.selection().canBeAddedToSelection(this._dataSource)}setAsSelection(){this._undoModel.model().selectionMacro((e=>{e.clearSelection(),e.addSourceToSelection(this._dataSource)}))}addToSelection(){this._undoModel.model().selectionMacro((e=>{e.addSourceToSelection(this._dataSource)}))}addSourcesToArray(e){return e.push(this._dataSource),e}insertSourcesBeforeThis(e){this._insertSources(e,(e=>this._undoModel.insertBefore(e,this._dataSource)))}insertSourcesAfterThis(e){this._insertSources(e,(e=>this._undoModel.insertAfter(e,this._dataSource)))}childrenIds(){return new Set}hasChildren(){return!1}pane(){return(0,
M.ensureNotNull)(this._undoModel.model().paneForSource(this._dataSource))}allowsMovingBetweenPanes(){return this._dataSource.allowsMovingBetweenPanes()}canBeAddedToGroup(){return(0,Eo.isLineTool)(this._dataSource)&&this._dataSource.boundToSymbol()}canInsertBeforeThis(e){return!this._isWidgetPane&&this._canInsertBeforeOrAfter(e)}canInsertAfterThis(e){return!this._isWidgetPane&&this._canInsertBeforeOrAfter(e)}detachFromParent(){if((0,Eo.isLineTool)(this._dataSource)){const e=this._undoModel.model(),t=this._undoModel.lineToolsGroupController(),n=e.lineToolsGroupModel().groupForLineTool(this._dataSource);null!==n&&t.excludeLineToolFromGroup(n,this._dataSource)}}canBeSyncedInLayout(){return(0,Eo.isLineTool)(this._dataSource)&&this._dataSource.isSynchronizable()}onSyncStateChanged(){return this._syncStateChanged}modificationDateChanged(){return(0,Eo.isLineTool)(this._dataSource)?this._dataSource.serverUpdateTimeChanged():Zo}setDrawingSyncState(e){if(!this.canBeSyncedInLayout()||!this.fullyConstructed())return;const t=this._dataSource;switch(e){case 0:if(null===t.linkKey().value())return;this._undoModel.unlinkLines([t]);break;case 1:if(null!==t.linkKey().value())return;this._undoModel.shareLineTools([t],1)}}getDrawingSyncState(){return this.canBeSyncedInLayout()?Qo?this.fullyConstructed()?qo(this._dataSource.sharingMode().value()):0:this.fullyConstructed()&&null!==this._dataSource.linkKey().value()?1:0:null}_canInsertBeforeOrAfter(e){const t=this._undoModel.model();if(!Go(this.pane(),e))return!1;if((0,Eo.isLineTool)(this._dataSource)){if(null!==t.lineToolsGroupModel().groupForLineTool(this._dataSource)&&e.some((e=>!e.canBeAddedToGroup())))return!1}return!0}_insertSources(e,t){const n=this._undoModel.model(),o=this._undoModel.lineToolsGroupController();this._undoModel.beginUndoMacro(Vo);const r=()=>{e.forEach((e=>e.detachFromParent()))},i=e.reduce(((e,t)=>t.addSourcesToArray(e)),[]);if((0,Eo.isLineTool)(this._dataSource)){const t=n.lineToolsGroupModel().groupForLineTool(this._dataSource);null!==t?((0,M.assert)(!e.some((e=>e.hasChildren()))),i.forEach((e=>{(0,Eo.isLineTool)(e)&&(t.containsLineTool(e)||o.addLineToolToGroup(t,e))}))):r()}else r();t(i),this._undoModel.endUndoMacro()}}class $o{constructor(e,t){this._onVisibilityChanged=new mo.Delegate,this._onLockChanged=new mo.Delegate,this._onIsActualIntervalChanged=new mo.Delegate,this._syncStateChanged=new mo.Delegate,this._linkKeyChangedBound=this._linkKeyChanged.bind(this),this._undoModel=e,this._group=t,this._lineTools=t.lineTools(),this._paneId=(0,M.ensureNotNull)(e.model().paneForSource(this._lineTools[0])).id();const n=()=>{this._lineTools.forEach((e=>{e.properties().visible.subscribe(this,(()=>this._onVisibilityChanged.fire())),e.properties().frozen.subscribe(this,(()=>this._onLockChanged.fire())),e.onIsActualIntervalChange().subscribe(this,(()=>this._onIsActualIntervalChanged.fire())),e.linkKey().subscribe(this._linkKeyChangedBound),e.sharingMode().subscribe(this._linkKeyChangedBound)}))};this._group.onChanged().subscribe(this,(e=>{this._unsubscribeFromAllLineTools(),
this._lineTools=this._group.lineTools(),n(),e.lockedChanged&&this._onLockChanged.fire(),e.visibilityChanged&&this._onVisibilityChanged.fire(),e.isActualIntervalChanged&&this._onIsActualIntervalChanged.fire();const t=this.getDrawingSyncState();null!==t&&this._syncStateChanged.fire(t)})),n(),this._lastActualZOrder=this.zOrder(),this._lastIsVisible=this.isVisible(),this._lastIsActualInterval=this.isActualInterval(),this._lastIsLocked=this.isLocked()}destroy(){this._unsubscribeFromAllLineTools(),this._group.onChanged().unsubscribeAll(this)}id(){return go(this._group.id,this._group.instanceId())}title(){return this._group.name()}gaLabel(){return"Group"}getIcon(){return{type:On.Svg,content:Po}}canBeRemoved(){return!0}canBeHidden(){return!0}canBeLocked(){return!0}canBeRenamed(){return!0}fullyConstructed(){return!0}isVisible(){return this._group.lineTools().length>0&&(this._lastIsVisible="Invisible"!==this._group.visibility()),this._lastIsVisible}isActualInterval(){return this._group.lineTools().length>0&&(this._lastIsActualInterval=this._group.lineTools().some((e=>e.isActualInterval()))),this._lastIsActualInterval}onIsActualIntervalChange(){return this._onIsActualIntervalChanged}isLocked(){return this._group.lineTools().length>0&&(this._lastIsLocked="Locked"===this._group.locked()),this._lastIsLocked}onVisibilityChanged(){return this._onVisibilityChanged}onLockChanged(){return this._onLockChanged}setVisible(e){this._undoModel.lineToolsGroupController().setGroupVisibility(this._group,e)}setLocked(e){this._undoModel.lineToolsGroupController().setGroupLock(this._group,e)}setName(e){this._undoModel.lineToolsGroupController().setGroupName(this._group,e)}isCopiable(){return!1}isClonable(){return!1}zOrder(){return this._group.lineTools().length>0&&(this._lastActualZOrder=this._group.lineTools()[0].zorder()),this._lastActualZOrder}remove(){this._undoModel.lineToolsGroupController().removeGroup(this._group)}canBeAddedToSelection(){const e=this._undoModel.model();return this._lineTools.every((t=>e.selection().canBeAddedToSelection(t)))}setAsSelection(){this._undoModel.model().selectionMacro((e=>{e.clearSelection(),this._lineTools.forEach((t=>e.addSourceToSelection(t)))}))}addToSelection(){this._undoModel.model().selectionMacro((e=>{this._lineTools.forEach((t=>e.addSourceToSelection(t)))}))}addSourcesToArray(e){return e.push(...this._lineTools),e}detachFromParent(){}insertSourcesBeforeThis(e){const t=this._insertBeforeTarget();this._insertSources(e,(e=>this._undoModel.insertBefore(e,t)))}insertSourcesAfterThis(e){const t=this._insertAfterTarget();this._insertSources(e,(e=>this._undoModel.insertAfter(e,t)))}childrenIds(){const e=[...this._lineTools];return e.sort(((e,t)=>t.zorder()-e.zorder())),new Set(e.map((e=>go(e.id(),e.instanceId()))))}hasChildren(){return!0}pane(){return(0,M.ensureDefined)(this._undoModel.model().panes().find((e=>e.id()===this._paneId)))}allowsMovingBetweenPanes(){return!1}canBeAddedToGroup(){return!1}canInsertBeforeThis(e){return this._canInsertBeforeOrAfter(e)}canInsertAfterThis(e){
return this._canInsertBeforeOrAfter(e)}canBeSyncedInLayout(){return this._lineTools.length>0&&this._lineTools[0].isSynchronizable()}onSyncStateChanged(){return this._syncStateChanged}setDrawingSyncState(e){if(this.canBeSyncedInLayout())switch(e){case 0:const e=this._lineTools.filter((e=>null!==e.linkKey().value()));e.length>0&&this._undoModel.unlinkLines(e);break;case 1:const t=this._lineTools.filter((e=>null===e.linkKey().value()));t.length>0&&this._undoModel.shareLineTools(t,1)}}getDrawingSyncState(){if(!this.canBeSyncedInLayout())return null;if(Qo){const e=this._lineTools[0]?.sharingMode().value();if(void 0===e)return null;let t=e;if(0!==t)for(const e of this._lineTools)if(e.sharingMode().value()!==t){t=0;break}return qo(t)}return this._lineTools.every((e=>null!==e.linkKey().value()))?1:0}modificationDateChanged(){return Zo}getModificationDate(){return null}_linkKeyChanged(){this._syncStateChanged.fire((0,M.ensureNotNull)(this.getDrawingSyncState()))}_canInsertBeforeOrAfter(e){return Go(this.pane(),e)}_insertSources(e,t){this._undoModel.beginUndoMacro(Vo);const n=e.reduce(((e,t)=>t.addSourcesToArray(e)),[]);e.forEach((e=>e.detachFromParent())),t(n),this._undoModel.endUndoMacro()}_insertBeforeTarget(){return(0,M.ensureNotNull)(this._lineTools.reduce(((e,t)=>null===e?t:e.zorder()<t.zorder()?e:t),null))}_insertAfterTarget(){return(0,M.ensureNotNull)(this._lineTools.reduce(((e,t)=>null===e?t:e.zorder()>t.zorder()?e:t),null))}_unsubscribeFromAllLineTools(){this._lineTools.forEach((e=>{e.properties().visible.unsubscribeAll(this),e.properties().frozen.unsubscribeAll(this),e.onIsActualIntervalChange().unsubscribeAll(this),e.linkKey().unsubscribe(this._linkKeyChangedBound),e.sharingMode().unsubscribe(this._linkKeyChangedBound)}))}}class Jo{constructor(e){this._hoveredObjectChanged=new mo.Delegate,this._entitiesCache=new Map,this._undoModel=e,this._undoModel.model().hoveredSourceChanged().subscribe(this,this._onModelHoveredSourceChanged)}destroy(){for(const e of this._entitiesCache.values())e?.destroy();this._undoModel.model().hoveredSourceChanged().unsubscribe(this,this._onModelHoveredSourceChanged)}getObjectById(e){if(this._entitiesCache.has(e))return(0,M.ensureDefined)(this._entitiesCache.get(e));const t=this._createObjectById(e);return this._entitiesCache.set(e,t),t}invalidateCache(e){Array.from(this._entitiesCache.keys()).forEach((t=>{e.has(t)||(this._entitiesCache.get(t)?.destroy(),this._entitiesCache.delete(t))}))}canBeGroupped(e){if(0===e.length||1===e.length&&e[0].hasChildren())return!1;const t=[];if(e.forEach((e=>e.addSourcesToArray(t))),t.some((e=>!(0,Eo.isLineTool)(e)||!e.boundToSymbol())))return!1;const n=this._undoModel.model(),o=t.map((e=>n.paneForSource(e)));if(new Set(o).size>1)return!1;if(!Qo)return!0;const r=t.map((e=>e.sharingMode().value()));return 1===new Set(r).size}contextMenuActions(e,t,n){const o=new Ro.ActionsProvider(e,n),r=[];return t.forEach((e=>e.addSourcesToArray(r))),o.contextMenuActionsForSources(r,(0,M.ensureNotNull)(this._undoModel.paneForSource(r[0])))}insertBefore(e,t){
t.insertSourcesAfterThis(e)}insertAfter(e,t){t.insertSourcesBeforeThis(e)}setHoveredObject(e){const t=this._undoModel.model();if(null===e)return void t.setHoveredSource(null,null);const n=t.dataSourceForId(e);null!==n&&t.setHoveredSource(n,null)}hoveredObjectId(){return Uo(this._undoModel.model().hoveredSource())}hoveredObjectChanged(){return this._hoveredObjectChanged}_onModelHoveredSourceChanged(e){this._hoveredObjectChanged.fire(Uo(e))}_createObjectById(e){const t=vo(e).persistentId,n=this._undoModel.model(),o=n.dataSourceForId(t);if(null!==o)return new Yo(this._undoModel,o);const r=n.lineToolsGroupModel().groupForId(t);return null!==r?new $o(this._undoModel,r):null}}class Xo extends Error{constructor(){super("CancelToken")}}function er(e){let t=!1;return{promise:new Promise(((n,o)=>{e.then((e=>t?o(new Xo):n(e))),e.catch((e=>o(t?new Xo:e)))})),cancel(){t=!0}}}var tr=n(45579),nr=n(40443),or=n(29023),rr=n(81199),ir=n(98017),sr=n(55744),ar=n(97874),lr=n(2872),cr=n(84959),ur=n(91730),dr=n(93544);const hr=(0,s.getLogger)("Platform.GUI.ObjectTree");var pr;!function(e){e[e.Up=0]="Up",e[e.Down=1]="Down"}(pr||(pr={}));const mr=new wo.TranslatedString("move objects",p.t(null,void 0,n(36044))),fr=new wo.TranslatedString("lock objects",p.t(null,void 0,n(18942))),gr=new wo.TranslatedString("unlock objects",p.t(null,void 0,n(23230))),vr=new wo.TranslatedString("show objects",p.t(null,void 0,n(23771))),br=new wo.TranslatedString("hide objects",p.t(null,void 0,n(93277))),_r=new wo.TranslatedString("remove objects",p.t(null,void 0,n(79688)));class yr{constructor(e){this._nodes={},this._onChange=new mo.Delegate,this._onGroupCreated=new mo.Delegate,this._subscriptions=[],this._removeSourcesPromise=null,this._timeout=null,this._objects=[],this._options={general:!0,mainSeries:!0,mainSeriesTrade:!0,esdStudies:!0,fundamentals:!0,studies:!0,lineTools:!0,publishedCharts:!0,ordersAndPositions:!0,alerts:!1,chartEvents:!0,objectTree:!1,gotoLineTool:!0},this._isContextMenuOpened=new Co.WatchedValue(!1),this._getObjectsToModify=e=>{const t=this.selection().selected();return t.find((t=>t===e))?t.map(this._ensuredEntity):[this._ensuredEntity(e)]},this._onActiveChartChanged=()=>{this._cleanup(),this._init()},this._cleanup=()=>{null!==this._timeout&&(clearTimeout(this._timeout),this._timeout=null),this._subscriptions.forEach((e=>{e.unsubscribeAll(this)})),this._selection.destroy(),this._chart.unsubscribe(this._onActiveChartChanged),null!==this._removeSourcesPromise&&this._removeSourcesPromise.cancel(),this._facade.destroy()},this._init=()=>{const e=this._chart.value();e.hasModel()&&(this._controller=e.model(),this._groupController=this._controller.lineToolsGroupController(),this._model=this._controller.model(),this._groupModel=this._model.lineToolsGroupModel(),this._facade=new Jo(this._controller),
this._subscriptions=[this._model.mainSeries().onStyleChanged(),this._model.mainSeries().dataEvents().symbolResolved(),this._model.mainSeries().onIntervalChanged(),this._model.panesCollectionChanged(),this._model.dataSourceCollectionChanged(),this._groupModel.onChanged()],this._subscriptions.forEach((e=>{e.subscribe(this,this._update)})),this._chart.subscribe(this._onActiveChartChanged),this._selection=new bo(this._model),this._update())},this._update=()=>{null===this._timeout&&(this._timeout=setTimeout((()=>{this._recalculateTree(),this._onChange.fire(),this._timeout=null})))},this._ensuredEntity=e=>(0,M.ensureNotNull)(this._getEntityById(e)),this._chart=e,this._init()}destroy(){this._cleanup()}getState(){return{nodes:Object.values(this._nodes),selection:this._selection.selected()}}getChartId(){return this._chart.value().id()}insertSelection(e,t){const n=this._facade,o=this.selection().selected().map(this._ensuredEntity),[r,i]=this._normalizeTargetAndDropType(e,t);this._controller.withMacro(mr,(()=>{switch(i){case"before":n.insertBefore(o,r);break;case"after":n.insertAfter(o,r)}})),this._update()}entity(e){return this._facade.getObjectById(e)}isMain(e){return vo(e.id()).persistentId===this._controller.mainSeries().id()}selection(){return this._selection}setIsLocked(e,t){const n=this._getObjectsToModify(e),o=t?fr:gr;this._controller.withMacro(o,(()=>{for(const e of n)e.setLocked(t)})),yn("Lock",Sn(n))}setIsVisible(e,t){const n=this._getObjectsToModify(e),o=t?vr:br;this._controller.withMacro(o,(()=>{for(const e of n)e.setVisible(t)})),yn("Hide",Sn(n))}remove(e){const t=e=>{this._controller.withMacro(_r,(()=>{for(const t of n)t.isLocked()&&!e||t.remove()})),yn("Delete",Sn(n)),this._update()},n=this._getObjectsToModify(e),o=n.some((e=>!1)),r=n.some((e=>e.isLocked()));o?(null!==this._removeSourcesPromise&&this._removeSourcesPromise.cancel(),this._removeSourcesPromise=er(confirmDatasourceRemoving(r)),this._removeSourcesPromise.promise.then((e=>{e&&(this._removeSourcesPromise=null,t(!0))}))):r?(this._removeSourcesPromise?.cancel(),this._removeSourcesPromise=er((0,sr.confirmRemovingLockedLineTools)(sr.DeleteLockedLineToolReason.RemoveSelected)),this._removeSourcesPromise.promise.then((e=>{t(e)}))):t(!0)}canSelectionBeGrouped(){const e=this._getSelectedEntities();return this._facade.canBeGroupped(e)}createGroupFromSelection(){const e=this._groupController.createGroupFromSelection();yn("Create Group");const t=go(e.id,e.instanceId());this.selection().set([this._ensuredEntity(t)]),this._onGroupCreated.fire(t),this._update()}isSelectionDropable(e,t){const n=this.selection().selected().map(this._ensuredEntity),[o,r]=this._normalizeTargetAndDropType(e,t);switch(r){case"after":return o.canInsertAfterThis(n);case"before":return o.canInsertBeforeThis(n)}}onChange(){return this._onChange}onGroupCreated(){return this._onGroupCreated}isSelectionCloneable(){const e=this._getSelectedEntities();return e.length>0&&e.every((e=>e.isClonable()))}isSelectionCopiable(){const e=this._getSelectedEntities()
;return e.length>0&&e.every((e=>e.isCopiable()))}openProperties(e,t){const n=this._model.dataSourceForId(vo(e.id()).persistentId);this.selection().selected().length>1&&this.selection().selected().includes(e.id())?this._chart.value().showSelectedSourcesProperties(t):(this.selection().set([e]),null!==n?this._controller.mainSeries()===n?this._chart.value().showGeneralChartProperties(void 0,{shouldReturnFocus:!0}):((0,Eo.isLineTool)(n)||(0,To.isStudy)(n))&&this._chart.value().showChartPropertiesForSource(n,t,{shouldReturnFocus:!0}):this._chart.value().showChartPropertiesForSources({sources:this._chart.value().model().selection().lineDataSources(),title:e.title().value(),tabName:t,renamable:!0}))}canSelectionBeUnmerged(){const e=this._getSelectedEntities();return 1===e.length&&this.canNodeWithIdBeUnmerged(vo(e[0].id()).persistentId)}canNodeWithIdBeUnmerged(e){const t=this._model.dataSourceForId(e);return null!==t&&(0,tr.isPriceDataSource)(t)&&this._model.isUnmergeAvailableForSource(t)}unmergeSelectionUp(){this._unmergeSelection(0)}unmergeSelectionDown(){this._unmergeSelection(1)}copySelection(){const e=this._getSelectedEntities(),t=e.map((e=>(0,M.ensureNotNull)(this._model.dataSourceForId(vo(e.id()).persistentId))));this._chart.value().chartWidgetCollection().clipboard.uiRequestCopy(t),yn("Copy",Sn(e))}cloneSelection(){const e=this._getSelectedEntities(),t=e.map((e=>(0,M.ensureNotNull)(this._model.dataSourceForId(vo(e.id()).persistentId))));t.every(Eo.isLineTool)&&(this._controller.cloneLineTools([...t],!1),yn("Clone",Sn(e)))}rename(e,t){const n=this._getObjectsToModify(e.id());1===n.length&&n.some((e=>e.canBeRenamed()))&&(t(),yn("Rename",Sn(n)))}async openContextMenu(e,t,n){this._objects=this._getObjectsToModify(e.id());const o=this._facade.canBeGroupped(this._objects);let r;if(this._objects.some((e=>e.hasChildren())))r=this._getActionsForGroupItem(e,t,o);else{const e=await this._facade.contextMenuActions(this._chart.value(),this._objects,this._options);if(r=Array.from(e).filter(((e,t,n)=>"separator"!==e.type||!n[t+1]||"separator"!==n[t+1].type)),1===this._objects.length&&this._objects[0].canBeRenamed()){const e=r.findIndex((e=>"Copy"===e.id));r.splice(-1===e?r.length:e+1,0,this._getRenameAction(t))}const n=r.findIndex((e=>"Clone"===e.id)),i=r[n];if(-1!==n&&"action"===i.type&&i.update({shortcutHint:void 0}),o){const e=this._getGroupAction();if(-1!==n)r.splice(n,0,e);else{const t=r.findIndex((e=>"Copy"===e.id));r.splice(-1===t?0:t,0,e)}}}if(r.length>0){const t=vo(e.id()).persistentId,o=this._model.dataSourceForId(t),i=o instanceof ir.Series,s=0!==e.childrenIds().size;let a;a=i?{menuName:"ObjectTreeContextMenu",detail:{type:"series",id:o.instanceId()}}:(0,Eo.isLineTool)(o)?{menuName:"ObjectTreeContextMenu",detail:{type:"shape",id:o?.id()??null}}:s?{menuName:"ObjectTreeContextMenu",detail:{type:"groupOfShapes",id:t||null}}:{menuName:"ObjectTreeContextMenu",detail:{type:"study",id:o?.id()||null}},nr.ContextMenuManager.showMenu(r,n,{takeFocus:!0,returnFocus:!0},a,(()=>{this._isContextMenuOpened.setValue(!1)})).then((()=>{
this._isContextMenuOpened.setValue(!0)}))}}setHoveredObject(e){const t=e?vo(e).persistentId:null;this._facade.setHoveredObject(t)}hoveredObjectChanged(){return this._facade.hoveredObjectChanged()}getNextNodeIdAfterRemove(e){const{nodes:t}=this.getState(),n=vo(e).persistentId,o=t.find((t=>t.id===e)),r=this.entity(e);if(!(o&&o.parentId&&r&&r.canBeRemoved()))return null;if(r.pane().mainDataSource()?.id()===n&&!this.canNodeWithIdBeUnmerged(n)){const e=t.filter((e=>0===e.level)).map((e=>e.id)),n=this._takeNextOrPrevElement(e,o.parentId);return(0,M.ensureDefined)(t.find((e=>e.id===n))).children[0]}const i=(0,M.ensureDefined)(t.find((e=>e.id===o.parentId))).children;return 1===i.length?this.getNextNodeIdAfterRemove(o.parentId):this._takeNextOrPrevElement(i,e)}isContextMenuOpened(){return this._isContextMenuOpened.readonly()}getChartLayout(){return this._chart.value().chartWidgetCollection().layout}_takeNextOrPrevElement(e,t){const n=e.indexOf(t);return e[n===e.length-1?n-1:n+1]}_getGroupAction(){return new rr.ActionWithStandardIcon({actionId:"ObjectsTree.CreateGroup",options:{label:jn,iconId:"ObjectsTree.CreateGroup",onExecute:()=>{this.createGroupFromSelection()}}})}_getRenameAction(e){return new rr.ActionWithStandardIcon({actionId:"ObjectsTree.RenameItem",options:{label:Vn,iconId:"ObjectsTree.RenameItem",onExecute:()=>{e(),yn("Context menu rename",Sn(this._objects))}}})}_getActionsForGroupItem(e,t,n){const o=[];this._objects.forEach((e=>e.addSourcesToArray(o)));const r=[];1===this._objects.length&&r.unshift(this._getRenameAction(t),new or.Separator),n&&r.unshift(this._getGroupAction(),new or.Separator);const i=(0,Ro.createSyncDrawingActions)(this._chart.value(),o.filter(Eo.isLineTool));i.length&&r.push(...i,new or.Separator);const s=this._chart.value().actions().format.getState();return r.push(new or.Action({actionId:"ObjectsTree.ToggleItemLocked",options:{label:e.isLocked()?Gn:Un,icon:e.isLocked()?ar:lr,onExecute:()=>this.setIsLocked(e.id(),!e.isLocked())}}),new or.Action({actionId:"ObjectsTree.ToggleItemVisibility",options:{label:e.isVisible()?Kn:Zn,icon:e.isVisible()?cr:ur,onExecute:()=>this.setIsVisible(e.id(),!e.isVisible())}}),new or.Action({actionId:"ObjectsTree.RemoveItem",options:{label:Qn,icon:dr,onExecute:()=>this.remove(e.id()),hotkeyHash:I.isMacKeyboard?8:46}}),new or.Separator,new or.Action({actionId:s.actionId,options:{label:s.label,icon:s.icon,onExecute:()=>this.openProperties(e)}})),r}_unmergeSelection(e){const t=this._getSelectedEntities();if(1!==t.length)throw new Error("Only one object can be unmerged");const n=t[0],o=(0,M.ensureNotNull)(this._model.dataSourceForId(vo(n.id()).persistentId));if(!(0,tr.isPriceDataSource)(o))throw new Error("Entity is not IPriceDataSource");(0===e?this._controller.unmergeSourceUp:this._controller.unmergeSourceDown).call(this._controller,o);yn(0===e?"New pane above":"New pane below",Sn([n]))}_recalculateTree(){const e=new _o(this._controller,this._facade);this._nodes=e.buildTree()}_normalizeTargetAndDropType(e,t){let n=this._ensuredEntity(e)
;return"inside"===t&&(t="before",n=(0,M.ensureNotNull)(this.entity([...n.childrenIds()].shift()||""))),[n,t]}_getSelectedEntities(){const{selected:e,removed:t}=this._selection.selected().reduce(((e,t)=>{const n=this._getEntityById(t);return n?(e.selected.push(n),e):(e.removed.push(t),e)}),{selected:[],removed:[]});return t.length&&hr.logWarn(`Detected dangling sources in selection. They will be ignored: ${JSON.stringify(t)}`),e}_getEntityById(e){return this._facade.getObjectById(e)}}var Sr=n(29280),wr=n(87896);class Cr extends Sr.DialogRenderer{constructor(){super(),this._handleClose=()=>{this._rootInstance?.unmount(),this._setVisibility(!1),null!==this._viewModel&&(this._viewModel.destroy(),this._viewModel=null)};const e=(0,ho.service)(po.CHART_WIDGET_COLLECTION_SERVICE);this._activeChartWidget=e.activeChartWidget.value(),this._viewModel=new yr(e.activeChartWidget)}hide(){this._handleClose()}isVisible(){return this.visible().value()}show(){this.isVisible()||h().then((()=>{null!==this._viewModel&&(this._rootInstance=(0,wr.createReactRoot)(o.createElement(co,{onClose:this._handleClose,viewModel:this._viewModel,activeChartWidget:this._activeChartWidget}),this._container),this._setVisibility(!0))}))}}},37558:(e,t,n)=>{"use strict";n.d(t,{DrawerContext:()=>s,DrawerManager:()=>i});var o=n(50959),r=n(99054);class i extends o.PureComponent{constructor(e){super(e),this._isBodyFixed=!1,this._addDrawer=e=>{this.setState((t=>({stack:[...t.stack,e]})))},this._removeDrawer=e=>{this.setState((t=>({stack:t.stack.filter((t=>t!==e))})))},this.state={stack:[]}}componentDidUpdate(e,t){!t.stack.length&&this.state.stack.length&&((0,r.setFixedBodyState)(!0),this._isBodyFixed=!0),t.stack.length&&!this.state.stack.length&&this._isBodyFixed&&((0,r.setFixedBodyState)(!1),this._isBodyFixed=!1)}componentWillUnmount(){this.state.stack.length&&this._isBodyFixed&&(0,r.setFixedBodyState)(!1)}render(){return o.createElement(s.Provider,{value:{addDrawer:this._addDrawer,removeDrawer:this._removeDrawer,currentDrawer:this.state.stack.length?this.state.stack[this.state.stack.length-1]:null}},this.props.children)}}const s=o.createContext(null)},41590:(e,t,n)=>{"use strict";n.d(t,{Drawer:()=>p});var o=n(50959),r=n(50151),i=n(97754),s=n(92184),a=n(42842),l=n(37558),c=n(29197),u=n(86656),d=n(36718);var h;function p(e){const{position:t="Bottom",onClose:n,children:u,reference:h,className:p,theme:f=d}=e,g=(0,r.ensureNotNull)((0,o.useContext)(l.DrawerContext)),[v]=(0,o.useState)((()=>(0,s.randomHash)())),b=(0,o.useRef)(null),_=(0,o.useContext)(c.CloseDelegateContext);return(0,o.useLayoutEffect)((()=>((0,r.ensureNotNull)(b.current).focus({preventScroll:!0}),_.subscribe(g,n),g.addDrawer(v),()=>{g.removeDrawer(v),_.unsubscribe(g,n)})),[]),o.createElement(a.Portal,null,o.createElement("div",{ref:h,className:i(d.wrap,d[`position${t}`])},v===g.currentDrawer&&o.createElement("div",{className:d.backdrop,onClick:n}),o.createElement(m,{className:i(f.drawer,d[`position${t}`],p),ref:b,"data-name":e["data-name"]},u)))}!function(e){e.Left="Left",e.Bottom="Bottom"}(h||(h={}))
;const m=(0,o.forwardRef)(((e,t)=>{const{className:n,...r}=e;return o.createElement(u.TouchScrollContainer,{className:i(d.drawer,n),tabIndex:-1,ref:t,...r})}))},71402:(e,t,n)=>{"use strict";n.d(t,{RemoveTitleType:()=>o,removeTitlesMap:()=>i});var o,r=n(11542);!function(e){e.Add="add",e.Remove="remove"}(o||(o={}));const i={[o.Add]:r.t(null,void 0,n(69207)),[o.Remove]:r.t(null,void 0,n(85106))}},36189:(e,t,n)=>{"use strict";n.d(t,{FavoriteButton:()=>h});var o=n(50959),r=n(97754),i=n.n(r),s=n(9745),a=n(71402),l=n(74670),c=n(39146),u=n(48010),d=n(22413);function h(e){const{className:t,isFilled:n,isActive:r,onClick:h,title:p,...m}=e,[f,g]=(0,l.useActiveDescendant)(null),v=p??(n?a.removeTitlesMap[a.RemoveTitleType.Remove]:a.removeTitlesMap[a.RemoveTitleType.Add]);return(0,o.useLayoutEffect)((()=>{const e=f.current;e instanceof HTMLElement&&v&&e.dispatchEvent(new CustomEvent("common-tooltip-update"))}),[v,f]),o.createElement(s.Icon,{...m,className:i()(d.favorite,"apply-common-tooltip",n&&d.checked,r&&d.active,g&&d.focused,t),onClick:h,icon:n?c:u,title:v,ariaLabel:v,ref:f})}},898:(e,t,n)=>{"use strict";n.d(t,{useDimensions:()=>i});var o=n(50959),r=n(67842);function i(e){const[t,n]=(0,o.useState)(null),i=(0,o.useCallback)((([e])=>{const o=e.target.getBoundingClientRect();o.width===t?.width&&o.height===t.height||n(o)}),[t]);return[(0,r.useResizeObserver)({callback:i,ref:e}),t]}},36947:(e,t,n)=>{"use strict";n.d(t,{useForceUpdate:()=>o.useForceUpdate});var o=n(125)},70412:(e,t,n)=>{"use strict";n.d(t,{hoverMouseEventFilter:()=>i,useAccurateHover:()=>s,useHover:()=>r});var o=n(50959);function r(){const[e,t]=(0,o.useState)(!1);return[e,{onMouseOver:function(e){i(e)&&t(!0)},onMouseOut:function(e){i(e)&&t(!1)}}]}function i(e){return!e.currentTarget.contains(e.relatedTarget)}function s(e){const[t,n]=(0,o.useState)(!1);return(0,o.useEffect)((()=>{const t=t=>{if(null===e.current)return;const o=e.current.contains(t.target);n(o)};return document.addEventListener("mouseover",t),()=>document.removeEventListener("mouseover",t)}),[]),t}},33127:(e,t,n)=>{"use strict";n.d(t,{useOverlayScroll:()=>l});var o=n(50959),r=n(50151),i=n(70412),s=n(49483);const a={onMouseOver:()=>{},onMouseOut:()=>{}};function l(e,t=s.CheckMobile.any()){const n=(0,o.useRef)(null),l=e||(0,o.useRef)(null),[c,u]=(0,i.useHover)(),[d,h]=(0,o.useState)({reference:n,containerHeight:0,containerWidth:0,contentHeight:0,contentWidth:0,scrollPosTop:0,scrollPosLeft:0,onVerticalChange:function(e){h((t=>({...t,scrollPosTop:e}))),(0,r.ensureNotNull)(l.current).scrollTop=e},onHorizontalChange:function(e){h((t=>({...t,scrollPosLeft:e}))),(0,r.ensureNotNull)(l.current).scrollLeft=e},visible:c}),p=(0,o.useCallback)((()=>{if(!l.current)return;const{clientHeight:e,scrollHeight:t,scrollTop:o,clientWidth:r,scrollWidth:i,scrollLeft:s}=l.current,a=n.current?n.current.offsetTop:0;h((n=>({...n,containerHeight:e-a,contentHeight:t-a,scrollPosTop:o,containerWidth:r,contentWidth:i,scrollPosLeft:s})))}),[]);function m(){h((e=>({...e,scrollPosTop:(0,r.ensureNotNull)(l.current).scrollTop,
scrollPosLeft:(0,r.ensureNotNull)(l.current).scrollLeft})))}return(0,o.useEffect)((()=>{c&&p(),h((e=>({...e,visible:c})))}),[c]),(0,o.useEffect)((()=>{const e=l.current;return e&&e.addEventListener("scroll",m),()=>{e&&e.removeEventListener("scroll",m)}}),[l]),[d,t?a:u,l,p]}},77975:(e,t,n)=>{"use strict";n.d(t,{useWatchedValueReadonly:()=>r});var o=n(50959);const r=(e,t=!1,n=[])=>{const r="watchedValue"in e?e.watchedValue:void 0,i="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[s,a]=(0,o.useState)(r?r.value():i);return(t?o.useLayoutEffect:o.useEffect)((()=>{if(r){a(r.value());const e=e=>a(e);return r.subscribe(e),()=>r.unsubscribe(e)}return()=>{}}),[r,...n]),s}},90692:(e,t,n)=>{"use strict";n.d(t,{MatchMedia:()=>r});var o=n(50959);class r extends o.PureComponent{constructor(e){super(e),this._handleChange=()=>{this.forceUpdate()},this.state={query:window.matchMedia(this.props.rule)}}componentDidMount(){this._subscribe(this.state.query)}componentDidUpdate(e,t){this.state.query!==t.query&&(this._unsubscribe(t.query),this._subscribe(this.state.query))}componentWillUnmount(){this._unsubscribe(this.state.query)}render(){return this.props.children(this.state.query.matches)}static getDerivedStateFromProps(e,t){return e.rule!==t.query.media?{query:window.matchMedia(e.rule)}:null}_subscribe(e){e.addEventListener("change",this._handleChange)}_unsubscribe(e){e.removeEventListener("change",this._handleChange)}}},64706:(e,t,n)=>{"use strict";n.d(t,{MenuContext:()=>o});const o=n(50959).createContext(null)},27317:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_MENU_THEME:()=>g,Menu:()=>b});var o=n(50959),r=n(97754),i=n.n(r),s=n(50151),a=n(9859),l=n(14729),c=n(50655),u=n(59064),d=n(67961),h=n(26709),p=n(83021),m=n(64706),f=n(67797);const g=f;var v;!function(e){e[e.IndentFromWindow=0]="IndentFromWindow"}(v||(v={}));class b extends o.PureComponent{constructor(e){super(e),this._containerRef=null,this._scrollWrapRef=null,this._raf=null,this._scrollRaf=null,this._scrollTimeout=void 0,this._manager=new d.OverlapManager,this._hotkeys=null,this._scroll=0,this._handleContainerRef=e=>{this._containerRef=e,this.props.reference&&("function"==typeof this.props.reference&&this.props.reference(e),"object"==typeof this.props.reference&&(this.props.reference.current=e))},this._handleScrollWrapRef=e=>{this._scrollWrapRef=e,"function"==typeof this.props.scrollWrapReference&&this.props.scrollWrapReference(e),"object"==typeof this.props.scrollWrapReference&&(this.props.scrollWrapReference.current=e)},this._handleCustomRemeasureDelegate=()=>{this._resizeForced(),this._handleMeasure()},this._handleMeasure=({callback:e,forceRecalcPosition:t}={})=>{if(this.state.isMeasureValid&&!t)return;const{position:n}=this.props,o=(0,s.ensureNotNull)(this._containerRef);let r=o.getBoundingClientRect();const i=document.documentElement.clientHeight,l=document.documentElement.clientWidth,c=this.props.closeOnScrollOutsideOffset??0;let u=i-0-c;const d=r.height>u;if(d){(0,s.ensureNotNull)(this._scrollWrapRef).style.overflowY="scroll",r=o.getBoundingClientRect()}
const{width:h,height:p}=r,m="function"==typeof n?n({contentWidth:h,contentHeight:p,availableWidth:l,availableHeight:i}):n,f=m?.indentFromWindow?.left??0,g=l-(m.overrideWidth??h)-(m?.indentFromWindow?.right??0),v=(0,a.clamp)(m.x,f,Math.max(f,g)),b=(m?.indentFromWindow?.top??0)+c,_=i-(m.overrideHeight??p)-(m?.indentFromWindow?.bottom??0);let y=(0,a.clamp)(m.y,b,Math.max(b,_));if(m.forbidCorrectYCoord&&y<m.y&&(u-=m.y-y,y=m.y),t&&void 0!==this.props.closeOnScrollOutsideOffset&&m.y<=this.props.closeOnScrollOutsideOffset)return void this._handleGlobalClose(!0);const S=m.overrideHeight??(d?u:void 0);this.setState({appearingMenuHeight:t?this.state.appearingMenuHeight:S,appearingMenuWidth:t?this.state.appearingMenuWidth:m.overrideWidth,appearingPosition:{x:v,y},isMeasureValid:!0},(()=>{this.props.doNotRestorePosition||this._restoreScrollPosition(),e&&e()}))},this._restoreScrollPosition=()=>{const e=document.activeElement,t=(0,s.ensureNotNull)(this._containerRef);if(null!==e&&t.contains(e))try{e.scrollIntoView()}catch(e){}else(0,s.ensureNotNull)(this._scrollWrapRef).scrollTop=this._scroll},this._resizeForced=()=>{this.setState({appearingMenuHeight:void 0,appearingMenuWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0})},this._resize=()=>{null===this._raf&&(this._raf=requestAnimationFrame((()=>{this.setState({appearingMenuHeight:void 0,appearingMenuWidth:void 0,appearingPosition:void 0,isMeasureValid:void 0}),this._raf=null})))},this._handleGlobalClose=e=>{this.props.onClose(e)},this._handleSlot=e=>{this._manager.setContainer(e)},this._handleScroll=()=>{this._scroll=(0,s.ensureNotNull)(this._scrollWrapRef).scrollTop},this._handleScrollOutsideEnd=()=>{clearTimeout(this._scrollTimeout),this._scrollTimeout=setTimeout((()=>{this._handleMeasure({forceRecalcPosition:!0})}),80)},this._handleScrollOutside=e=>{e.target!==this._scrollWrapRef&&(this._handleScrollOutsideEnd(),null===this._scrollRaf&&(this._scrollRaf=requestAnimationFrame((()=>{this._handleMeasure({forceRecalcPosition:!0}),this._scrollRaf=null}))))},this.state={}}componentDidMount(){this._handleMeasure({callback:this.props.onOpen});const{customCloseDelegate:e=u.globalCloseDelegate,customRemeasureDelegate:t}=this.props;e.subscribe(this,this._handleGlobalClose),t?.subscribe(null,this._handleCustomRemeasureDelegate),window.addEventListener("resize",this._resize);const n=null!==this.context;this._hotkeys||n||(this._hotkeys=h.createGroup({desc:"Popup menu"}),this._hotkeys.add({desc:"Close",hotkey:27,handler:()=>{this.props.onKeyboardClose&&this.props.onKeyboardClose(),this._handleGlobalClose()}})),this.props.repositionOnScroll&&window.addEventListener("scroll",this._handleScrollOutside,{capture:!0})}componentDidUpdate(){this._handleMeasure()}componentWillUnmount(){const{customCloseDelegate:e=u.globalCloseDelegate,customRemeasureDelegate:t}=this.props;e.unsubscribe(this,this._handleGlobalClose),t?.unsubscribe(null,this._handleCustomRemeasureDelegate),window.removeEventListener("resize",this._resize),window.removeEventListener("scroll",this._handleScrollOutside,{
capture:!0}),this._hotkeys&&(this._hotkeys.destroy(),this._hotkeys=null),null!==this._raf&&(cancelAnimationFrame(this._raf),this._raf=null),null!==this._scrollRaf&&(cancelAnimationFrame(this._scrollRaf),this._scrollRaf=null),this._scrollTimeout&&clearTimeout(this._scrollTimeout)}render(){const{id:e,role:t,"aria-label":n,"aria-labelledby":r,"aria-activedescendant":s,"aria-hidden":a,"aria-describedby":u,"aria-invalid":d,children:h,minWidth:g,theme:v=f,className:b,maxHeight:y,onMouseOver:S,onMouseOut:w,onKeyDown:C,onFocus:E,onBlur:T}=this.props,{appearingMenuHeight:I,appearingMenuWidth:M,appearingPosition:D,isMeasureValid:x}=this.state,N={"--ui-kit-menu-max-width":`${D&&D.x}px`,maxWidth:"calc(100vw - var(--ui-kit-menu-max-width) - 6px)"};return o.createElement(m.MenuContext.Provider,{value:this},o.createElement(p.SubmenuHandler,null,o.createElement(c.SlotContext.Provider,{value:this._manager},o.createElement("div",{id:e,role:t,"aria-label":n,"aria-labelledby":r,"aria-activedescendant":s,"aria-hidden":a,"aria-describedby":u,"aria-invalid":d,className:i()(b,v.menuWrap,!x&&v.isMeasuring),style:{height:I,left:D&&D.x,minWidth:g,position:"fixed",top:D&&D.y,width:M,...this.props.limitMaxWidth&&N},"data-name":this.props["data-name"],"data-tooltip-show-on-focus":this.props["data-tooltip-show-on-focus"],ref:this._handleContainerRef,onScrollCapture:this.props.onScroll,onContextMenu:l.preventDefaultForContextMenu,tabIndex:this.props.tabIndex,onMouseOver:S,onMouseOut:w,onKeyDown:C,onFocus:E,onBlur:T},o.createElement("div",{className:i()(v.scrollWrap,!this.props.noMomentumBasedScroll&&v.momentumBased),style:{overflowY:void 0!==I?"scroll":"auto",maxHeight:y},onScrollCapture:this._handleScroll,ref:this._handleScrollWrapRef},o.createElement(_,{className:v.menuBox},h)))),o.createElement(c.Slot,{reference:this._handleSlot})))}update(e){e?this._resizeForced():this._resize()}focus(e){this._containerRef?.focus(e)}blur(){this._containerRef?.blur()}}function _(e){const t=(0,s.ensureNotNull)((0,o.useContext)(p.SubmenuContext)),n=o.useRef(null);return o.createElement("div",{ref:n,className:e.className,onMouseOver:function(e){if(!(null!==t.current&&e.target instanceof Node&&(o=e.target,n.current?.contains(o))))return;var o;t.isSubmenuNode(e.target)||t.setCurrent(null)},"data-name":"menu-inner"},e.children)}b.contextType=p.SubmenuContext},29197:(e,t,n)=>{"use strict";n.d(t,{CloseDelegateContext:()=>i});var o=n(50959),r=n(59064);const i=o.createContext(r.globalCloseDelegate)},42842:(e,t,n)=>{"use strict";n.d(t,{Portal:()=>c,PortalContext:()=>u});var o=n(50959),r=n(32227),i=n(55698),s=n(67961),a=n(34811),l=n(99663);class c extends o.PureComponent{constructor(){super(...arguments),this._uuid=(0,i.nanoid)()}componentWillUnmount(){this._manager().removeWindow(this._uuid)}render(){const e=this._manager().ensureWindow(this._uuid,this.props.layerOptions);e.style.top=this.props.top||"",e.style.bottom=this.props.bottom||"",e.style.left=this.props.left||"",e.style.right=this.props.right||"",e.style.pointerEvents=this.props.pointerEvents||""
;const t=this.props.className;return t&&("string"==typeof t?e.classList.add(t):e.classList.add(...t)),this.props.shouldTrapFocus&&!e.hasAttribute(a.FOCUS_TRAP_DATA_ATTRIBUTE)&&e.setAttribute(a.FOCUS_TRAP_DATA_ATTRIBUTE,"true"),this.props["aria-hidden"]&&e.setAttribute("aria-hidden","true"),r.createPortal(o.createElement(u.Provider,{value:this},this.props.children),e)}moveToTop(){this._manager().moveToTop(this._uuid)}_manager(){return null===this.context?(0,s.getRootOverlapManager)():this.context}}c.contextType=l.SlotContext;const u=o.createContext(null)},96040:(e,t,n)=>{"use strict";n.d(t,{RemoveButton:()=>d});var o=n(11542),r=n(50959),i=n(97754),s=n.n(i),a=n(9745),l=n(74670),c=n(33765),u=n(35990);function d(e){const{className:t,isActive:i,onClick:d,onMouseDown:h,title:p,hidden:m,"data-name":f="remove-button",icon:g,...v}=e,[b,_]=(0,l.useActiveDescendant)(null);return r.createElement(a.Icon,{...v,"data-name":f,className:s()(u.button,"apply-common-tooltip",i&&u.active,m&&u.hidden,_&&u.focused,t),icon:g||c,onClick:d,onMouseDown:h,title:p??o.t(null,void 0,n(67410)),ariaLabel:p??o.t(null,void 0,n(67410)),ref:b})}},47308:(e,t,n)=>{"use strict";n.d(t,{RoundButtonTabs:()=>Y});var o=n(50959),r=n(97754),i=n(11542),s=n(63273),a=n(47201),l=n(35020),c=n(86240),u=n(86781);var d=n(95854),h=n(36966),p=n(7953),m=n(38528),f=n(66686);n(34869);const g=o.createContext({children:{},setIsReady:()=>{}});function v(){return!function(){const[e,t]=(0,o.useState)(!0);return(0,o.useEffect)((()=>{t(!1)}),[]),e}()}var b=n(67842);function _(e,t,n){const{id:r,items:i,activationType:s,orientation:_,disabled:y,onActivate:S,isActive:w,overflowBehaviour:C,enableActiveStateStyles:E,tablistLabelId:T,tablistLabel:I,preventDefaultIfKeyboardActionHandled:M,stopPropagationIfKeyboardActionHandled:D,keyboardNavigationLoop:x,defaultKeyboardFocus:N,focusableItemAttributes:k}=t,R=(0,l.useMobileTouchState)(),B=function(e){const t=(0,u.useSafeMatchMedia)(c["media-mf-phone-landscape"],!0),n=(0,l.useMobileTouchState)();return e??(n||!t?"scroll":"collapse")}(C),L=(0,o.useRef)(!1),O=(0,o.useCallback)((e=>e.id),[]),A=E??!R,P=function(){const{setIsReady:e,children:t}=(0,o.useContext)(g),n=(0,o.useRef)((0,o.useId)());return t[n.current]||(t[n.current]={isReady:!1}),(0,o.useCallback)((()=>{t[n.current].isReady=!0,e(Object.values(t).every((e=>e.isReady)))}),[t,e])}(),{visibleItems:F,hiddenItems:z,containerRefCallback:W,innerContainerRefCallback:H,moreButtonRef:j,setItemRef:V,itemsMeasurements:G}=(0,d.useCollapsible)(i,O,w),U=function(e){const t=(0,o.useRef)(null);return(0,o.useEffect)((()=>{t.current=e}),[e]),t.current}(G.current?.containerWidth)??0,K=v(),Z=G.current?.containerWidth??0;let Q=!1;G.current&&K&&(Q=function(e,t,n,o,r){if("collapse"!==o)return!0;const i=function(e,t,n){const o=e.filter((e=>t.find((t=>t.id===e[0]))));return t.length>0?o[0][1]+n:0}(Array.from(e.widthsMap.entries()),t,r),s=e.moreButtonWidth??0;let a=function(e,t){return e.reduce(((e,n)=>e+(t.get(n.id)??0)),0)}(n,e.widthsMap);return a+=t.length>0?s:0,function(e,t,n,o){return 0!==e?t-n<e&&t-n>o:n<t
}(i,e.containerWidth,a,r)}(G.current,z,F,B,n.gap??0)||0===Z);const q=(0,b.useResizeObserver)((([e])=>{const t=K&&0===U&&0===z.length;(Q&&e.contentRect.width===U||t)&&P()})),Y="collapse"===B?F:i,$=(0,o.useMemo)((()=>"collapse"===B?z:[]),[B,z]),J=(0,o.useCallback)((e=>$.includes(e)),[$]),{isOpened:X,open:ee,close:te,onButtonClick:ne}=(0,p.useDisclosure)({id:r,disabled:y}),{tabsBindings:oe,tablistBinding:re,scrollWrapBinding:ie,onActivate:se,onHighlight:ae,isHighlighted:le}=(0,h.useTabs)({id:r,items:[...Y,...$],activationType:s,orientation:_,disabled:y,tablistLabelId:T,tablistLabel:I,preventDefaultIfKeyboardActionHandled:M,scrollIntoViewOptions:n.scrollIntoViewOptions,onActivate:S,isActive:w,isCollapsed:J,isRtl:n.isRtl,isDisclosureOpened:X,isRadioGroup:n.isRadioGroup,stopPropagationIfKeyboardActionHandled:D,keyboardNavigationLoop:x,defaultKeyboardFocus:N,focusableItemAttributes:k}),ce=$.find(le),ue=(0,o.useCallback)((()=>{const e=i.find(w);e&&ae(e)}),[ae,w,i]),de=(0,o.useCallback)((e=>oe.find((t=>t.id===e.id))),[oe]),he=(0,o.useCallback)((()=>{te(),ue(),L.current=!0}),[te,ue]),pe=(0,o.useCallback)((()=>{ce&&(se(ce),ae(ce,250))}),[se,ae,ce]);ie.ref=(0,m.useMergedRefs)([q,ie.ref,W]),re.ref=(0,m.useMergedRefs)([re.ref,H]),re.onKeyDown=(0,a.createSafeMulticastEventHandler)((0,f.useKeyboardEventHandler)([(0,f.useKeyboardClose)(X,he),(0,f.useKeyboardActionHandler)([13,32],pe,(0,o.useCallback)((()=>Boolean(ce)),[ce]))],M),re.onKeyDown);const me=(0,o.useCallback)((e=>{L.current=!0,ne(e)}),[L,ne]),fe=(0,o.useCallback)((e=>{e&&se(e)}),[se]);return(0,o.useEffect)((()=>{L.current?L.current=!1:(ce&&!X&&ee(),!ce&&X&&te())}),[ce,X,ee,te]),{enableActiveStateStyles:A,moreButtonRef:j,setItemRef:V,getBindings:de,handleMoreButtonClick:me,handleCollapsedItemClick:fe,scrollWrapBinding:ie,overflowBehaviour:B,tablistBinding:re,visibleTabs:Y,hiddenTabs:$,handleActivate:se,isMobileTouch:R,getItemId:O,isDisclosureOpened:X,isHighlighted:le,closeDisclosure:te}}var y=n(8304),S=n(53017),w=n(17946),C=n(9745),E=n(2948),T=n(90854);const I="xsmall",M="primary";function D(e){const t=(0,o.useContext)(w.CustomBehaviourContext),{size:n="xsmall",variant:i="primary",active:s,fake:a,startIcon:l,endIcon:c,showCaret:u,iconOnly:d,anchor:h,enableActiveStateStyles:p=t.enableActiveStateStyles,disableFocusOutline:m=!1,tooltip:f}=e;return r(T.roundTabButton,T[n],T[i],l&&T.withStartIcon,(c||u)&&T.withEndIcon,d&&T.iconOnly,s&&T.selected,a&&T.fake,h&&T.enableCursorPointer,!p&&T.disableActiveStateStyles,m&&T.disableFocusOutline,f&&"apply-common-tooltip")}function x(e){const{startIcon:t,endIcon:n,showCaret:i,iconOnly:s,children:a}=e;return o.createElement(o.Fragment,null,t&&o.createElement(C.Icon,{icon:t,className:T.startIconWrap,"aria-hidden":!0}),a&&o.createElement("span",{className:r(T.content,s&&T.visuallyHidden)},a),(!s&&n||i)&&o.createElement(N,{icon:n,showCaret:i}))}function N(e){const{icon:t,showCaret:n}=e;return o.createElement(C.Icon,{className:r(T.endIconWrap,n&&T.caret),icon:n?E:t,"aria-hidden":!0})}const k=(0,o.forwardRef)(((e,t)=>{
const{id:n,size:r,variant:i,active:s,fake:a,startIcon:l,endIcon:c,showCaret:u,iconOnly:d,children:h,enableActiveStateStyles:p,disableFocusOutline:m,tooltip:f,...g}=e;return o.createElement("button",{...g,id:n,ref:t,"data-tooltip":f,className:D({size:r,variant:i,active:s,fake:a,startIcon:l,endIcon:c,showCaret:u,iconOnly:d,enableActiveStateStyles:p,disableFocusOutline:m,tooltip:f})},o.createElement(x,{startIcon:l,endIcon:c,showCaret:u,iconOnly:d},h))}));k.displayName="RoundTabsBaseButton";const R=(0,o.createContext)({size:"small",variant:"primary",isHighlighted:!1,isCollapsed:!1,disabled:!1});function B(e){const{item:t,highlighted:n,handleItemRef:r,reference:i,onClick:s,"aria-disabled":a,...l}=e,c=(0,o.useCallback)((e=>{l.disabled&&e.preventDefault(),s&&s(t)}),[s,t,l.disabled]),u=(0,o.useCallback)((e=>{r&&r(t,e),(0,S.isomorphicRef)(i)(e)}),[t,r]),d={size:l.size??I,variant:l.variant??M,isHighlighted:Boolean(l.active),isCollapsed:!1,disabled:l.disabled??!1};return o.createElement(k,{...l,id:t.id,onClick:c,ref:u,startIcon:t.startIcon,endIcon:t.endIcon,tooltip:t.tooltip,"aria-label":"radio"===l.role?t.children:void 0},o.createElement(R.Provider,{value:d},t.children))}var L=n(16396),O=n(4523),A=n(16829),P=n(89882),F=n(2057),z=n(93524);function W(e){const{disabled:t,isOpened:n,enableActiveStateStyles:r,disableFocusOutline:i,fake:s,items:a,buttonText:l,buttonPreset:c="text",buttonRef:u,size:d,variant:h,isAnchorTabs:p,isHighlighted:f,onButtonClick:g,onItemClick:v,onClose:b}=e,_=(0,o.useRef)(null),y=(0,m.useMergedRefs)([u,_]),S="text"===c?void 0:"xsmall"===d?P:F;return o.createElement(O.PopupMenuDisclosureView,{buttonRef:_,listboxTabIndex:-1,isOpened:n,onClose:b,listboxAria:{"aria-hidden":!0},button:o.createElement(k,{"aria-hidden":!0,disabled:t,active:n,onClick:g,ref:y,tabIndex:-1,size:d,variant:h,startIcon:S,showCaret:"text"===c,iconOnly:"meatballs"===c,enableActiveStateStyles:r,disableFocusOutline:i,fake:s},l),popupChildren:o.createElement(o.Fragment,null,"meatballs"===c&&o.createElement(A.ToolWidgetMenuSummary,null,l),a.map((e=>o.createElement(L.PopupMenuItem,{key:e.id,className:p?z.linkItem:void 0,onClick:v,onClickArg:e,isActive:f(e),label:o.createElement(H,{isHighlighted:f(e),size:d,variant:h,disabled:e.disabled},e.children),isDisabled:e.disabled,link:"href"in e?e.href:void 0,rel:"rel"in e?e.rel:void 0,target:"target"in e?e.target:void 0,icon:e.startIcon,toolbox:e.endIcon&&o.createElement(C.Icon,{icon:e.endIcon}),renderComponent:"renderComponent"in e?e.renderComponent:void 0,dontClosePopup:!0}))))})}function H(e){const{isHighlighted:t,size:n,variant:r,children:i,disabled:s}=e,a={size:n??I,variant:r??M,isHighlighted:t,isCollapsed:!0,disabled:s??!1};return o.createElement(R.Provider,{value:a},i)}var j,V,G,U,K=n(76912);function Z(e){const{overflowBehaviour:t}=e;return r(K.scrollWrap,"scroll"===t&&K.overflowScroll,"wrap"===t&&K.overflowWrap)}function Q(e){const{align:t="start"}=e;return r(K.roundTabs,K[t])}function q(e){
const{children:t,disabled:a,moreButtonText:l=i.t(null,void 0,n(37117)),moreButtonPreset:c,className:u,size:d,variant:h,align:p,style:m={},"data-name":f,isRadioGroup:g,"aria-controls":v}=e,b=function(e="xsmall"){switch(e){case"small":return 8;case"xsmall":return 4;default:return 16}}(d),{enableActiveStateStyles:S,moreButtonRef:w,setItemRef:C,getBindings:E,handleMoreButtonClick:T,handleCollapsedItemClick:I,scrollWrapBinding:M,overflowBehaviour:D,tablistBinding:x,visibleTabs:N,hiddenTabs:k,handleActivate:R,isMobileTouch:L,getItemId:O,isDisclosureOpened:A,isHighlighted:P,closeDisclosure:F}=_(y.TabNames.RoundButtonTabs,e,{isRtl:s.isRtl,scrollIntoViewOptions:{additionalScroll:b},isRadioGroup:g,gap:b});return o.createElement("div",{...M,className:r(Z({overflowBehaviour:D}),u),style:{...m,"--ui-lib-round-tabs-gap":`${b}px`},"data-name":f},o.createElement("div",{...x,className:Q({align:p,overflowBehaviour:D})},N.map((e=>o.createElement(B,{...E(e),key:e.id,item:e,onClick:()=>R(e),variant:h,size:d,enableActiveStateStyles:S,disableFocusOutline:L,reference:C(O(e)),...e.dataId&&{"data-id":e.dataId},"aria-controls":v}))),k.map((e=>o.createElement(B,{...E(e),key:e.id,item:e,variant:h,size:d,reference:C(O(e)),"aria-controls":v,fake:!0}))),o.createElement(W,{disabled:a,isOpened:A,items:k,buttonText:l,buttonPreset:c,buttonRef:w,isHighlighted:P,onButtonClick:T,onItemClick:I,onClose:F,variant:h,size:d,enableActiveStateStyles:S,disableFocusOutline:L,fake:0===k.length}),t))}function Y(e){const{"data-name":t="round-tabs-buttons",...n}=e;return o.createElement(q,{...n,"data-name":t})}!function(e){e.Primary="primary",e.Ghost="ghost"}(j||(j={})),function(e){e.XSmall="xsmall",e.Small="small",e.Large="large"}(V||(V={})),function(e){e.Start="start",e.Center="center"}(G||(G={})),function(e){e.Text="text",e.Meatballs="meatballs"}(U||(U={}));n(21593)},50655:(e,t,n)=>{"use strict";n.d(t,{Slot:()=>o.Slot,SlotContext:()=>o.SlotContext});var o=n(99663)},63932:(e,t,n)=>{"use strict";n.d(t,{Spinner:()=>l});var o=n(50959),r=n(97754),i=n(58096),s=(n(15216),n(85862)),a=n.n(s);function l(e){const{ariaLabel:t,ariaLabelledby:n,className:s,style:l,size:c,id:u,disableSelfPositioning:d}=e;return o.createElement("div",{className:r(s,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${i.spinnerSizeMap[c||i.DEFAULT_SIZE]}`,d&&a().disableSelfPositioning),style:l,role:"progressbar",id:u,"aria-label":t,"aria-labelledby":n})}},10381:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetCaret:()=>l});var o=n(50959),r=n(97754),i=n(9745),s=n(49128),a=n(578);function l(e){const{dropped:t,className:n}=e;return o.createElement(i.Icon,{className:r(n,s.icon,{[s.dropped]:t}),icon:a})}},86656:(e,t,n)=>{"use strict";n.d(t,{TouchScrollContainer:()=>c});var o=n(50959),r=n(59142),i=n(50151),s=n(49483);const a=CSS.supports("overscroll-behavior","none");let l=0;const c=(0,o.forwardRef)(((e,t)=>{const{children:n,...i}=e,c=(0,o.useRef)(null);return(0,o.useImperativeHandle)(t,(()=>c.current)),(0,o.useLayoutEffect)((()=>{if(s.CheckMobile.iOS())return l++,
null!==c.current&&(a?1===l&&(document.body.style.overscrollBehavior="none"):(0,r.disableBodyScroll)(c.current,{allowTouchMove:u(c)})),()=>{l--,null!==c.current&&(a?0===l&&(document.body.style.overscrollBehavior=""):(0,r.enableBodyScroll)(c.current))}}),[]),o.createElement("div",{ref:c,...i},n)}));function u(e){return t=>{const n=(0,i.ensureNotNull)(e.current),o=document.activeElement;return!n.contains(t)||null!==o&&n.contains(o)&&o.contains(t)}}},26448:e=>{e.exports={accessible:"accessible-NQERJsv9",active:"active-NQERJsv9"}},38576:e=>{e.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",clicked:"clicked-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},97373:e=>{e.exports={button:"button-xNqEcuN2"}},55973:e=>{e.exports={title:"title-u3QJgF_p"}},95389:e=>{e.exports={button:"button-merBkM5y",hover:"hover-merBkM5y",clicked:"clicked-merBkM5y",accessible:"accessible-merBkM5y",arrow:"arrow-merBkM5y",arrowWrap:"arrowWrap-merBkM5y",isOpened:"isOpened-merBkM5y"}},20243:(e,t,n)=>{"use strict";n.d(t,{focusFirstMenuItem:()=>u,handleAccessibleMenuFocus:()=>l,handleAccessibleMenuKeyDown:()=>c,queryMenuElements:()=>p});var o=n(19291),r=n(57177),i=n(68335),s=n(15754);const a=[37,39,38,40];function l(e,t){if(!e.target)return;const n=e.relatedTarget?.getAttribute("aria-activedescendant");if(e.relatedTarget!==t.current){const e=n&&document.getElementById(n);if(!e||e!==t.current)return}u(e.target)}function c(e){if(e.defaultPrevented)return;const t=(0,i.hashFromEvent)(e);if(!a.includes(t))return;const n=document.activeElement;if(!(document.activeElement instanceof HTMLElement))return;const s=p(e.currentTarget).sort(o.navigationOrderComparator);if(0===s.length)return;const l=document.activeElement.closest('[data-role="menuitem"]')||document.activeElement.parentElement?.querySelector('[data-role="menuitem"]');if(!(l instanceof HTMLElement))return;const c=s.indexOf(l);if(-1===c)return;const u=m(l),f=u.indexOf(document.activeElement),g=-1!==f,v=e=>{n&&(0,r.becomeSecondaryElement)(n),(0,r.becomeMainElement)(e),e.focus()};switch((0,o.mapKeyCodeToDirection)(t)){case"inlinePrev":if(!u.length)return;e.preventDefault(),v(0===f?s[c]:g?d(u,f,-1):u[u.length-1]);break;case"inlineNext":if(!u.length)return;e.preventDefault(),f===u.length-1?v(s[c]):v(g?d(u,f,1):u[0]);break;case"blockPrev":{e.preventDefault();const t=d(s,c,-1);if(g){const e=h(t,f);v(e||t);break}v(t);break}case"blockNext":{e.preventDefault();const t=d(s,c,1);if(g){const e=h(t,f);v(e||t);break}v(t)}}}function u(e){const[t]=p(e);t&&((0,r.becomeMainElement)(t),t.focus())}function d(e,t,n){return e[(t+e.length+n)%e.length]}function h(e,t){const n=m(e);return n.length?n[(t+n.length)%n.length]:null}function p(e){return Array.from(e.querySelectorAll('[data-role="menuitem"]:not([disabled]):not([aria-disabled])')).filter((0,s.createScopedVisibleElementFilter)(e))}
function m(e){return Array.from(e.querySelectorAll("[tabindex]:not([disabled]):not([aria-disabled])")).filter((0,s.createScopedVisibleElementFilter)(e))}},57177:(e,t,n)=>{"use strict";var o;function r(e){e.dispatchEvent(new CustomEvent("roving-tabindex:main-element"))}function i(e){e.dispatchEvent(new CustomEvent("roving-tabindex:secondary-element"))}n.d(t,{becomeMainElement:()=>r,becomeSecondaryElement:()=>i}),function(e){e.MainElement="roving-tabindex:main-element",e.SecondaryElement="roving-tabindex:secondary-element"}(o||(o={}))},10838:(e,t,n)=>{"use strict";n.d(t,{AccessibleMenuItem:()=>u});var o=n(50959),r=n(97754),i=n.n(r),s=n(3343),a=n(50238),l=n(16396),c=n(26448);function u(e){const{className:t,...n}=e,[r,u]=(0,a.useRovingTabindexElement)(null);return o.createElement(l.PopupMenuItem,{...n,className:i()(c.accessible,e.isActive&&c.active,t),reference:r,tabIndex:u,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,s.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),r.current instanceof HTMLElement&&r.current.click())},"data-role":"menuitem","aria-disabled":e.isDisabled||void 0,toolboxRole:"toolbar"})}},78135:(e,t,n)=>{"use strict";n.d(t,{HorizontalAttachEdge:()=>r,HorizontalDropDirection:()=>s,VerticalAttachEdge:()=>o,VerticalDropDirection:()=>i,getPopupPositioner:()=>c});var o,r,i,s,a=n(50151);!function(e){e[e.Top=0]="Top",e[e.Bottom=1]="Bottom",e[e.AutoStrict=2]="AutoStrict"}(o||(o={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(r||(r={})),function(e){e[e.FromTopToBottom=0]="FromTopToBottom",e[e.FromBottomToTop=1]="FromBottomToTop"}(i||(i={})),function(e){e[e.FromLeftToRight=0]="FromLeftToRight",e[e.FromRightToLeft=1]="FromRightToLeft"}(s||(s={}));const l={verticalAttachEdge:o.Bottom,horizontalAttachEdge:r.Left,verticalDropDirection:i.FromTopToBottom,horizontalDropDirection:s.FromLeftToRight,verticalMargin:0,horizontalMargin:0,matchButtonAndListboxWidths:!1};function c(e,t){return n=>{const{contentWidth:c,contentHeight:u,availableHeight:d}=n,h=(0,a.ensureNotNull)(e).getBoundingClientRect(),{horizontalAttachEdge:p=l.horizontalAttachEdge,horizontalDropDirection:m=l.horizontalDropDirection,horizontalMargin:f=l.horizontalMargin,verticalMargin:g=l.verticalMargin,matchButtonAndListboxWidths:v=l.matchButtonAndListboxWidths}=t;let b=t.verticalAttachEdge??l.verticalAttachEdge,_=t.verticalDropDirection??l.verticalDropDirection;b===o.AutoStrict&&(d<h.y+h.height+g+u?(b=o.Top,_=i.FromBottomToTop):(b=o.Bottom,_=i.FromTopToBottom));const y=b===o.Top?-1*g:g,S=p===r.Right?h.right:h.left,w=b===o.Top?h.top:h.bottom,C={x:S-(m===s.FromRightToLeft?c:0)+f,y:w-(_===i.FromBottomToTop?u:0)+y};return v&&(C.overrideWidth=h.width),C}}},81348:(e,t,n)=>{"use strict";n.d(t,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>a,ToolWidgetButton:()=>l});var o=n(50959),r=n(97754),i=n(9745),s=n(38576);const a=s,l=o.forwardRef(((e,t)=>{
const{tag:n="div",icon:a,endIcon:l,isActive:c,isOpened:u,isDisabled:d,isGrouped:h,isHovered:p,isClicked:m,onClick:f,text:g,textBeforeIcon:v,title:b,theme:_=s,className:y,forceInteractive:S,inactive:w,"data-name":C,"data-tooltip":E,...T}=e,I=r(y,_.button,(b||E)&&"apply-common-tooltip",{[_.isActive]:c,[_.isOpened]:u,[_.isInteractive]:(S||Boolean(f))&&!d&&!w,[_.isDisabled]:Boolean(d||w),[_.isGrouped]:h,[_.hover]:p,[_.clicked]:m}),M=a&&("string"==typeof a?o.createElement(i.Icon,{className:_.icon,icon:a}):o.cloneElement(a,{className:r(_.icon,a.props.className)}));return"button"===n?o.createElement("button",{...T,ref:t,type:"button",className:r(I,_.accessible),disabled:d&&!w,onClick:f,title:b,"data-name":C,"data-tooltip":E},v&&g&&o.createElement("div",{className:r("js-button-text",_.text)},g),M,!v&&g&&o.createElement("div",{className:r("js-button-text",_.text)},g)):o.createElement("div",{...T,ref:t,"data-role":"button",className:I,onClick:d?void 0:f,title:b,"data-name":C,"data-tooltip":E},v&&g&&o.createElement("div",{className:r("js-button-text",_.text)},g),M,!v&&g&&o.createElement("div",{className:r("js-button-text",_.text)},g),l&&o.createElement(i.Icon,{icon:l,className:s.endIcon}))}))},56388:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetIconButton:()=>a});var o=n(50959),r=n(97754),i=n(81348),s=n(97373);const a=o.forwardRef((function(e,t){const{className:n,id:a,...l}=e;return o.createElement(i.ToolWidgetButton,{id:a,"data-name":a,...l,ref:t,className:r(n,s.button)})}))},16829:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetMenuSummary:()=>s});var o=n(50959),r=n(97754),i=n(55973);function s(e){return o.createElement("div",{className:r(e.className,i.title)},e.children)}},20626:(e,t,n)=>{"use strict";n.d(t,{ToolWidgetMenu:()=>b});var o=n(50959),r=n(97754),i=n.n(r),s=n(3343),a=n(20520),l=n(10381),c=n(90186),u=n(37558),d=n(41590),h=n(78135),p=n(90692),m=n(56570),f=n(76460),g=n(95389);var v;!function(e){e[e.Vertical=2]="Vertical",e[e.Horizontal=0]="Horizontal"}(v||(v={}));class b extends o.PureComponent{constructor(e){super(e),this._wrapperRef=null,this._controller=o.createRef(),this._onPopupCloseOnClick=e=>{"keyboard"===e.detail.clickType&&this.focus()},this._handleMenuFocus=e=>{e.relatedTarget===this._wrapperRef&&this.setState((e=>({...e,isOpenedByButton:!0}))),this.props.onMenuFocus?.(e)},this._handleWrapperRef=e=>{this._wrapperRef=e,this.props.reference&&this.props.reference(e)},this._handleOpen=()=>{"div"!==this.props.tag&&(this.setState((e=>({...e,isOpenedByButton:!1}))),this.props.menuReference?.current?.addEventListener("popup-menu-close-event",this._onPopupCloseOnClick),this._controller.current?.focus())},this._handleClick=e=>{(m.enabled("skip_event_target_check")||e.target instanceof Node)&&e.currentTarget.contains(e.target)&&(this._handleToggleDropdown(void 0,(0,f.isKeyboardClick)(e)),this.props.onClick&&this.props.onClick(e,!this.state.isOpened))},this._handleToggleDropdown=(e,t=!1)=>{const{onClose:n,onOpen:o}=this.props,{isOpened:r}=this.state,i="boolean"==typeof e?e:!r;this.setState({isOpened:i,shouldReturnFocus:!!i&&t}),
i&&o&&o(),!i&&n&&n()},this._handleClose=()=>{this.close()},this._handleKeyDown=e=>{const{orientation:t="horizontal"}=this.props;if(e.defaultPrevented)return;if(!(e.target instanceof Node))return;const n=(0,s.hashFromEvent)(e);if(e.currentTarget.contains(e.target))switch(n){case 40:if("div"===this.props.tag||"horizontal"!==t)return;if(this.state.isOpened)return;e.preventDefault(),this._handleToggleDropdown(!0,!0);break;case 27:if(!this.state.isOpened||!this.props.closeOnEsc)return;e.preventDefault(),e.stopPropagation(),this._handleToggleDropdown(!1)}else{if("div"===this.props.tag)return;switch(n){case 27:{e.preventDefault();const{shouldReturnFocus:t,isOpenedByButton:n}=this.state;this._handleToggleDropdown(!1),t&&n&&this._wrapperRef?.focus();break}}}},this.state={isOpened:!1,shouldReturnFocus:!1,isOpenedByButton:!1}}render(){const{tag:e="div",id:t,arrow:n,content:r,isDisabled:s,isDrawer:a,isShowTooltip:u,title:d,className:h,hotKey:m,theme:f,drawerBreakpoint:g,tabIndex:v,isClicked:b}=this.props,{isOpened:y}=this.state,S=i()(h,f.button,{"apply-common-tooltip":u||!s,[f.isDisabled]:s,[f.isOpened]:y,[f.clicked]:b}),w=_(r)?r({isOpened:y}):r;return"button"===e?o.createElement("button",{type:"button",id:t,className:i()(S,f.accessible),disabled:s,onClick:this._handleClick,title:d,"data-tooltip-hotkey":m,ref:this._handleWrapperRef,onKeyDown:this._handleKeyDown,tabIndex:v,...(0,c.filterDataProps)(this.props),...(0,c.filterAriaProps)(this.props)},w,n&&o.createElement("div",{className:f.arrow},o.createElement("div",{className:f.arrowWrap},o.createElement(l.ToolWidgetCaret,{dropped:y}))),this.state.isOpened&&(g?o.createElement(p.MatchMedia,{rule:g},(e=>this._renderContent(e))):this._renderContent(a))):o.createElement("div",{id:t,className:S,onClick:s?void 0:this._handleClick,title:d,"data-tooltip-hotkey":m,ref:this._handleWrapperRef,"data-role":"button",tabIndex:v,onKeyDown:this._handleKeyDown,"aria-haspopup":this.props["aria-haspopup"],...(0,c.filterDataProps)(this.props)},w,n&&o.createElement("div",{className:f.arrow},o.createElement("div",{className:f.arrowWrap},o.createElement(l.ToolWidgetCaret,{dropped:y}))),this.state.isOpened&&(g?o.createElement(p.MatchMedia,{rule:g},(e=>this._renderContent(e))):this._renderContent(a)))}close(){this.props.menuReference?.current?.removeEventListener("popup-menu-close-event",this._onPopupCloseOnClick),this._handleToggleDropdown(!1)}focus(){this._wrapperRef?.focus()}update(){null!==this._controller.current&&this._controller.current.update()}_renderContent(e){const{menuDataName:t,minWidth:n,menuClassName:r,menuRole:i,maxHeight:s,drawerPosition:l="Bottom",children:c,noMomentumBasedScroll:p}=this.props,{isOpened:m}=this.state,f={horizontalMargin:this.props.horizontalMargin||0,verticalMargin:this.props.verticalMargin||2,verticalAttachEdge:this.props.verticalAttachEdge,horizontalAttachEdge:this.props.horizontalAttachEdge,verticalDropDirection:this.props.verticalDropDirection,horizontalDropDirection:this.props.horizontalDropDirection,matchButtonAndListboxWidths:this.props.matchButtonAndListboxWidths
},g=Boolean(m&&e&&l),v=_(c)?c({isDrawer:g}):c;return g?o.createElement(u.DrawerManager,null,o.createElement(d.Drawer,{reference:this.props.drawerReference,onClose:this._handleClose,position:l,"data-name":t},v)):o.createElement(a.PopupMenu,{reference:this.props.menuReference,role:i,controller:this._controller,closeOnClickOutside:this.props.closeOnClickOutside,doNotCloseOn:this,isOpened:m,minWidth:n,onClose:this._handleClose,position:(0,h.getPopupPositioner)(this._wrapperRef,f),className:r,maxHeight:s,"data-name":t,tabIndex:"div"!==this.props.tag?-1:void 0,onOpen:this._handleOpen,onKeyDown:this.props.onMenuKeyDown,onFocus:this._handleMenuFocus,noMomentumBasedScroll:p},v)}}function _(e){return"function"==typeof e}b.defaultProps={arrow:!0,closeOnClickOutside:!0,theme:g}},45827:(e,t,n)=>{"use strict";n.d(t,{ToolbarIconButton:()=>a});var o=n(50959),r=n(50238),i=n(56388);const s=(0,o.forwardRef)((function(e,t){const{tooltip:n,...r}=e;return o.createElement(i.ToolWidgetIconButton,{"aria-label":n,...r,tag:"button",ref:t,"data-tooltip":n,"data-tooltip-show-on-focus":"true"})})),a=(0,o.forwardRef)((function(e,t){const[n,i]=(0,r.useRovingTabindexElement)(t);return o.createElement(s,{...e,ref:n,tabIndex:i})}))},88811:(e,t,n)=>{"use strict";n.d(t,{ToolbarMenuButton:()=>u});var o=n(50959),r=n(39416),i=n(50238),s=n(7047),a=n(20626),l=n(20243);const c=(0,o.forwardRef)((function(e,t){const{tooltip:n,tag:i,buttonRef:s,reference:c,...u}=e,d=(0,r.useFunctionalRefObject)(c??null);return o.createElement(a.ToolWidgetMenu,{"aria-label":n,...u,ref:t,tag:i??"button",reference:s??d,"data-tooltip":n,onMenuKeyDown:l.handleAccessibleMenuKeyDown,onMenuFocus:e=>(0,l.handleAccessibleMenuFocus)(e,s??d)})})),u=(0,o.forwardRef)((function(e,t){const{tooltip:n,menuReference:a=null,...l}=e,[u,d]=(0,i.useRovingTabindexElement)(null),h=(0,r.useFunctionalRefObject)(a);return o.createElement(c,{"aria-label":n,"aria-haspopup":"menu",...s.MouseClickAutoBlurHandler.attributes(),...l,ref:t,tag:"button",buttonRef:u,tabIndex:d,menuReference:h,tooltip:n})}))},6190:(e,t,n)=>{"use strict";n.d(t,{Toolbar:()=>d});var o=n(50959),r=n(50151),i=n(47201),s=n(3343),a=n(19291),l=n(57177),c=n(39416),u=n(7047);const d=(0,o.forwardRef)((function(e,t){const{onKeyDown:n,orientation:d,blurOnEscKeydown:h=!0,blurOnClick:p=!0,...m}=e,f=(0,c.useFunctionalRefObject)(t);return(0,o.useLayoutEffect)((()=>{const e=(0,r.ensureNotNull)(f.current),t=()=>{const t=(0,a.queryTabbableElements)(e).sort(a.navigationOrderComparator);if(0===t.length){const[t]=(0,a.queryFocusableElements)(e).sort(a.navigationOrderComparator);if(void 0===t)return;(0,l.becomeMainElement)(t)}if(t.length>1){const[,...e]=t;for(const t of e)(0,l.becomeSecondaryElement)(t)}};return window.addEventListener("keyboard-navigation-activation",t),()=>window.removeEventListener("keyboard-navigation-activation",t)}),[]),o.createElement("div",{...u.MouseClickAutoBlurHandler.attributes(p),...m,role:"toolbar","aria-orientation":d,ref:f,onKeyDown:(0,i.createSafeMulticastEventHandler)((function(e){if(e.defaultPrevented)return
;if(!(document.activeElement instanceof HTMLElement))return;const t=(0,s.hashFromEvent)(e);if(h&&27===t)return e.preventDefault(),void document.activeElement.blur();if("vertical"!==d&&37!==t&&39!==t)return;if("vertical"===d&&38!==t&&40!==t)return;const n=e.currentTarget,o=(0,a.queryFocusableElements)(n).sort(a.navigationOrderComparator);if(0===o.length)return;const r=o.indexOf(document.activeElement);if(-1===r)return;e.preventDefault();const i=()=>{const e=(r+o.length-1)%o.length;(0,l.becomeSecondaryElement)(o[r]),(0,l.becomeMainElement)(o[e]),o[e].focus()},c=()=>{const e=(r+o.length+1)%o.length;(0,l.becomeSecondaryElement)(o[r]),(0,l.becomeMainElement)(o[e]),o[e].focus()};switch((0,a.mapKeyCodeToDirection)(t)){case"inlinePrev":"vertical"!==d&&i();break;case"inlineNext":"vertical"!==d&&c();break;case"blockPrev":"vertical"===d&&i();break;case"blockNext":"vertical"===d&&c()}}),n),"data-tooltip-show-on-focus":"true"})}))},60925:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M12 4h3v1h-1.04l-.88 9.64a1.5 1.5 0 0 1-1.5 1.36H6.42a1.5 1.5 0 0 1-1.5-1.36L4.05 5H3V4h3v-.5C6 2.67 6.67 2 7.5 2h3c.83 0 1.5.67 1.5 1.5V4ZM7.5 3a.5.5 0 0 0-.5.5V4h4v-.5a.5.5 0 0 0-.5-.5h-3ZM5.05 5l.87 9.55a.5.5 0 0 0 .5.45h5.17a.5.5 0 0 0 .5-.45L12.94 5h-7.9Z"/></svg>'},89882:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M5 9a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm6 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 2a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/></svg>'},2057:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M9 14a3 3 0 1 1-6 0 3 3 0 0 1 6 0zm8 0a3 3 0 1 1-6 0 3 3 0 0 1 6 0zm5 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/></svg>'},578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},33765:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"><path fill="currentColor" d="M9.707 9l4.647-4.646-.707-.708L9 8.293 4.354 3.646l-.708.708L8.293 9l-4.647 4.646.708.708L9 9.707l4.646 4.647.708-.707L9.707 9z"/></svg>'},29540:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72" width="72" height="72"><path fill="currentColor" d="M15 24a21 21 0 1 1 42 0v7.41l8.97 5.01 1.08.6-.82.94-7.77 8.82 2.34 2.53-1.47 1.36L57 48.15V69H46v-7h-6v5h-9V56h-6v13H15V48.15l-2.33 2.52-1.47-1.36 2.35-2.53-7.78-8.82-.82-.93 1.08-.6L15 31.4V24Zm0 9.7-6.9 3.87L15 45.4V33.7Zm42 11.7 6.91-7.83-6.9-3.87v11.7ZM36 5a19 19 0 0 0-19 19v43h6V54h10v11h5v-5h10v7h7V24A19 19 0 0 0 36 5Zm-5 19.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0ZM42.5 26a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"/></svg>'},36296:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8 9.5H6.5a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V20m-8-1.5h11a1 1 0 0 0 1-1v-11a1 1 0 0 0-1-1h-11a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1z"/></svg>'},69533:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 5l3.5 3.5L8 12"/></svg>'},57674:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 11.5v8a1 1 0 0 0 1 1h15a1 1 0 0 0 1-1v-8m-17 0v-4a1 1 0 0 1 1-1h4l2 2h9a1 1 0 0 1 1 1v2m-17 0h17"/></svg>'},80465:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M5.5 6C4.67 6 4 6.67 4 7.5V20.5c0 .83.67 1.5 1.5 1.5H16v-1H5.5a.5.5 0 0 1-.5-.5V12h16v1h1V9.5c0-.83-.67-1.5-1.5-1.5h-8.8L9.86 6.15 9.71 6H5.5zM21 11H5V7.5c0-.28.22-.5.5-.5h3.8l1.85 1.85.14.15h9.21c.28 0 .5.22.5.5V11zm1 11v-3h3v-1h-3v-3h-1v3h-3v1h3v3h1z"/></svg>'},94007:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M2.448 10.124a10.82 10.82 0 0 1-.336-.609L2.105 9.5l.007-.015a12.159 12.159 0 0 1 1.686-2.466C5.002 5.665 6.752 4.373 9.05 4.373c2.297 0 4.047 1.292 5.25 2.646a12.166 12.166 0 0 1 1.687 2.466l.007.015-.007.015a12.163 12.163 0 0 1-1.686 2.466c-1.204 1.354-2.954 2.646-5.251 2.646-2.298 0-4.048-1.292-5.252-2.646a12.16 12.16 0 0 1-1.35-1.857zm14.558-.827l-.456.203.456.203v.002l-.003.005-.006.015-.025.052a11.813 11.813 0 0 1-.461.857 13.163 13.163 0 0 1-1.463 2.011c-1.296 1.46-3.296 2.982-5.998 2.982-2.703 0-4.703-1.522-6-2.982a13.162 13.162 0 0 1-1.83-2.677 7.883 7.883 0 0 1-.118-.243l-.007-.015-.002-.005v-.001l.456-.204-.456-.203v-.002l.002-.005.007-.015a4.66 4.66 0 0 1 .119-.243 13.158 13.158 0 0 1 1.83-2.677c1.296-1.46 3.296-2.982 5.999-2.982 2.702 0 4.702 1.522 5.998 2.981a13.158 13.158 0 0 1 1.83 2.678 8.097 8.097 0 0 1 .119.243l.006.015.003.005v.001zm-.456.203l.456-.203.09.203-.09.203-.456-.203zM1.092 9.297l.457.203-.457.203-.09-.203.09-.203zm9.958.203c0 1.164-.917 2.07-2 2.07-1.084 0-2-.906-2-2.07 0-1.164.916-2.07 2-2.07 1.083 0 2 .906 2 2.07zm1 0c0 1.695-1.344 3.07-3 3.07-1.657 0-3-1.375-3-3.07 0-1.695 1.343-3.07 3-3.07 1.656 0 3 1.375 3 3.07z"/></svg>'},52870:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M7 5.5a2.5 2.5 0 0 1 5 0V7H7V5.5zM6 7V5.5a3.5 3.5 0 1 1 7 0V7a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2zm8 2a1 1 0 0 0-1-1H6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V9zm-3 2.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/></svg>'},74059:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M21.106 12.5H6.894a.5.5 0 0 1-.318-.886L14 5.5l7.424 6.114a.5.5 0 0 1-.318.886zM21.106 16.5H6.894a.5.5 0 0 0-.318.886L14 23.5l7.424-6.114a.5.5 0 0 0-.318-.886z"/></svg>'},91730:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M4.605 14.089A10.052 10.052 0 0 1 4.56 14l.046-.089a17.18 17.18 0 0 1 2.329-3.327C8.58 8.758 10.954 7 14 7c3.046 0 5.421 1.757 7.066 3.585A17.18 17.18 0 0 1 23.44 14l-.046.089a17.18 17.18 0 0 1-2.329 3.327C19.42 19.242 17.046 21 14 21c-3.046 0-5.421-1.757-7.066-3.584a17.18 17.18 0 0 1-2.329-3.327zm19.848-.3L24 14l.453.212-.001.002-.003.005-.009.02a16.32 16.32 0 0 1-.662 1.195c-.44.72-1.1 1.684-1.969 2.65C20.08 20.008 17.454 22 14 22c-3.454 0-6.079-1.993-7.81-3.916a18.185 18.185 0 0 1-2.469-3.528 10.636 10.636 0 0 1-.161-.318l-.01-.019-.002-.005v-.002L4 14a55.06 55.06 0 0 1-.453-.212l.001-.002.003-.005.009-.02.033-.067a16.293 16.293 0 0 1 .629-1.126c.44-.723 1.1-1.686 1.969-2.652C7.92 7.993 10.546 6 14 6c3.454 0 6.079 1.993 7.81 3.916a18.183 18.183 0 0 1 2.469 3.528 10.588 10.588 0 0 1 .161.318l.01.019.002.005v.002zM24 14l.453-.211.099.211-.099.211L24 14zm-20.453-.211L4 14l-.453.211L3.448 14l.099-.211zM11 14a3 3 0 1 1 6 0 3 3 0 0 1-6 0zm3-4a4 4 0 1 0 0 8 4 4 0 0 0 0-8zm0 5a1 1 0 1 0 0-2 1 1 0 0 0 0 2z"/></svg>'},7295:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M4.5 12.5l4.59-4.59a2 2 0 0 1 2.83 0l3.17 3.17a2 2 0 0 0 2.83 0L22.5 6.5m-8 9.5v5.5M12 19l2.5 2.5L17 19m4.5 3v-5.5M19 19l2.5-2.5L24 19"/></svg>'},28824:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l4.586-4.586a2 2 0 0 1 2.828 0l3.172 3.172a2 2 0 0 0 2.828 0L23.5 10.5"/></svg>'},49756:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M11.5 4A2.5 2.5 0 0 0 7 5.5V7h6a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2V5.5a3.5 3.5 0 0 1 6.231-2.19c-.231.19-.73.69-.73.69zM13 8H6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h7a1 1 0 0 0 1-1V9a1 1 0 0 0-1-1zm-2 3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/></svg>'},62766:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M14.692 3.012l-12 12.277.715.699 12-12.277-.715-.699zM9.05 15.627a7.042 7.042 0 0 1-3.144-.741l.742-.76c.72.311 1.52.5 2.402.5 2.297 0 4.047-1.29 5.25-2.645a12.168 12.168 0 0 0 1.687-2.466l.007-.015-.007-.015A12.166 12.166 0 0 0 14.3 7.019c-.11-.124-.225-.247-.344-.37l.699-.715c.137.14.268.28.392.42a13.16 13.16 0 0 1 1.83 2.678 8.117 8.117 0 0 1 .119.243l.006.015.003.005v.001l-.456.204.456.203v.002l-.003.005-.006.015-.025.052a11.762 11.762 0 0 1-.461.857 13.158 13.158 0 0 1-1.463 2.011c-1.296 1.46-3.296 2.982-5.998 2.982zm7.5-6.127l.456-.203.09.203-.09.203-.456-.203zm-7.5 3.07c-.27 0-.53-.037-.778-.105l.879-.899c.999-.052 1.833-.872 1.895-1.938l.902-.923c.**************.102.795 0 1.695-1.344 3.07-3 3.07zM6.15 10.294l.902-.923c.063-1.066.896-1.886 1.895-1.938l.879-.9a2.94 2.94 0 0 0-.777-.103c-1.657 0-3 1.374-3 3.069 0 .275.035.541.101.795zM9.05 4.373c.88 0 1.68.19 2.4.5l.743-.759a7.043 7.043 0 0 0-3.143-.74c-2.703 0-4.703 1.521-6 2.98a13.159 13.159 0 0 0-1.83 2.678 7.886 7.886 0 0 0-.118.243l-.007.015-.002.005v.001l.456.204-.457-.203-.09.203.09.203.457-.203-.456.203v.002l.002.005.007.015a4.5 4.5 0 0 0 .119.243 13.152 13.152 0 0 0 1.83 2.677c.124.14.255.28.392.42l.7-.715c-.12-.122-.235-.245-.345-.369a12.156 12.156 0 0 1-1.686-2.466L2.105 9.5l.007-.015a12.158 12.158 0 0 1 1.686-2.466C5.002 5.665 6.752 4.373 9.05 4.373z"/></svg>'},39146:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path fill="currentColor" d="M9 1l2.35 4.76 5.26.77-3.8 3.7.9 5.24L9 13l-4.7 2.47.9-5.23-3.8-3.71 5.25-.77L9 1z"/></svg>'},48010:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M9 2.13l1.903 3.855.116.236.26.038 4.255.618-3.079 3.001-.188.184.044.259.727 4.237-3.805-2L9 12.434l-.233.122-3.805 2.001.727-4.237.044-.26-.188-.183-3.079-3.001 4.255-.618.26-.038.116-.236L9 2.13z"/></svg>'}}]);