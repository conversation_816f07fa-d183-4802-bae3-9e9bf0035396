(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[5516,9685],{45658:e=>{e.exports={small:"small-CtnpmPzP",medium:"medium-CtnpmPzP",large:"large-CtnpmPzP",switchView:"switchView-CtnpmPzP",checked:"checked-CtnpmPzP",disabled:"disabled-CtnpmPzP",track:"track-CtnpmPzP",thumb:"thumb-CtnpmPzP"}},20071:e=>{e.exports={switcher:"switcher-fwE97QDf",input:"input-fwE97QDf",thumbWrapper:"thumbWrapper-fwE97QDf",disabled:"disabled-fwE97QDf",checked:"checked-fwE97QDf"}},6917:e=>{e.exports={labelRow:"labelRow-_uXjSSQ5",toolbox:"toolbox-_uXjSSQ5",description:"description-_uXjSSQ5",descriptionTabletSmall:"descriptionTabletSmall-_uXjSSQ5",item:"item-_uXjSSQ5",titleItem:"titleItem-_uXjSSQ5",remove:"remove-_uXjSSQ5",active:"active-_uXjSSQ5",titleItemTabletSmall:"titleItemTabletSmall-_uXjSSQ5",itemTabletSmall:"itemTabletSmall-_uXjSSQ5",itemLabelTabletSmall:"itemLabelTabletSmall-_uXjSSQ5",wrap:"wrap-_uXjSSQ5",hovered:"hovered-_uXjSSQ5"}},34625:e=>{e.exports={footer:"footer-dwINHZFL"}},95214:e=>{e.exports={item:"item-zwyEh4hn",label:"label-zwyEh4hn",labelRow:"labelRow-zwyEh4hn",toolbox:"toolbox-zwyEh4hn"}},17946:(e,t,a)=>{"use strict";a.d(t,{CustomBehaviourContext:()=>i});const i=(0,a(50959).createContext)({enableActiveStateStyles:!0});i.displayName="CustomBehaviourContext"},125:(e,t,a)=>{"use strict";a.d(t,{useForceUpdate:()=>n});var i=a(50959);const n=()=>{const[,e]=(0,i.useReducer)((e=>e+1),0);return e}},76974:(e,t,a)=>{"use strict";a.d(t,{useIsMounted:()=>n});var i=a(50959);const n=()=>{const e=(0,i.useRef)(!1);return(0,i.useEffect)((()=>(e.current=!0,()=>{e.current=!1})),[]),e}},39362:(e,t,a)=>{"use strict";a.d(t,{SymbolSearchDialogFooter:()=>l});var i=a(50959),n=a(97754),s=a.n(n),o=a(34625);function l(e){const{className:t,children:a}=e;return i.createElement("div",{className:s()(o.footer,t)},a)}},36947:(e,t,a)=>{"use strict";a.d(t,{useForceUpdate:()=>i.useForceUpdate});var i=a(125)},81332:(e,t,a)=>{"use strict";a.d(t,{multilineLabelWithIconAndToolboxTheme:()=>o});var i=a(40173),n=a(9059),s=a(95214);const o=(0,i.mergeThemes)(n,s)},91836:e=>{e.exports={summary:"summary-ynHBVe1n",hovered:"hovered-ynHBVe1n",caret:"caret-ynHBVe1n"}},66114:e=>{e.exports={item:"item-KMkDzD5K",accessible:"accessible-KMkDzD5K",round:"round-KMkDzD5K",active:"active-KMkDzD5K"}},97542:e=>{e.exports={accessible:"accessible-raQdxQp0"}},89888:e=>{e.exports={button:"button-LkmyTVRc",active:"active-LkmyTVRc"}},17479:e=>{e.exports={wrapper:"wrapper-psOC5oyI",labelRow:"labelRow-psOC5oyI",label:"label-psOC5oyI",labelHint:"labelHint-psOC5oyI",labelOn:"labelOn-psOC5oyI"}},22315:e=>{e.exports={wrapper:"wrapper-bl9AR3Gv",hovered:"hovered-bl9AR3Gv",switchWrap:"switchWrap-bl9AR3Gv",withIcon:"withIcon-bl9AR3Gv",labelRow:"labelRow-bl9AR3Gv",label:"label-bl9AR3Gv",icon:"icon-bl9AR3Gv",labelHint:"labelHint-bl9AR3Gv",labelOn:"labelOn-bl9AR3Gv",accessible:"accessible-bl9AR3Gv"}},9306:e=>{e.exports={button:"button-Y1TCZogJ",active:"active-Y1TCZogJ"}},23225:e=>{e.exports={button:"button-ptpAHg8E",withText:"withText-ptpAHg8E",withoutText:"withoutText-ptpAHg8E"}},
68484:e=>{e.exports={spinnerWrap:"spinnerWrap-cZT0OZe0"}},82316:e=>{e.exports={button:"button-neROVfUe",first:"first-neROVfUe",last:"last-neROVfUe"}},54663:e=>{e.exports={wrap:"wrap-n5bmFxyX"}},21569:e=>{e.exports={hidden:"hidden-5MVS18J8"}},63295:e=>{e.exports={"tablet-small-breakpoint":"(max-width: 440px)",item:"item-o5a0MQMm",withIcon:"withIcon-o5a0MQMm",shortcut:"shortcut-o5a0MQMm",loading:"loading-o5a0MQMm",icon:"icon-o5a0MQMm"}},41340:e=>{e.exports={button:"button-b3Cgff6l",group:"group-b3Cgff6l",menu:"menu-b3Cgff6l",betaBadge:"betaBadge-b3Cgff6l",newBadge:"newBadge-b3Cgff6l",label:"label-b3Cgff6l"}},935:e=>{e.exports={customTradingViewStyleButton:"customTradingViewStyleButton-zigjK1n2",withoutIcon:"withoutIcon-zigjK1n2"}},69744:e=>{e.exports={dropdown:"dropdown-l0nf43ai",label:"label-l0nf43ai",smallWidthTitle:"smallWidthTitle-l0nf43ai",smallWidthMenuItem:"smallWidthMenuItem-l0nf43ai",smallWidthWrapper:"smallWidthWrapper-l0nf43ai"}},18027:e=>{e.exports={value:"value-gwXludjS",selected:"selected-gwXludjS"}},23745:e=>{e.exports={smallWidthMenuItem:"smallWidthMenuItem-RmqZNwwp",menuItem:"menuItem-RmqZNwwp",remove:"remove-RmqZNwwp",signal:"signal-RmqZNwwp","highlight-animation":"highlight-animation-RmqZNwwp"}},20905:e=>{e.exports={button:"button-S_1OCXUK",first:"first-S_1OCXUK",last:"last-S_1OCXUK",menu:"menu-S_1OCXUK",dropdown:"dropdown-S_1OCXUK",menuContent:"menuContent-S_1OCXUK",section:"section-S_1OCXUK",smallTabletSectionTitle:"smallTabletSectionTitle-S_1OCXUK",addCustomInterval:"addCustomInterval-S_1OCXUK",desktop:"desktop-S_1OCXUK",group:"group-S_1OCXUK"}},36488:e=>{e.exports={button:"button-gn9HMufu"}},75394:e=>{e.exports={button:"button-ZuDkGGhF",isDisabled:"isDisabled-ZuDkGGhF"}},52464:e=>{e.exports={saveString:"saveString-XVd1Kfjg",hidden:"hidden-XVd1Kfjg",loader:"loader-XVd1Kfjg"}},18603:e=>{e.exports={menuBtnWrap:"menuBtnWrap-yyMUOAN9",menu:"menu-yyMUOAN9",hintPlaceholder:"hintPlaceholder-yyMUOAN9",hintWrapper:"hintWrapper-yyMUOAN9",hintText:"hintText-yyMUOAN9",hintButton:"hintButton-yyMUOAN9",hintButtons:"hintButtons-yyMUOAN9",hintAdditionalButton:"hintAdditionalButton-yyMUOAN9",opened:"opened-yyMUOAN9",hover:"hover-yyMUOAN9",clicked:"clicked-yyMUOAN9",autoSaveWrapper:"autoSaveWrapper-yyMUOAN9",sharingWrapper:"sharingWrapper-yyMUOAN9",button:"button-yyMUOAN9",buttonSmallPadding:"buttonSmallPadding-yyMUOAN9",hintPlaceHolder:"hintPlaceHolder-yyMUOAN9",smallHintPlaceHolder:"smallHintPlaceHolder-yyMUOAN9",popupItemRowTabletSmall:"popupItemRowTabletSmall-yyMUOAN9",shortcut:"shortcut-yyMUOAN9",toolTitle:"toolTitle-yyMUOAN9",toolTitleMobile:"toolTitleMobile-yyMUOAN9",layoutItem:"layoutItem-yyMUOAN9",layoutMeta:"layoutMeta-yyMUOAN9",toolbox:"toolbox-yyMUOAN9",toolboxSmall:"toolboxSmall-yyMUOAN9",layoutTitle:"layoutTitle-yyMUOAN9",layoutItemWrap:"layoutItemWrap-yyMUOAN9",layoutItemWrapSmall:"layoutItemWrapSmall-yyMUOAN9",layoutTitleSmall:"layoutTitleSmall-yyMUOAN9",textWrap:"textWrap-yyMUOAN9",text:"text-yyMUOAN9",withIcon:"withIcon-yyMUOAN9",sharingLabelWrap:"sharingLabelWrap-yyMUOAN9",
titleSharingLabel:"titleSharingLabel-yyMUOAN9",switcherLabel:"switcherLabel-yyMUOAN9",iconWrap:"iconWrap-yyMUOAN9",infoIcon:"infoIcon-yyMUOAN9",accessibleLabel:"accessibleLabel-yyMUOAN9"}},92876:e=>{e.exports={button:"button-cq__ntSC",smallLeftPadding:"smallLeftPadding-cq__ntSC",text:"text-cq__ntSC",uppercase:"uppercase-cq__ntSC"}},75313:e=>{e.exports={description:"description-jgoQcEnP"}},97357:e=>{e.exports={wrap:"wrap-HXSqojvq",titleWrap:"titleWrap-HXSqojvq",indicators:"indicators-HXSqojvq",title:"title-HXSqojvq",icon:"icon-HXSqojvq",text:"text-HXSqojvq",titleTabletSmall:"titleTabletSmall-HXSqojvq",labelRow:"labelRow-HXSqojvq",label:"label-HXSqojvq"}},72154:e=>{e.exports={menu:"menu-hcofKPms",menuSmallTablet:"menuSmallTablet-hcofKPms",menuItemHeaderTabletSmall:"menuItemHeaderTabletSmall-hcofKPms",menuItemHeader:"menuItemHeader-hcofKPms"}},88506:e=>{e.exports={wrap:"wrap-jiC5bgmi",full:"full-jiC5bgmi",first:"first-jiC5bgmi",last:"last-jiC5bgmi",medium:"medium-jiC5bgmi",buttonWithFavorites:"buttonWithFavorites-jiC5bgmi"}},28282:e=>{e.exports={icon:"icon-uMfL97K2"}},39706:(e,t,a)=>{"use strict";a.d(t,{CollapsibleSection:()=>r});var i=a(50959),n=a(97754),s=a.n(n),o=a(10381),l=a(91836);const r=(0,i.forwardRef)((function(e,t){const{open:a,summary:n,children:r,onStateChange:h,tabIndex:c,className:d,...u}=e;return i.createElement(i.Fragment,null,i.createElement("div",{...u,className:s()(d,l.summary),onClick:function(){h&&h(!a)},"data-open":a,"aria-expanded":a,ref:t,tabIndex:c},n,i.createElement(o.ToolWidgetCaret,{className:l.caret,dropped:Boolean(a)})),a&&r)}))},40894:(e,t,a)=>{"use strict";a.d(t,{MenuFavoriteButton:()=>c});var i=a(50959),n=a(97754),s=a.n(n),o=a(50238),l=a(36189),r=a(71402),h=a(89888);function c(e){const{onClick:t,isFilled:a,isActive:n,...c}=e,[d,u]=(0,o.useRovingTabindexElement)(null),m=a?r.removeTitlesMap[r.RemoveTitleType.Remove]:r.removeTitlesMap[r.RemoveTitleType.Add];return(0,i.useLayoutEffect)((()=>{const e=d.current;e instanceof HTMLElement&&e.dispatchEvent(new CustomEvent("common-tooltip-update"))}),[m,d]),i.createElement("button",{ref:d,tabIndex:u,onClick:t,className:s()(h.button,n&&h.active,"apply-common-tooltip"),type:"button","aria-label":m,"data-tooltip":m},i.createElement(l.FavoriteButton,{...c,isFilled:a,isActive:n,title:""}))}},10428:(e,t,a)=>{"use strict";a.d(t,{DEFAULT_MENU_ITEM_SWITCHER_THEME:()=>C,MenuItemSwitcher:()=>_});var i,n=a(50959),s=a(97754),o=a.n(s),l=a(17946),r=a(45658);function h(e){const{size:t="small",checked:a,disabled:i}=e;return n.createElement("span",{className:o()(r.switchView,r[t],i&&r.disabled,a&&r.checked)},n.createElement("span",{className:r.track}),n.createElement("span",{className:r.thumb}))}!function(e){e.Small="small",e.Medium="medium",e.Large="large"}(i||(i={}));var c,d=a(3343),u=a(20071),m=a.n(u);function v(e){const t=(0,
n.useContext)(l.CustomBehaviourContext),{size:a,intent:i="default",checked:o,className:r,enableActiveStateStyles:c=t.enableActiveStateStyles,disabled:u,onChange:v,title:p,id:g,name:b,value:S,tabIndex:C,role:_="switch",ariaDisabled:y,reference:w,ariaLabelledBy:f,ariaLabel:k,...E}=e;return n.createElement("span",{className:s(r,m().switcher)},n.createElement("input",{...E,type:"checkbox",className:s(m().input,c&&m().activeStylesEnabled,o&&m().checked,u&&m().disabled),role:_,"aria-checked":o,checked:o,onKeyDown:e=>{13===(0,d.hashFromEvent)(e)&&e.currentTarget?.click()},onChange:v,disabled:u,"aria-disabled":y,tabIndex:C,title:p,id:g,name:b,value:S,ref:w,"aria-label":k,"aria-labelledby":f}),n.createElement("span",{className:s(m().thumbWrapper,m()[i])},n.createElement(h,{checked:o,size:a,disabled:u})))}!function(e){e.Default="default",e.Select="select"}(c||(c={}));var p=a(9745),g=a(50238),b=a(90186),S=a(22315);const C=S;function _(e){const{role:t,checked:a,onChange:i,className:s,id:l,label:r,labelDescription:h,preventLabelHighlight:c,value:u,reference:m,switchReference:C,theme:_=S,disabled:y,switchRole:w,icon:f}=e,[k,E]=(0,g.useRovingTabindexElement)(null),M=o()(_.label,a&&!c&&_.labelOn),T=o()(s,_.wrapper,a&&_.wrapperWithOnLabel,h&&_.wrapperWithDescription);return n.createElement("label",{role:t,className:o()(T,f&&_.withIcon,S.accessible),htmlFor:l,ref:m,onKeyDown:function(e){if(e.target!==e.currentTarget)return;const t=(0,d.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),k.current instanceof HTMLElement&&k.current.click())},tabIndex:E,"data-role":"menuitem","aria-disabled":e.disabled||void 0,"aria-selected":a},void 0!==f&&n.createElement(p.Icon,{className:_.icon,icon:f}),n.createElement("div",{className:_.labelRow},n.createElement("div",{className:M},r),h&&n.createElement("div",{className:_.labelHint},h)),n.createElement("div",{className:S.switchWrap},n.createElement(v,{disabled:y,className:_.switch,reference:function(e){k(e),C?.(e)},checked:a,onChange:function(e){const t=e.target.checked;void 0!==i&&i(t)},value:u,tabIndex:-1,id:l,role:w,ariaDisabled:!0,...(0,b.filterDataProps)(e)})))}},38068:(e,t,a)=>{"use strict";a.d(t,{MenuRemoveButton:()=>d});var i=a(50959),n=a(97754),s=a.n(n),o=a(11542),l=a(50238),r=a(96040),h=a(60925),c=a(9306);function d(e){const{onClick:t,isActive:n,onKeyDown:d,...u}=e,[m,v]=(0,l.useRovingTabindexElement)(null);return i.createElement("button",{ref:m,tabIndex:v,onClick:t,onKeyDown:d,className:s()(c.button,n&&c.active,"apply-common-tooltip"),"aria-label":o.t(null,void 0,a(67410)),"data-tooltip":o.t(null,void 0,a(67410)),type:"button"},i.createElement(r.RemoveButton,{...u,isActive:n,title:"",icon:h}))}},95230:(e,t,a)=>{"use strict";a.r(t),a.d(t,{getRestrictedToolSet:()=>di});var i=a(56570),n=a(50959),s=a(19036),o=a(9745),l=a(11542),r=a(82992),h=a(5171),c=a(23076),d=a(88811),u=a(97754),m=a.n(u),v=a(54663);const p=n.forwardRef(((e,t)=>{const{children:a,className:i,...s}=e;return n.createElement("div",{className:u(i,v.wrap),ref:t,...s},a)}));var g=a(20792),b=a(82316)
;class S extends n.PureComponent{constructor(){super(...arguments),this._handleClick=()=>{const{onClick:e,onClickArg:t}=this.props;e&&e(t)}}render(){const{className:e,icon:t,hint:a,text:i,isDisabled:s,isActive:o,isFirst:l,isLast:r,onClick:h,onClickArg:c,...d}=this.props;return n.createElement(g.ToolbarButton,{...d,icon:t,text:i,tooltip:a,isDisabled:s,isActive:o,isGrouped:!0,onClick:this._handleClick,className:u(e,b.button,{[b.first]:l,[b.last]:r})})}}var C=a(11684),_=a(90692),y=a(24437),w=a(81332),f=a(77151),k=a(42989),E=a(68805),M=a(47201),T=a(3343),x=a(19291),I=a(15754);function R(e){const{orientation:t,onKeyDown:a,...i}=e;return n.createElement("div",{...i,role:"radiogroup","aria-orientation":t,onKeyDown:(0,M.createSafeMulticastEventHandler)((function(e){if(e.defaultPrevented)return;if(!(document.activeElement instanceof HTMLElement))return;const a=(0,T.hashFromEvent)(e);if("vertical"!==t&&38!==a&&40!==a)return;if("vertical"===t&&37!==a&&39!==a)return;const i=(n=e.currentTarget,Array.from(n.querySelectorAll('[role="radio"]:not([disabled]):not([aria-disabled])')).filter((0,I.createScopedVisibleElementFilter)(n))).sort(x.navigationOrderComparator);var n;if(0===i.length)return;const s=i.indexOf(document.activeElement);if(-1===s)return;e.preventDefault();const o=()=>{const e=(s+i.length-1)%i.length;i[s].dispatchEvent(new CustomEvent("roving-tabindex:secondary-element")),i[e].dispatchEvent(new CustomEvent("roving-tabindex:main-element")),i[e].focus()},l=()=>{const e=(s+i.length+1)%i.length;i[s].dispatchEvent(new CustomEvent("roving-tabindex:secondary-element")),i[e].dispatchEvent(new CustomEvent("roving-tabindex:main-element")),i[e].focus()};switch(a){case 38:"vertical"!==t&&o();break;case 40:"vertical"!==t&&l();break;case 37:"vertical"===t&&o();break;case 39:"vertical"===t&&l()}}),a)})}var A=a(10838),N=a(40894),F=a(41340);const H=(0,f.registryContextType)();function O(e){return!r.linking.supportedChartStyles.value()?.includes(e)}const D="ITEMS_DIVIDER",U=[[0,1,9,19,21],[2,14,15],[3,16,10],[13,12],[17,18,20],[8,4,7,5,6,11]];new Set([]),new Set([]);class W extends n.PureComponent{constructor(e,t){super(e,t),this._handleChangeStyle=e=>{const{favorites:t,lastSelectedNotFavorite:a,activeStyle:i}=this.state;this.setState({activeStyle:e,lastSelectedNotFavorite:t.includes(i)?a:i})},this._handleSelectStyle=e=>{const{chartWidgetCollection:t}=this.context;e!==t.activeChartStyle.value()&&t.setChartStyleToWidget(e)},this._handleClickFavorite=e=>{this._isStyleFavorited(e)?this._handleRemoveFavorite(e):this._handleAddFavorite(e)},this._boundForceUpdate=()=>{this.forceUpdate()},this._handleQuickClick=e=>{this._handleSelectStyle(e),this._trackClick()},(0,f.validateRegistry)(t,{chartWidgetCollection:s.any.isRequired,favoriteChartStylesService:s.any.isRequired});const{chartWidgetCollection:a,favoriteChartStylesService:i}=t,n=a.activeChartStyle.value(),o=i.get(),l=new Set((0,k.allChartStyles)());this.state={activeStyle:n,favorites:o,styles:U.reduce(((e,t)=>{const a=t.filter((e=>l.has(e)));return a.length&&(e.length&&a.unshift(D),
e.push(...a)),e}),[])}}componentDidMount(){const{chartWidgetCollection:e,favoriteChartStylesService:t}=this.context;e.activeChartStyle.subscribe(this._handleChangeStyle),t.getOnChange().subscribe(this,this._handleChangeSettings),r.linking.supportedChartStyles.subscribe(this._boundForceUpdate)}componentWillUnmount(){const{chartWidgetCollection:e,favoriteChartStylesService:t}=this.context;e.activeChartStyle.unsubscribe(this._handleChangeStyle),t.getOnChange().unsubscribe(this,this._handleChangeSettings),r.linking.supportedChartStyles.unsubscribe(this._boundForceUpdate)}render(){const{isShownQuicks:e,displayMode:t="full",id:i}=this.props,{activeStyle:s,favorites:r,styles:u,lastSelectedNotFavorite:m}=this.state,v="small"!==t&&e&&0!==r.length,g=[...r];g.includes(s)?void 0!==m&&g.push(m):g.push(s);const b=v&&g.length>1;return n.createElement(_.MatchMedia,{rule:y.DialogBreakpoints.TabletSmall},(e=>{const t=u.map(((t,a)=>t===D?n.createElement(C.PopupMenuSeparator,{key:`separator-${a}`}):this._renderPopupMenuItem(t,t===s,e)));return n.createElement(p,{id:i},b&&n.createElement(R,{orientation:"horizontal",className:F.group},g.map(((e,t)=>n.createElement(S,{role:"radio",className:F.button,icon:c.SERIES_ICONS[e],"aria-checked":v&&s===e,isActive:v&&s===e,isDisabled:O(e),key:t,hint:(0,E.getTranslatedChartStyleName)(e),isFirst:0===t,isLast:t===g.length-1,onClick:v?this._handleQuickClick:void 0,onClickArg:e,"data-value":h.STYLE_SHORT_NAMES[e]})))),n.createElement(d.ToolbarMenuButton,{arrow:Boolean(b),content:b?void 0:n.createElement(p,null,n.createElement(o.Icon,{icon:c.SERIES_ICONS[s]})),tooltip:b?l.t(null,void 0,a(89911)):(0,E.getTranslatedChartStyleName)(s),className:F.menu,isDrawer:e,onClick:this._trackClick,menuRole:"treegrid"},t))}))}_renderPopupMenuItem(e,t,a){const{isFavoritingAllowed:i}=this.props,s=this._isStyleFavorited(e);return n.createElement(A.AccessibleMenuItem,{key:`chart-type-${e}`,role:"row",theme:a?w.multilineLabelWithIconAndToolboxTheme:void 0,icon:c.SERIES_ICONS[e],isActive:t,"aria-selected":t,isDisabled:O(e),label:n.createElement("div",{className:F.label},(0,E.getTranslatedChartStyleName)(e)||"",!1,!1),onClick:this._handleSelectStyle,onClickArg:e,showToolboxOnHover:!s,showToolboxOnFocus:!0,toolbox:i&&n.createElement(N.MenuFavoriteButton,{isActive:t,isFilled:s,onClick:()=>this._handleClickFavorite(e)}),"data-value":h.STYLE_SHORT_NAMES[e]})}_handleChangeSettings(e){this.setState({lastSelectedNotFavorite:void 0,favorites:e})}_isStyleFavorited(e){return-1!==this.state.favorites.indexOf(e)}_handleAddFavorite(e){const{favorites:t}=this.state,{favoriteChartStylesService:a}=this.context;a.set([...t,e])}_handleRemoveFavorite(e){const{favorites:t}=this.state,{favoriteChartStylesService:a}=this.context;a.set(t.filter((t=>t!==e)))}_trackClick(){0}}W.contextType=H;var P=a(50238),L=a(81348),V=a(23225);const B=["medium","small"],z=(0,n.forwardRef)((function(e,t){const{text:a,className:i,displayMode:s,collapseWhen:o=B,...l}=e,r=!o.includes(s);return n.createElement(L.ToolWidgetButton,{...l,ref:t,text:r?a:void 0,
className:u(i,V.button,r?V.withText:V.withoutText)})}));function Z(e){const{tooltip:t,...a}=e,[i,s]=(0,P.useRovingTabindexElement)(null);return n.createElement(z,{"aria-label":t,...a,tag:"button",tabIndex:s,ref:i,"data-tooltip":t})}var K=a(51768),q=a(76460),j=a(1393);const Q=(0,f.registryContextType)();class X extends n.PureComponent{constructor(e,t){super(e,t),this._updateState=e=>{this.setState({isActive:e})},this._handleClick=e=>{(0,K.trackEvent)("GUI","Chart Header Toolbar","compare"),this._compareDialogRenderer?.show({shouldReturnFocus:(0,q.isKeyboardClick)(e)})},(0,f.validateRegistry)(t,{chartWidgetCollection:s.any.isRequired}),this.state={isActive:!1},this._compareDialogRenderer=this.context.chartWidgetCollection.getCompareDialogRenderer()}componentDidMount(){this._compareDialogRenderer?.visible().subscribe(this._updateState)}componentWillUnmount(){this._compareDialogRenderer?.visible().unsubscribe(this._updateState)}render(){const{isActive:e}=this.state;return n.createElement(Z,{...this.props,icon:j,isOpened:e,onClick:this._handleClick,collapseWhen:["full","medium","small"],tooltip:l.t(null,void 0,a(53942)),"aria-haspopup":"dialog"})}}X.contextType=Q;var G=a(45827),$=a(61814),Y=a(68335),J=a(97268),ee=a(36992);const te=(0,$.hotKeySerialize)({keys:[(0,Y.humanReadableModifiers)(Y.Modifiers.Shift,!1),"F"],text:"{0} + {1}"}),ae=(0,f.registryContextType)();function ie(e){return e.fullscreen().value()?ee:J}class ne extends n.PureComponent{constructor(e,t){super(e,t),this._updateState=()=>{this.setState({icon:ie(this.context.chartWidgetCollection)})},this._handleClick=()=>{const{chartWidgetCollection:e}=this.context;e.fullscreen().value()?e.exitFullscreen():e.startFullscreen()},(0,f.validateRegistry)(t,{chartWidgetCollection:s.any.isRequired}),this.state={icon:ie(this.context.chartWidgetCollection)},this._subscribe()}render(){const{className:e,id:t}=this.props,{icon:i}=this.state;return n.createElement(G.ToolbarIconButton,{id:t,icon:i,onClick:this._handleClick,className:u(e),tooltip:l.t(null,void 0,a(67092)),"data-tooltip-hotkey":te})}componentWillUnmount(){this._unsubscribe()}_subscribe(){this.context.chartWidgetCollection.fullscreen().subscribe(this._updateState)}_unsubscribe(){this.context.chartWidgetCollection.fullscreen().unsubscribe(this._updateState)}}ne.contextType=ae;var se=a(50151),oe=a(16396),le=a(20243),re=a(16638);const he=(0,a(9343).getLogger)("FavoritesInfo");function ce(e){if(0===e.length)return Promise.resolve([]);he.logNormal("Requesting favorites info");const t=[],a=new Map,i=new Map,n=new Map;return e.forEach((e=>{switch(e.type){case"java":n.set(e.studyId,e);break;case"pine":isPublishedPineId(e.pineId)?a.set(e.pineId,e):i.set(e.pineId,e);break;default:(0,se.assert)(!1,`unknown favorite type ${JSON.stringify(e)}`)}})),0!==n.size&&t.push((0,re.studyMetaInfoRepository)().findAllJavaStudies().then((e=>{const t=new Map;for(const a of e)!a.is_hidden_study&&n.has(a.id)&&t.set(a.id,{name:a.description,localizedName:a.description_localized,studyMarketShittyObject:a});return t})).then((e=>{
const t=function(e,t){const a={items:[],notFoundItems:[]};return e.forEach(((e,i)=>{const n=t.get(i);void 0!==n?a.items.push({item:e,info:n}):a.notFoundItems.push(e)})),a}(n,e);if(0!==t.notFoundItems.length){const e=t.notFoundItems.map((e=>e.studyId));he.logWarn(`Cannot find java scripts: ${JSON.stringify(e)}`)}return t.items}))),Promise.all(t).then((e=>(he.logNormal("Requesting favorites info finished"),e.reduce(((e,t)=>e.concat(t)),[]))))}var de=a(32755),ue=a(928),me=a(26996),ve=a(68484);function pe(e){const{className:t}=e;return n.createElement("div",{className:m()(ve.spinnerWrap,t)},n.createElement(me.Loader,null))}var ge=a(16829),be=a(76422),Se=a(39681),Ce=a(69744);const _e=(0,$.hotKeySerialize)({keys:["/"],text:"{0}"}),ye=(0,f.registryContextType)();class we extends n.PureComponent{constructor(e,t){super(e,t),this._promise=null,this._menu=n.createRef(),this._menuItemsContainer=n.createRef(),this._favoriteFundamentalsModel=null,this._setActiveState=e=>{this.setState({isActive:e})},this._handleClick=e=>{const{studyMarket:t}=this.props;this.setState({isActive:!0},(()=>{t.visible().value()?t.hide():t.show({shouldReturnFocus:(0,q.isKeyboardClick)(e)})})),this._trackClick()},this._handleSelectIndicator=e=>{e=(0,se.ensureDefined)(e),this._trackFavoriteAction("Favorite indicator from toolbar");"java"===e.type?e.studyId:e.pineId;(async()=>{e=(0,se.ensureDefined)(e);const{chartWidgetCollection:t}=this.context;if("java"===e.type){const t=(0,de.tryFindStudyLineToolNameByStudyId)(e.studyId);if(null!==t)return await(0,de.initLineTool)(t),void ue.tool.setValue(t)}t.activeChartWidget.value().insertStudy(e,[])})()},this._handleFavoriteIndicatorsChange=()=>{const{favoriteScriptsModel:e}=this.context,t=[...(0,se.ensureDefined)(e).favorites()];this.setState({favorites:t}),this._clearCache()},this._handleFavoriteFundamentalsChange=()=>{const e=new Set(this._favoriteFundamentalsModel?.favorites()||[]);this.setState({favoriteFundamentals:e}),this._clearCache()},this._handleMouseEnter=()=>{this._prefetchFavorites()},this._handleWrapClick=()=>{this._prefetchFavorites()},this._handleChangeActiveWidget=()=>{this._clearCache()},this._clearCache=()=>{this._promise=null,this.setState({infos:[]})},this._handleScriptRenamed=e=>{const{favoriteScriptsModel:t}=this.context;void 0!==t&&t.isFav(e.scriptIdPart)&&this._clearCache()},this._handleFavoriteMenuClick=()=>{this._trackClick(),this._trackFavoriteAction("Select favorite indicators dropdown")},(0,f.validateRegistry)(t,{favoriteScriptsModel:s.any,chartWidgetCollection:s.any.isRequired});const{favoriteScriptsModel:a}=t,i=void 0!==a?a.favorites():[];this.state={isActive:!1,isLoading:!1,favorites:i,favoriteFundamentals:void 0,infos:[]}}componentDidMount(){const{studyMarket:e}=this.props,{favoriteScriptsModel:t,chartWidgetCollection:a}=this.context;e.visible().subscribe(this._setActiveState),void 0!==t&&(t.favoritesChanged().subscribe(this,this._handleFavoriteIndicatorsChange),a.activeChartWidget.subscribe(this._handleChangeActiveWidget)),be.on("TVScriptRenamed",this._handleScriptRenamed,null)}
componentWillUnmount(){const{studyMarket:e}=this.props,{favoriteScriptsModel:t,chartWidgetCollection:a}=this.context;e.visible().unsubscribe(this._setActiveState),void 0!==t&&(t.favoritesChanged().unsubscribe(this,this._handleFavoriteIndicatorsChange),a.activeChartWidget.unsubscribe(this._handleChangeActiveWidget)),be.unsubscribe("TVScriptRenamed",this._handleScriptRenamed,null),this._promise=null}render(){const{isActive:e,favorites:t,favoriteFundamentals:i,isLoading:s}=this.state,{className:o,displayMode:r,id:h}=this.props,{chartWidgetCollection:c}=this.context;return n.createElement(n.Fragment,null,n.createElement(p,{id:h,onMouseEnter:this._handleMouseEnter,onClick:this._handleWrapClick},n.createElement(Z,{displayMode:r,className:o,icon:Se,isOpened:e,onClick:this._handleClick,text:l.t(null,void 0,a(84549)),"data-role":"button","data-name":"open-indicators-dialog",tooltip:l.t(null,void 0,a(62005)),"data-tooltip-hotkey":_e}),Boolean(t.length>0||i?.size)&&n.createElement(_.MatchMedia,{rule:"(max-width: 440px)"},(e=>n.createElement(d.ToolbarMenuButton,{key:c.activeChartWidget.value().id(),arrow:!0,closeOnClickOutside:!0,isDrawer:e,drawerPosition:"Bottom",ref:this._menu,menuReference:this._menuItemsContainer,onClick:this._handleFavoriteMenuClick,"data-name":"show-favorite-indicators",tooltip:l.t(null,void 0,a(70234)),menuRole:"menu"},n.createElement("div",{className:m()(Ce.dropdown,e&&Ce.smallWidthWrapper)},n.createElement(ge.ToolWidgetMenuSummary,{className:e&&Ce.smallWidthTitle},l.t(null,void 0,a(52530))),s&&n.createElement(pe,null),!s&&n.createElement(n.Fragment,null,this.state.infos.length>0?this.state.infos.map((t=>n.createElement(A.AccessibleMenuItem,{role:"menuitem",className:m()(e&&Ce.smallWidthMenuItem),theme:e?w.multilineLabelWithIconAndToolboxTheme:void 0,key:"java"===t.item.type?t.item.studyId:t.item.pineId,onClick:this._handleSelectIndicator,onClickArg:t.item,label:n.createElement("span",{className:m()(!e&&Ce.label,e&&Ce.smallWidthLabel,"apply-overflow-tooltip")},fe(t))}))):null!==this._promise&&n.createElement(oe.PopupMenuItem,{isDisabled:!0,label:l.t(null,void 0,a(18448))}))))))))}_prefetchFavorites(){const{chartWidgetCollection:e}=this.context;if(null!==this._promise||!window.is_authenticated)return;if(!e.activeChartWidget.value().hasModel())return;this.setState({isLoading:!0});const t=this._promise=Promise.all([ce(this.state.favorites),void 0]).then((e=>{if(t!==this._promise)return;const[a,i]=e;let n=[...a];if(i){const e=i.filter((e=>this.state.favoriteFundamentals?.has(e.scriptIdPart))).map(this._mapFundamentalToFavoriteItemInfo);n.push(...e)}n=[...n].sort(((e,t)=>fe(e).localeCompare(fe(t)))),this.setState({infos:n,isLoading:!1},(()=>{this._menu.current?.update(),this._menuItemsContainer.current&&document.activeElement===this._menuItemsContainer.current&&(0,le.focusFirstMenuItem)(this._menuItemsContainer.current)}))}))}_trackClick(){0}_trackFavoriteAction(e){(0,K.trackEvent)("GUI","Chart Header Toolbar",e)}_mapFundamentalToFavoriteItemInfo(e){return{item:{type:"pine",pineId:e.scriptIdPart},info:{
name:e.scriptName,localizedName:getLocalizedFundamentalsName(e),studyMarketShittyObject:void 0}}}}function fe(e){return e.info.localizedName||l.t(e.info.name,{context:"study"},a(83477))}we.contextType=ye;var ke=a(10074),Ee=a(85049),Me=a(18027);function Te(e){return n.createElement("div",{className:u(Me.value,{[Me.selected]:e.isSelected})},e.value,e.metric)}var xe=a(70412),Ie=a(32563),Re=a(38068),Ae=a(23745);function Ne(e){const{role:t,interval:a,hint:i,isActive:s,isDisabled:o,isFavorite:l,isRemovable:r,isFavoritingAllowed:h,isSignaling:c,onClick:d,onClickRemove:u,onClickFavorite:v,isSmallTablet:p,scrollContainerRef:g,...b}=e,[S,C]=(0,xe.useHover)(),_=n.useCallback((e=>u(a,e)),[u,a]),y=n.useCallback((()=>v(a)),[v,a]),f=(0,n.useRef)(null);return(0,n.useEffect)((()=>{if(c){const e=f.current;if(e&&g.current){const t=e.getBoundingClientRect(),a=g.current.getBoundingClientRect();(a.top>t.top||a.bottom<t.bottom)&&e.scrollIntoView()}}}),[c,g]),n.createElement("div",{...C,ref:f},n.createElement(A.AccessibleMenuItem,{...b,role:t,className:m()(Ae.menuItem,p&&Ae.smallWidthMenuItem,c&&Ae.signal),theme:p?w.multilineLabelWithIconAndToolboxTheme:void 0,isActive:s,isDisabled:o,onClick:d,onClickArg:a,toolbox:function(){const e=n.createElement(Re.MenuRemoveButton,{key:"remove",isActive:s,hidden:!Ie.touch&&!S,onClick:_,className:Ae.remove}),t=n.createElement(N.MenuFavoriteButton,{key:"favorite",isActive:s,isFilled:l,onClick:y});return[r&&e,h&&t]}(),showToolboxOnHover:!l,showToolboxOnFocus:!0,label:i}))}const Fe={[Ee.ResolutionKind.Ticks]:l.t(null,{context:"interval_group_name"},a(49512)),[Ee.ResolutionKind.Seconds]:l.t(null,{context:"interval_group_name"},a(32116)),[Ee.ResolutionKind.Minutes]:l.t(null,{context:"interval_group_name"},a(21551)),[Ee.SpecialResolutionKind.Hours]:l.t(null,{context:"interval_group_name"},a(18817)),[Ee.ResolutionKind.Days]:l.t(null,{context:"interval_group_name"},a(57252)),[Ee.ResolutionKind.Weeks]:l.t(null,{context:"interval_group_name"},a(41815)),[Ee.ResolutionKind.Months]:l.t(null,{context:"interval_group_name"},a(89506)),[Ee.ResolutionKind.Range]:l.t(null,{context:"interval_group_name"},a(22163)),[Ee.ResolutionKind.Invalid]:""};function He(e,t=!1){return{id:e,name:Fe[e],items:[],mayOmitSeparator:t}}var Oe=a(52033),De=a(29197),Ue=a(59064),We=a(39706),Pe=a(97542);function Le(e){const{className:t,...a}=e,[i,s]=(0,P.useRovingTabindexElement)(null);return n.createElement(We.CollapsibleSection,{...a,ref:i,tabIndex:s,"data-role":"menuitem",className:m()(Pe.accessible,t),onKeyDown:function(e){const t=(0,T.hashFromEvent)(e);13!==t&&32!==t||(e.preventDefault(),i.current instanceof HTMLElement&&i.current.click())}})}var Ve=a(34585),Be=a(20905),ze=a(6198);const Ze={openDialog:l.t(null,void 0,a(46311)),timeInterval:l.t(null,void 0,a(41173))},Ke=(0,$.hotKeySerialize)({keys:[","],text:l.t(null,void 0,a(92969))}),qe=(0,f.registryContextType)(),je=new Oe.Delegate,Qe=n.lazy((async()=>({
default:(await Promise.all([a.e(101),a.e(4524),a.e(7939),a.e(9258),a.e(5387),a.e(6445),a.e(3799),a.e(5480),a.e(8222),a.e(9296),a.e(625),a.e(422),a.e(8859),a.e(9418),a.e(9928),a.e(4013)]).then(a.bind(a,17891))).ToolWidgetIntervalsAddDialog})));class Xe extends n.PureComponent{constructor(e,t){super(e,t),this._menu=n.createRef(),this._menuItemsContainerRef=n.createRef(),this._drawerContainerRef=n.createRef(),this._renderChildren=(e,t)=>{const a=this._customIntervals?1:0,i=function(e){const t=He(Ee.ResolutionKind.Ticks),a=He(Ee.ResolutionKind.Seconds),i=He(Ee.ResolutionKind.Minutes),n=He(Ee.SpecialResolutionKind.Hours),s=He(Ee.ResolutionKind.Days),o=He(Ee.ResolutionKind.Range);return e.forEach((e=>{const l=Ee.Interval.parse(e);l.isMinuteHours()?n.items.push(e):l.isMinutes()?(0,Ee.isHour)(Number(l.multiplier()))?n.items.push(e):i.items.push(e):l.isSeconds()?a.items.push(e):l.isDWM()?s.items.push(e):l.isRange()?o.items.push(e):l.isTicks()&&t.items.push(e)})),[t,a,i,n,s,o].filter((e=>0!==e.items.length))}(e),n=i.length+a;return[...this._createIntervalItem(t,n),...this._createMenuItems(i,t,a,n)]},this._handleChangeInterval=()=>{const e=r.linking.interval.value(),{activeInterval:t,lastNotQuicked:a}=this.state,i=this._getQuicks();this.setState({activeInterval:(0,ke.normalizeIntervalString)(e),lastNotQuicked:void 0===t||i.includes(t)?a:t})},this._bindedForceUpdate=()=>{this.forceUpdate()},this._handleSelectInterval=e=>{void 0!==e&&e!==r.linking.interval.value()&&this.context.chartWidgetCollection.setResolution(e),e&&(0,K.trackEvent)("GUI","Time Interval",e)},this._handleClickFavorite=e=>{e=(0,se.ensureDefined)(e),this._isIntervalFavorite(e)?this._handleRemoveFavorite(e):this._handleAddFavorite(e)},this._handleAddFavorite=e=>{const{favorites:t}=this.state;this.context.favoriteIntervalsService.set([...t,e])},this._handleRemoveFavorite=e=>{const{favorites:t}=this.state;this.context.favoriteIntervalsService.set(t.filter((t=>t!==e)))},this._handleAddInterval=(e,t)=>{const{intervalService:a,intervalsMenuViewStateService:i}=this.context,{menuViewState:n}=this.state,s=a.add(e,t);if(s){this.setState({lastAddedInterval:s});const e=Ee.Interval.parseExt(s);if(e.interval.isMinuteHours())return void i.set({...n,[Ee.SpecialResolutionKind.Hours]:!1});const t=e.guiResolutionKind;if(t===Ee.ResolutionKind.Days||t===Ee.ResolutionKind.Weeks||t===Ee.ResolutionKind.Months)return void i.set({...n,[Ee.ResolutionKind.Days]:!1});i.set({...n,[t]:!1})}},this._handleRemoveInterval=(e,t)=>{const{intervalService:a}=this.context;if(e){if(t&&(0,q.isKeyboardClick)(t)&&this._menuItemsContainerRef.current){const t=(0,le.queryMenuElements)(this._menuItemsContainerRef.current),a=t.findIndex((t=>t.matches(`[data-value="${e}"]`)));if(-1!==a){const e=t[a+1]??t[a-1];e?e.focus():(0,le.focusFirstMenuItem)(this._menuItemsContainerRef.current)}}a.remove(e),this._handleRemoveFavorite(e)}},this._getHandleSectionStateChange=e=>t=>{const{menuViewState:a}=this.state,{intervalsMenuViewStateService:i}=this.context;i.set({...a,[e]:!t})},this._handleOpenAddIntervalDialog=()=>{
this.setState({isAddIntervalDialogOpened:!0})},this._handleCloseAddIntervalDialog=()=>{this.setState({isAddIntervalDialogOpened:!1})},this._handleGlobalClose=()=>{const{isFake:e}=this.props,{isAddIntervalDialogOpened:t}=this.state;e||t||je.fire()},this._handeQuickClick=e=>{this._handleSelectInterval(e),this._trackClick()},(0,f.validateRegistry)(t,{chartApiInstance:s.any.isRequired,favoriteIntervalsService:s.any.isRequired,intervalService:s.any.isRequired,intervalsMenuViewStateService:s.any.isRequired});const{chartApiInstance:a,favoriteIntervalsService:o,intervalService:l,intervalsMenuViewStateService:h}=t;this._customIntervals=i.enabled("custom_resolutions");const c=r.linking.interval.value(),d=c&&(0,ke.normalizeIntervalString)(c),u=o.get(),m=l.getCustomIntervals(),v=h.get();this._defaultIntervals=a.defaultResolutions().filter(ke.isIntervalEnabled).map(ke.normalizeIntervalString),this.state={isOpenedFormMenu:!1,activeInterval:d,favorites:u,customs:m,menuViewState:v,isAddIntervalDialogOpened:!1}}componentDidMount(){const{favoriteIntervalsService:e,intervalService:t,intervalsMenuViewStateService:a}=this.context;e.getOnChange().subscribe(this,this._handleChangeFavorites),a.getOnChange().subscribe(this,this._handleChangeMenuViewState),t.getOnChange().subscribe(this,this._handleChangeCustoms),r.linking.interval.subscribe(this._handleChangeInterval),r.linking.intraday.subscribe(this._bindedForceUpdate),r.linking.seconds.subscribe(this._bindedForceUpdate),r.linking.ticks.subscribe(this._bindedForceUpdate),r.linking.range.subscribe(this._bindedForceUpdate),r.linking.supportedResolutions.subscribe(this._bindedForceUpdate),r.linking.dataFrequencyResolution.subscribe(this._bindedForceUpdate),Ue.globalCloseDelegate.subscribe(this,this._handleGlobalClose)}componentWillUnmount(){const{favoriteIntervalsService:e,intervalService:t,intervalsMenuViewStateService:a}=this.context;e.getOnChange().unsubscribe(this,this._handleChangeFavorites),a.getOnChange().unsubscribe(this,this._handleChangeMenuViewState),t.getOnChange().unsubscribe(this,this._handleChangeCustoms),r.linking.interval.unsubscribe(this._handleChangeInterval),r.linking.intraday.unsubscribe(this._bindedForceUpdate),r.linking.seconds.unsubscribe(this._bindedForceUpdate),r.linking.ticks.unsubscribe(this._bindedForceUpdate),r.linking.range.unsubscribe(this._bindedForceUpdate),r.linking.supportedResolutions.unsubscribe(this._bindedForceUpdate),r.linking.dataFrequencyResolution.unsubscribe(this._bindedForceUpdate),Ue.globalCloseDelegate.unsubscribe(this,this._handleGlobalClose)}componentDidUpdate(e,t){this.state.lastAddedInterval&&setTimeout((()=>this.setState({lastAddedInterval:void 0})),1600)}render(){const{isShownQuicks:e,id:t}=this.props,{activeInterval:a,customs:i,lastNotQuicked:s,isAddIntervalDialogOpened:o}=this.state,l=this._getQuicks(),r=(0,ke.sortResolutions)([...l]);void 0!==a&&r.includes(a)?void 0!==s&&r.push(s):void 0!==a&&r.push(a);const h=(!(!e||0===l.length)||void 0)&&r.length>1,c={},m=(0,ke.mergeResolutions)(this._defaultIntervals,i)
;(void 0!==a?m.concat(a):m).filter(ke.isAvailable).forEach((e=>c[e]=!0));const v=void 0!==a?(0,ke.getTranslatedResolutionModel)(a):null;return n.createElement(p,{id:t},h&&n.createElement(R,{className:Be.group,orientation:"horizontal"},r.map(((e,t)=>{const i=(0,ke.getTranslatedResolutionModel)(e);return n.createElement(S,{key:t,role:"radio",className:u(Be.button,{[Be.first]:0===t,[Be.last]:t===r.length-1}),text:n.createElement(Te,{value:i.mayOmitMultiplier?void 0:i.multiplier,metric:i.shortKind}),hint:i.hint,"aria-checked":a===e,isActive:a===e,isDisabled:!c[e],onClick:this._handeQuickClick,onClickArg:e,"data-value":e})}))),n.createElement(_.MatchMedia,{rule:y.DialogBreakpoints.TabletSmall},(e=>n.createElement(n.Fragment,null,n.createElement(De.CloseDelegateContext.Provider,{value:je},n.createElement(d.ToolbarMenuButton,{arrow:Boolean(h),closeOnClickOutside:!o,content:h||null===v?void 0:n.createElement(p,{className:Be.menuContent},n.createElement(Te,{value:v.mayOmitMultiplier?void 0:v.multiplier,metric:v.shortKind})),hotKey:h?Ke:void 0,className:Be.menu,ref:this._menu,isDrawer:e,onClick:this._trackClick,tooltip:h||null===v?Ze.timeInterval:v.hint,menuReference:this._menuItemsContainerRef,menuRole:"treegrid",drawerReference:this._drawerContainerRef},n.createElement("div",{className:Be.dropdown},this._renderChildren(m,e)))),o&&n.createElement(n.Suspense,{fallback:null},n.createElement(Qe,{onAdd:this._handleAddInterval,onClose:this._handleCloseAddIntervalDialog,onUnmount:this._handleCloseAddIntervalDialog,intervalService:this.context.intervalService,isSmallTablet:e}))))))}_createMenuItems(e,t,a,i){const n=e.map(((e,n,s)=>this._renderResolutionsGroup(e,1===s.length,t,n+1+a,i)));return function(e){let t=!1;return e.filter(((e,a,i)=>{let n=!0;return e.type===C.PopupMenuSeparator&&(0!==a&&a!==i.length-1||(n=!1),t&&(n=!1)),t=e.type===C.PopupMenuSeparator,n}))}([].concat(...n))}_createIntervalItem(e,t){return this._customIntervals?[n.createElement(Ge,{key:"add-dialog","aria-level":1,"aria-posinset":1,"aria-setsize":t,isSmallTablet:e,onClick:this._handleOpenAddIntervalDialog}),n.createElement(C.PopupMenuSeparator,{key:"custom-interval-separator"})]:[]}_renderResolutionsGroup(e,t=!1,a,i,s){const o=[],l=t?1:2,r=e.items.map(((t,i)=>this._renderPopupMenuItem(t,a,i+1,e.items.length,l)));if(t)o.push(...r);else if(a){const t=n.createElement($e,{key:e.id,title:e.name},r);o.push(t)}else{const{intervalsMenuViewStateService:t}=this.context,{menuViewState:a}=this.state;if(!t.isAllowed(e.id))return[];const l=n.createElement(Le,{key:e.id,role:"row","aria-posinset":i,"aria-setsize":s,"aria-level":1,className:Be.section,summary:e.name,open:!a[e.id],onStateChange:this._getHandleSectionStateChange(e.id)},r);o.push(l)}return(!e.mayOmitSeparator||e.items.length>1)&&(o.unshift(n.createElement(C.PopupMenuSeparator,{key:`begin-${e.name}`})),o.push(n.createElement(C.PopupMenuSeparator,{key:`end-${e.name}`}))),o}_handleChangeFavorites(e){this.setState({lastNotQuicked:void 0,favorites:e})}_handleChangeCustoms(e){this.setState({customs:e})}
_handleChangeMenuViewState(e){this.setState({menuViewState:e},(()=>{this._menu.current&&this._menu.current.update()}))}_renderPopupMenuItem(e,t,a,i,s){const{isFavoritingAllowed:o}=this.props,{activeInterval:l,lastAddedInterval:r}=this.state,h=e===l,c=(0,ke.isAvailable)(e),d=this._isIntervalFavorite(e),u=this._isIntervalDefault(e),m=(0,ke.getTranslatedResolutionModel)(e);return n.createElement(Ne,{key:e,role:"row","aria-level":s,"aria-posinset":a,"aria-setsize":i,"aria-selected":h,isSmallTablet:t,interval:e,hint:m.hint,isSignaling:r===e,isFavoritingAllowed:o,isDisabled:!c,isFavorite:d,isRemovable:!u,isActive:h,onClick:this._handleSelectInterval,onClickRemove:this._handleRemoveInterval,onClickFavorite:this._handleClickFavorite,scrollContainerRef:t?this._drawerContainerRef:this._menuItemsContainerRef,"data-value":e})}_isIntervalDefault(e){return this._defaultIntervals.includes(e)}_isIntervalFavorite(e){return this.state.favorites.includes(e)}_getQuicks(e){return this.props.isShownQuicks&&"small"!==this.props.displayMode?void 0===e?this.state.favorites:e:[]}_trackClick(){0}}function Ge(e){const{onClick:t,className:i,isSmallTablet:s,...o}=e;return n.createElement(A.AccessibleMenuItem,{...o,key:"add-dialog",role:"row","aria-haspopup":"dialog",className:u(Be.addCustomInterval,!s&&Be.desktop,i),onClick:t,label:(0,Ve.appendEllipsis)(l.t(null,void 0,a(80335))),icon:ze,dontClosePopup:!0})}function $e(e){const{children:t,title:a,className:i}=e;return n.createElement("div",{className:i},n.createElement("div",{className:Be.smallTabletSectionTitle},a),t)}Xe.contextType=qe;var Ye=a(36488),Je=a(82436);const et=(0,f.registryContextType)();class tt extends n.PureComponent{constructor(e,t){super(e,t),this._handleClick=()=>{const{chartWidgetCollection:e,windowMessageService:t,isFundamental:a}=this.context,i=e.activeChartWidget.value();const n=function(){const e=new URL(window.location.href),t=JSON.parse(decodeURIComponent(e.hash.slice(1)));return delete t.show_popup_button,e.hash=`#${encodeURIComponent(JSON.stringify(t))}`,e.toString()}();i.withModel(null,(()=>{t.post(parent,"openChartInPopup",{symbol:i.model().mainSeries().actualSymbol(),interval:i.model().mainSeries().interval(),fundamental:a,url:n})}))},(0,f.validateRegistry)(t,{isFundamental:s.any,chartWidgetCollection:s.any.isRequired,windowMessageService:s.any.isRequired})}render(){const{className:e}=this.props;return n.createElement(G.ToolbarIconButton,{className:u(e,Ye.button),icon:Je,onClick:this._handleClick,tooltip:l.t(null,void 0,a(34770))})}}tt.contextType=et;var at=a(34369);const it=(0,f.registryContextType)();class nt extends n.PureComponent{constructor(e,t){super(e,t),this._updateState=e=>{this.setState({isOpened:e})},this._handleClick=e=>{const{chartWidgetCollection:t}=this.context,a=t.activeChartWidget.value();(0,K.trackEvent)("GUI","Chart Header Toolbar","chart properties"),a.showGeneralChartProperties(void 0,{shouldReturnFocus:(0,q.isKeyboardClick)(e)})},(0,f.validateRegistry)(t,{chartWidgetCollection:s.any.isRequired}),this.state={isOpened:!1},
this._propertiesDialogRenderer=this.context.chartWidgetCollection.getChartPropertiesDialogRenderer()}componentDidMount(){this._propertiesDialogRenderer?.visible().subscribe(this._updateState)}componentWillUnmount(){this._propertiesDialogRenderer?.visible().unsubscribe(this._updateState)}render(){const{isOpened:e}=this.state;return n.createElement(G.ToolbarIconButton,{...this.props,icon:at,isOpened:e,onClick:this._handleClick,tooltip:l.t(null,void 0,a(32514))})}}nt.contextType=it;var st=a(26709),ot=a(56616),lt=a(49483),rt=a(40173),ht=a(56127),ct=a(10428),dt=a(17479);(0,rt.mergeThemes)(ct.DEFAULT_MENU_ITEM_SWITCHER_THEME,dt);var ut=a(52464);function mt(e){const{wasChanges:t,isSaving:i,className:s}=e;return n.createElement("span",{className:u(ut.saveString,!t&&!i&&ut.hidden,s)},i?n.createElement(me.Loader,{className:ut.loader,size:"small",staticPosition:!0}):l.t(null,void 0,a(64e3)))}var vt=a(27830),pt=a(36296),gt=a(13090),bt=a(18603),St=a(75394);a(22315);const Ct=i.enabled("widget"),_t=i.enabled("save_shortcut"),yt=(0,rt.mergeThemes)(L.DEFAULT_TOOL_WIDGET_BUTTON_THEME,St),wt=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{shortcut:bt.shortcut,withIcon:bt.withIcon}),ft=((0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{toolbox:bt.toolbox}),(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{toolbox:bt.toolboxSmall}),l.t(null,void 0,a(40115))),kt=l.t(null,void 0,a(26276)),Et=[],Mt=(0,$.hotKeySerialize)({keys:[(0,Y.humanReadableModifiers)(Y.Modifiers.Mod,!1),"S"],text:"{0} + {1}"}),Tt=()=>null;class xt extends n.PureComponent{constructor(e){super(e),this._copyElRef=n.createRef(),this._menuRef=n.createRef(),this._handleCopyLinkClick=()=>Promise.resolve(),this._handleCloneClick=()=>{this.props.onCloneChart?.()},this._handleSaveClick=()=>{this.props.onSaveChart?.(),this._trackClick()},this._handleSaveAsClick=()=>{this.props.onSaveAsChart?.()},this.state={isSaving:!1}}componentDidUpdate(e,t){e.isProcessing&&!this.props.isProcessing&&(clearTimeout(this._timeout),this._timeout=void 0,this.setState({isSaving:!1})),!e.recentItems?.length&&this.props.recentItems?.length&&this._menuRef.current?.update(),!e.isProcessing&&this.props.isProcessing&&(this._timeout=setTimeout((()=>{this.setState({isSaving:!0})}),1e3))}componentWillUnmount(){this._timeout&&clearTimeout(this._timeout)}render(){const{id:e,isReadOnly:t,displayMode:i,isProcessing:s,title:r,wasChanges:h,hideMenu:c,isTabletSmall:m,onOpenMenu:v,dataNameSaveMenu:g,isSaveDialogOpened:b,chartList:S,favorites:C,showRemoveFavoriteHint:_,onButtonClick:y,onAdditionalButtonClick:w}=this.props,f=!t&&!c;let k=[];vt.showFavoriteLayouts&&S&&C&&(k=S.filter((e=>C[e.id])).sort(((e,t)=>e.name.localeCompare(t.name))));const E=!(h||!r||this.state.isSaving),M=n.createElement("div",{className:bt.textWrap},n.createElement("span",{className:bt.text},n.createElement(ht.LeadingEmojiText,{text:r||l.t(null,void 0,a(64e3))})),n.createElement(mt,{isSaving:this.state.isSaving,wasChanges:h}));return n.createElement(p,null,t?n.createElement(p,null,n.createElement(Z,{id:e,displayMode:i,
icon:n.createElement(o.Icon,{icon:pt}),isDisabled:s,onClick:this._handleCloneClick,text:l.t(null,void 0,a(49680)),collapseWhen:Et,tooltip:ft})):n.createElement(p,null,n.createElement(Z,{id:e,className:u(bt.button,f&&bt.buttonSmallPadding),displayMode:i,"aria-disabled":!!E||void 0,isDisabled:s,onClick:E?void 0:this._handleSaveClick,text:M,theme:yt,collapseWhen:Et,isOpened:b,tooltip:E?l.t(null,void 0,a(89771)):l.t(null,void 0,a(15356)),"data-tooltip-hotkey":Ct||E?"":_t?Mt:""}),f&&n.createElement("div",{className:bt.menuBtnWrap},n.createElement(d.ToolbarMenuButton,{"data-name":g,arrow:!0,ref:this._menuRef,isDrawer:m,drawerPosition:"Bottom",onClick:this._trackClick,onOpen:v,tooltip:l.t(null,void 0,a(38973)),menuClassName:u(!m&&bt.menu),menuRole:"grid"},this._renderMenuItems(Boolean(m))),vt.showFavoriteLayouts&&_&&k.length>FAVORITE_LAYOUTS_LIMIT&&n.createElement(n.Suspense,{fallback:null},n.createElement(Tt,{onClick:y,onAdditionalClick:w,placement:"auto",theme:undefined,buttonSize:"small",saveHintStateByClick:!1,text:l.t(null,{count:k.length,plural:"Your favorite layouts have moved to the top toolbar. You currently have {count} favorites. You can reduce the number to find them easier — unfavorite all in just one click or set up your choice manually.",replace:{count:k.length.toString()}},a(6094)),buttonText:l.t(null,void 0,a(66418)),additionalButtonText:l.t(null,void 0,a(36291)),settingsHintKey:"hint.favoriteLayouts",placeHolderClassName:bt.hintPlaceholder,hideWithReference:!0,hintStateHandler:favoriteLayoutsHintStateHandler}))),!1))}_renderMenuItems(e){const{wasChanges:t,isProcessing:i,chartId:s,onSaveChartFromMenu:o,onRenameChart:r,onLoadChart:h,onNewChart:c,isAutoSaveEnabled:d,autoSaveId:m,sharingId:v,onAutoSaveChanged:p,isSharingEnabled:g,onSharingChanged:b,recentItems:S,onExportData:_,isAuthenticated:y,favorites:f}=this.props,k=e?w.multilineLabelWithIconAndToolboxTheme:wt,E=e?void 0:(0,Y.humanReadableHash)(Y.Modifiers.Mod+83),M=e?void 0:l.t(null,{context:"hotkey"},a(23821)),T=[];return T.push(n.createElement(A.AccessibleMenuItem,{key:"save",role:"row",isDisabled:Boolean(i||!t&&s),label:kt,onClick:o,shortcut:E,labelRowClassName:u(e&&bt.popupItemRowTabletSmall),theme:k,"data-name":"save-load-menu-item-save"})),void 0!==s&&T.push(n.createElement(A.AccessibleMenuItem,{key:"rename",role:"row",icon:void 0,label:(0,Ve.appendEllipsis)(l.t(null,void 0,a(6321))),onClick:r,labelRowClassName:u(e&&bt.popupItemRowTabletSmall),theme:k,"data-name":"save-load-menu-item-rename","aria-haspopup":"dialog"}),n.createElement(A.AccessibleMenuItem,{key:"save-as",role:"row",icon:void 0,label:(0,Ve.appendEllipsis)(ft),onClick:this._handleSaveAsClick,labelRowClassName:u(e&&bt.popupItemRowTabletSmall),theme:k,"data-name":"save-load-menu-item-clone","aria-haspopup":"dialog"})),T.push(n.createElement(C.PopupMenuSeparator,{key:"all-layouts-separator"}),n.createElement(A.AccessibleMenuItem,{key:"all-layouts",role:"row",className:"js-save-load-menu-item-load-chart",label:(0,Ve.appendEllipsis)(l.t(null,void 0,a(65433))),onClick:h,icon:gt,
labelRowClassName:u(e&&bt.popupItemRowTabletSmall),theme:k,shortcut:M,"data-name":"save-load-menu-item-load","aria-haspopup":"dialog"})),T}_trackClick(){0}}const It=(0,f.registryContextType)();class Rt extends n.PureComponent{constructor(e,t){super(e,t),this._exportDialogPromise=null,this._recentLayoutsAbortController=null,this._requestRecentLayouts=()=>{this._recentLayoutsAbortController?.abort()},this._updateState=e=>{this.setState((t=>({...t,...e})))},this._handleExportData=()=>{0},this._onSaveDialogVisibleChange=e=>{this.setState({isSaveDialogOpened:e})},this._syncState=e=>{this.setState(e)},this._handleAdditionalButton=()=>{const{loadChartService:e}=this.context;e.deleteAllFavorites()},this._onChangeHasChanges=e=>{this.state.wasChanges!==e&&this.setState({wasChanges:e})},this._onChangeAutoSaveEnabled=e=>{0},this._onChangeSharingEnabled=e=>{this.setState({isSharingEnabled:e})},this._onChangeTitle=e=>{this.setState({title:e})},this._onChangeId=e=>{this.setState({id:e})},this._onChartAboutToBeSaved=()=>{this.setState({isProcessing:!0})},this._onChartSaved=()=>{this.setState({isProcessing:!1})},this._handleAutoSaveEnabled=e=>{0},this._handleSharingEnabled=e=>{0},this._handleClickSave=()=>{this.context.saveChartService.saveChartOrShowTitleDialog(),this._trackEvent("Save click")},this._handleOpenMenu=()=>{this._requestRecentLayouts()},this._handleClickSaveFromMenu=()=>{this.context.saveChartService.saveChartOrShowTitleDialog(),this._trackEvent("Save From Menu")},this._handleClickClone=()=>{this.context.saveChartService.cloneChart()},this._handleClickSaveAs=()=>{this.context.saveChartService.saveChartAs(),this._trackEvent("Make a copy")},this._handleClickNew=()=>{this._trackEvent("New chart layout")},this._handleClickLoad=()=>{this.context.loadChartService.showLoadDialog()},this._handleHotkey=()=>{this.context.loadChartService.showLoadDialog()},this._handleClickRename=()=>{this.context.saveChartService.renameChart(),this._trackEvent("Rename")},(0,f.validateRegistry)(t,{chartWidgetCollection:s.any.isRequired,chartChangesWatcher:s.any.isRequired,saveChartService:s.any.isRequired,sharingChartService:s.any,loadChartService:s.any.isRequired});const{chartWidgetCollection:a,chartChangesWatcher:i,saveChartService:n,sharingChartService:o,loadChartService:l}=t;this._loadChartServiceState=l.state(),this.state={isAuthenticated:window.is_authenticated,isProcessing:!1,id:a.metaInfo.id.value(),title:a.metaInfo.name.value(),wasChanges:i.hasChanges(),iconHovered:!1,isSaveDialogOpened:!1,...this._loadChartServiceState.value()}}componentDidMount(){const{chartSaver:e,isFake:t,stateSyncEmitter:i}=this.props,{chartWidgetCollection:n,chartChangesWatcher:s,saveChartService:o,sharingChartService:r,loadChartService:h}=this.context;t?i.on("change",this._syncState):(s.getOnChange().subscribe(this,this._onChangeHasChanges),n.metaInfo.name.subscribe(this._onChangeTitle),n.metaInfo.id.subscribe(this._onChangeId),this._hotkeys=(0,st.createGroup)({desc:"Save/Load"}),this._hotkeys.add({desc:l.t(null,void 0,a(29517)),handler:this._handleHotkey,
hotkey:190}),e.chartSaved().subscribe(this,this._onChartSaved),e.chartAboutToBeSaved().subscribe(this,this._onChartAboutToBeSaved),window.loginStateChange.subscribe(this,this._onLoginStateChange),this.context.saveChartService.getCreateController().visible().subscribe(this._onSaveDialogVisibleChange),this._loadChartServiceState.subscribe(this._updateState),vt.showFavoriteLayouts&&h.refreshChartList())}componentDidUpdate(e,t){this.props.isFake||t!==this.state&&this.props.stateSyncEmitter.emit("change",this.state)}componentWillUnmount(){const{chartSaver:e,isFake:t,stateSyncEmitter:a}=this.props,{chartWidgetCollection:i,chartChangesWatcher:n,saveChartService:s,sharingChartService:o}=this.context;t?a.off("change",this._syncState):(n.getOnChange().unsubscribe(this,this._onChangeHasChanges),i.metaInfo.name.unsubscribe(this._onChangeTitle),i.metaInfo.id.unsubscribe(this._onChangeId),(0,se.ensureDefined)(this._hotkeys).destroy(),e.chartSaved().unsubscribe(this,this._onChartSaved),e.chartAboutToBeSaved().unsubscribe(this,this._onChartAboutToBeSaved),window.loginStateChange.unsubscribe(this,this._onLoginStateChange),this._recentLayoutsAbortController?.abort(),this.context.saveChartService.getCreateController().visible().unsubscribe(this._onSaveDialogVisibleChange),this._loadChartServiceState.unsubscribe(this._updateState))}render(){const{isReadOnly:e,displayMode:t,id:a,isFake:i}=this.props,{isProcessing:s,isAuthenticated:o,title:l,id:r,wasChanges:h,isAutoSaveEnabled:c,isSharingEnabled:d,isSaveDialogOpened:u,favorites:m,chartList:v,recents:p}=this.state,g={displayMode:t,isReadOnly:e,isAuthenticated:o,isProcessing:s,wasChanges:h,title:l,id:a,isSaveDialogOpened:u,favorites:m,chartList:v,chartId:null!==r?r:void 0,dataNameSaveMenu:i?void 0:"save-load-menu",showRemoveFavoriteHint:!i,onCloneChart:this._handleClickClone,onSaveChart:this._handleClickSave,onSaveChartFromMenu:this._handleClickSaveFromMenu,onRenameChart:this._handleClickRename,onSaveAsChart:this._handleClickSaveAs,onLoadChart:this._handleClickLoad,onButtonClick:this._handleClickLoad,onAdditionalButtonClick:this._handleAdditionalButton};return n.createElement(_.MatchMedia,{rule:y.DialogBreakpoints.TabletSmall},(e=>n.createElement(xt,{...g,isTabletSmall:e})))}_onLoginStateChange(){const e=window.is_authenticated;this.setState({isAuthenticated:e}),vt.showFavoriteLayouts&&this.context.loadChartService.refreshChartList()}_trackEvent(e){0}}Rt.contextType=It;var At=a(7986),Nt=a(52388),Ft=a(13665);const Ht=new Nt.DateTimeFormatter({dateTimeSeparator:"_",timeFormat:"%h-%m-%s"}),Ot={takeSnapshot:l.t(null,void 0,a(87972))},Dt=(0,f.registryContextType)();var Ut=a(75774);function Wt(e,t,i){return async function(e,t,i){const n=URL.createObjectURL(new Blob([`<!doctype html><html style="background-color:${getComputedStyle(document.documentElement).backgroundColor}"><head><meta charset="utf-8"><title>${l.t(null,void 0,a(93292))}</title></head><body style="background-color:${getComputedStyle(document.body).backgroundColor}"></body></html>`],{type:"text/html"}));try{
Ut.isIOS&&await new Promise((e=>setTimeout(e)));const a=open(n,t,i);if(!a)throw new Error("cound not open a new tab");const s=await e.catch((()=>{}));void 0!==s?a.location.replace(s):a.close()}finally{URL.revokeObjectURL(n)}}(e,t,i)}var Pt=a(7372),Lt=a(21569);function Vt(e){const t=u(e.isLoading&&Lt.hidden),a=u(!e.isLoading&&Lt.hidden);return n.createElement("div",null,n.createElement("span",{className:t},e.children),n.createElement("span",{className:a},n.createElement(me.Loader,null)))}var Bt=a(76974),zt=a(67487),Zt=a(1457),Kt=a(23595),qt=a(29414),jt=a(99280),Qt=a(63295);const Xt=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,Qt);function Gt(e){const{serverSnapshot:t,clientSnapshot:i,hideShortcuts:s}=e,[o,r]=(0,n.useState)(!1),[h,c]=(0,n.useState)(!1),[d,m]=(0,n.useState)(!1),[v,p]=(0,n.useState)(!1),[g,b]=(0,n.useState)(!1),S=(0,Bt.useIsMounted)(),C=(0,n.useCallback)((async()=>{const e=i(),t=e.then((e=>new Promise((t=>e.canvas.toBlob((e=>{null!==e&&t(e)}))))));try{await(0,ot.writePromiseUsingApi)(t,"image/png"),be.emit("onClientScreenshotCopiedToClipboard")}catch(t){const{canvas:a}=await e;window.open()?.document.write(`<img width="100%" src="${a.toDataURL()}"/>`)}}),[i]),_=(0,n.useCallback)((async()=>{const e=await i(),t=await function(e){return new Promise((t=>{try{e.canvas.toBlob((e=>{if(null===e)throw new Error("Unable to generate blob");t(URL.createObjectURL(e))}))}catch{t(e.canvas.toDataURL())}}))}(e);t&&(0,Pt.downloadFile)(`${e.name}.png`,t)}),[i]),y=e=>Wt(e.then((e=>e.imageUrl))),w=(0,n.useCallback)((async(e=!1)=>{const a=t();try{if(e)await y(a);else{const e=a.then((e=>new Blob([e.imageUrl],{type:"text/plain"})));await(0,ot.writePromiseUsingApi)(e,"text/plain"),be.emit("onServerScreenshotCopiedToClipboard")}return!0}catch(e){return y(a),!0}finally{S.current&&(c(!1),r(!1),(0,Ue.globalCloseMenu)())}}),[t]),f=((0,n.useCallback)((async()=>{const e=t();try{const t=720,a=e.then((async e=>{const a=await snapshoter().getSnapshot(e.symbol,new Set(["description"])),i="error"!==a.status?a.values.description:e.symbol,n=e.imageUrl.match(/\/x\/([0-9a-zA-Z]{8})/)?.[1]??"",s=createSnapshotImageUrls(n)?.url;return new Blob([`<img width="${t}" loading="lazy" src="${s}"/><p><a href="https://www.tradingview.com${getSymbolPagePath({shortName:e.symbol})}">${i} chart</a> by TradingView</p>`],{type:"text/plain"})}));return await(0,ot.writePromiseUsingApi)(a,"text/plain"),be.emit("onServerScreenshotEmbedCodeCopiedToClipboard"),!0}catch(t){return y(e),!0}finally{S.current&&(m(!1),(0,Ue.globalCloseMenu)())}}),[t]),(0,n.useCallback)((async()=>{p(!0);const[e,i]=await Promise.all([a.e(4665).then(a.bind(a,66921)),t()]);e.Twitter.shareSnapshotInstantly(i.symbol,i.imageUrl),S.current&&(p(!1),(0,Ue.globalCloseMenu)())}),[t]));return n.createElement(n.Fragment,null,n.createElement(ge.ToolWidgetMenuSummary,null,l.t(null,void 0,a(58425))),n.createElement(A.AccessibleMenuItem,{role:"row","data-name":"save-chart-image",label:l.t(null,void 0,a(26200)),icon:Kt,onClick:_,shortcut:s?void 0:(0,
Y.humanReadableHash)(Y.Modifiers.Mod+Y.Modifiers.Alt+83),theme:Xt}),n.createElement(A.AccessibleMenuItem,{role:"row","data-name":"copy-chart-image",label:l.t(null,void 0,a(96554)),icon:Zt,onClick:C,shortcut:s?void 0:(0,Y.humanReadableHash)(Y.Modifiers.Mod+Y.Modifiers.Shift+83),theme:Xt}),!(0,lt.onWidget)()&&n.createElement(A.AccessibleMenuItem,{role:"row","data-name":"copy-link-to-the-chart-image",label:n.createElement(Vt,{isLoading:o},l.t(null,void 0,a(81939))),icon:qt,onClick:()=>{r(!0),w(!1)},dontClosePopup:!0,isDisabled:o,shortcut:s?void 0:(0,Y.humanReadableHash)(Y.Modifiers.Alt+83),className:u(o&&Qt.loading),theme:Xt}),!1,!(0,lt.onWidget)()&&!(0,lt.isSymphonyEmbed)()&&n.createElement(A.AccessibleMenuItem,{role:"row","data-name":"open-image-in-new-tab",label:n.createElement(Vt,{isLoading:h},l.t(null,void 0,a(19159))),icon:jt,onClick:()=>{c(!0),w(!0)},dontClosePopup:!0,isDisabled:h,className:u(h&&Qt.loading),theme:Xt}),!(0,lt.onWidget)()&&!(0,lt.isSymphonyEmbed)()&&n.createElement(A.AccessibleMenuItem,{role:"row","data-name":"tweet-chart-image",label:n.createElement(Vt,{isLoading:v},l.t(null,void 0,a(28239))),icon:zt,onClick:f,dontClosePopup:!0,isDisabled:v,className:u(v&&Qt.loading),theme:Xt}),!1)}var $t=a(84015);function Yt(e){const[t,a]=(0,n.useState)(!1),i=(0,Bt.useIsMounted)(),s=(0,n.useCallback)((async()=>{a(!0),await e.serverSnapshot(),i.current&&a(!1)}),[e.serverSnapshot]);return n.createElement(L.ToolWidgetButton,{id:e.id,className:e.className,isDisabled:t,onClick:s,title:e.tooltip,icon:e.icon})}var Jt=a(72644);const ea=(ta=function(e){return(0,$t.isOnMobileAppPage)("any")?n.createElement(Yt,{...e,icon:Jt}):n.createElement(d.ToolbarMenuButton,{content:n.createElement(L.ToolWidgetButton,{tag:"div",id:e.id,className:e.className,icon:Jt}),drawerPosition:"Bottom",drawerBreakpoint:y.DialogBreakpoints.TabletSmall,arrow:!1,onClick:function(){},tooltip:e.tooltip,menuRole:"grid"},n.createElement(Gt,{...e}))},(aa=class extends n.PureComponent{constructor(e,t){super(e,t),this._clientSnapshot=async()=>{const e=this.context.chartWidgetCollection.activeChartWidget.value().model().mainSeries().actualSymbol();return{canvas:await this.context.chartWidgetCollection.clientSnapshot(),name:`${(0,Ft.shortName)(e)}_${Ht.formatLocal(new Date)}`}},this._serverSnapshot=async()=>{const e=this.context.chartWidgetCollection.activeChartWidget.value().model().mainSeries().actualSymbol(),t=await this.context.chartWidgetCollection.takeScreenshot(),a=void 0!==this.context.snapshotUrl?t:(0,At.convertImageNameToUrl)(t);return{symbol:(0,Ft.shortName)(e),imageUrl:a}},(0,f.validateRegistry)(t,{chartWidgetCollection:s.any.isRequired})}render(){const{className:e,id:t}=this.props;return n.createElement(ta,{id:t,className:e,tooltip:Ot.takeSnapshot,serverSnapshot:this._serverSnapshot,clientSnapshot:this._clientSnapshot})}}).contextType=Dt,aa);var ta,aa,ia=a(79652),na=a(39362),sa=a(50340);class oa{async show(e){if(null!==oa._provider){const e=await oa._provider.getSymbol();return r.linking.setSymbolAndLogInitiator(e.symbol,"symbol search UI"),e}
if(oa._currentShowingInstance)throw new DOMException("SymbolSearchUI is already shown","InvalidStateError");try{oa._currentShowingInstance=this,oa.preload();const t=await oa._implementation;return(0,se.assert)(null!==t),new Promise((a=>{t.showDefaultSearchDialog({...e,onSearchComplete:e=>{a({symbol:e})}})}))}finally{oa._currentShowingInstance=null}}static setProvider(e){this._provider=e}static preload(){null===this._provider&&null===this._implementation&&(this._implementation=(0,sa.loadNewSymbolSearch)())}}oa._currentShowingInstance=null,oa._provider=null,oa._implementation=null;var la=a(29142),ra=a(92876);const ha=(0,rt.mergeThemes)(g.DEFAULT_TOOLBAR_BUTTON_THEME,ra),ca=(0,f.registryContextType)();class da extends n.PureComponent{constructor(e,t){super(e,t),this._openSymbolSearchDialog=async e=>{if((0,Y.modifiersFromEvent)(e)===Y.Modifiers.Alt)return void(0,ot.getClipboard)().writeText(this.state.symbol);if(this.state.isOpened)return;const{chartWidgetCollection:t}=this.context,s=t?.activeChartWidget.value().model().model(),o=s?.mainSeries().symbolInfo(),r="option"===o?.type;try{(0,K.trackEvent)("GUI","SS","main search");let t=this._isSpread(this.state.symbol)?this.state.symbol:this.state.shortName;if(i.enabled("symbol_search_option_chain_selector")&&r){const e=await resolveUnderlyingSymbol(this.state.symbol);e&&(t={type:"option",value:this.state.symbol,underlying:e})}await(new oa).show({trackResultsOptions:{trackResults:!1,emptySearchType:"empty_result__supercharts"},onClose:()=>{this.setState({isOpened:!1})},onOpen:()=>{this.setState({isOpened:!0})},shouldReturnFocus:(0,q.isKeyboardClick)(e),defaultValue:t,showSpreadActions:(0,ia.canShowSpreadActions)()&&this.props.isActionsVisible,source:"searchBar",enableOptionsChain:i.enabled("symbol_search_option_chain_selector"),footer:Ie.mobiletouch?void 0:n.createElement(na.SymbolSearchDialogFooter,null,l.t(null,void 0,a(50021)))})}catch(e){}},this._isSpread=e=>!1,this._onSymbolChanged=()=>{const e=r.linking.proSymbol.value();this.setState({symbol:e,shortName:ua(e)})},(0,f.validateRegistry)(t,{chartWidgetCollection:s.any.isRequired}),this.state={symbol:r.linking.proSymbol.value(),shortName:ua(r.linking.proSymbol.value()),isOpened:!1}}componentDidMount(){r.linking.proSymbol.subscribe(this._onSymbolChanged),oa.preload()}componentWillUnmount(){r.linking.proSymbol.unsubscribe(this._onSymbolChanged)}render(){const{id:e,className:t}=this.props;return n.createElement(g.ToolbarButton,{id:e,className:m()(t,i.enabled("uppercase_instrument_names")&&ra.uppercase,ra.smallLeftPadding),theme:ha,icon:la,isOpened:this.state.isOpened,text:this.state.shortName,onClick:this._openSymbolSearchDialog,tooltip:l.t(null,void 0,a(51165))})}async _updateQuotes(e){}}function ua(e){return function(e){return e.includes(":")?e.split(":"):["",e]}(e)[1]}da.contextType=ca;var ma=a(75313);function va(e){return n.createElement("div",{className:u(ma.description,e.className)},e.children)}var pa=a(6917);const ga=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{labelRow:pa.labelRow,toolbox:pa.toolbox,
item:pa.titleItem}),ba=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{labelRow:pa.labelRow,toolbox:pa.toolbox,item:pa.titleItemTabletSmall}),Sa=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{item:pa.item}),Ca=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,{item:pa.itemTabletSmall});function _a(e){const{role:t,className:a,item:i,description:s,favorite:o,isFavoritingAllowed:l,isTabletSmall:r,onApply:h,onRemove:c,onFavor:d,isActive:u,descriptionDataName:v,...p}=e,g=p,[b,S]=(0,xe.useHover)(),C=r?ba:ga,_=r?Ca:Sa,y=(0,n.useCallback)((()=>h(i)),[h,i]),w=(0,n.useCallback)((e=>c(i,e)),[c,i]),f=(0,n.useCallback)((()=>{d&&d(i)}),[d,i]);return n.createElement("div",{...S,...g,className:m()(a,pa.wrap)},n.createElement(A.AccessibleMenuItem,{role:t,theme:C,label:n.createElement(ht.LeadingEmojiText,{text:i.name}),labelRowClassName:m()(r&&pa.itemLabelTabletSmall),isHovered:b,showToolboxOnHover:!o&&!b,showToolboxOnFocus:!0,onClick:y,isActive:u,toolbox:n.createElement(n.Fragment,null,!i.isDefault&&n.createElement(Re.MenuRemoveButton,{key:"remove",hidden:!Ie.touch&&!b,onClick:w,className:m()(pa.remove,u&&pa.active)}),Boolean(d)&&l&&n.createElement(N.MenuFavoriteButton,{key:"favorite",isFilled:Boolean(o),onClick:f}))}),s&&n.createElement(oe.PopupMenuItem,{theme:_,label:n.createElement(va,{className:m()(pa.description,r&&pa.descriptionTabletSmall)},s),onClick:y,isHovered:b,isActive:u,"data-name":v}))}var ya=a(76032);function wa(e){const{item:t}=e,a={...t,isDefault:t.is_default},i={"data-name":t.name,"data-id":t.id,"data-is-default":Boolean(t.is_default)},s=t.meta_info,o=s?(0,ya.getStudyTemplateDescString)(s.indicators):void 0;return n.createElement(_a,{...e,...i,description:o,item:a})}var fa=a(97357);const ka=(0,rt.mergeThemes)(oe.DEFAULT_POPUP_MENU_ITEM_THEME,fa);function Ea(e){const{onClick:t,isTabletSmall:a,className:i,icon:s,title:l,...r}=e;return n.createElement(A.AccessibleMenuItem,{...r,theme:ka,className:m()(i,fa.wrap),label:n.createElement("div",{className:fa.titleWrap},n.createElement("div",{className:m()(fa.title,a&&fa.titleTabletSmall)},n.createElement(o.Icon,{className:fa.icon,icon:s}),n.createElement("div",{className:fa.text},l))),onClick:t})}var Ma=a(36947),Ta=a(64706);const xa=n.createContext(null),Ia=!1;var Ra=a(53707),Aa=a(72154);function Na(e){const{templates:t,recents:i,onTemplateSave:s,onTemplateRemove:o,onTemplateSelect:r,onTemplateFavorite:h,isTabletSmall:c,isLoading:d,onOpenTemplatesDialog:u}=e,v=(0,n.useMemo)((()=>t.filter((e=>e.is_default))),[t]),p=(0,n.useMemo)((()=>t.filter((e=>!e.is_default))),[t]),g=(0,n.useContext)(xa),b=(0,n.useContext)(Ta.MenuContext),S=(0,Ma.useForceUpdate)();(0,n.useEffect)((()=>{if(null!==g){const e={};return g.getOnChange().subscribe(e,(()=>{S(),b&&b.update()})),()=>g.getOnChange().unsubscribeAll(e)}return()=>{}}),[]);const _=(e,t)=>n.createElement(wa,{key:e.name,role:"row",item:e,isFavoritingAllowed:Boolean(h),favorite:null!==e.favorite_date,onApply:r,onFavor:h,onRemove:o,isTabletSmall:c,"data-group-name":t});return n.createElement("div",{role:"treegrid",
className:m()(Aa.menu,c&&Aa.menuSmallTablet)},n.createElement(Ea,{role:"row",onClick:s,isTabletSmall:c,icon:Ra,title:(0,Ve.appendEllipsis)(l.t(null,void 0,a(26869))),"aria-haspopup":"dialog"}),d&&n.createElement(n.Fragment,null,n.createElement(C.PopupMenuSeparator,null),n.createElement(pe,null)),!d&&(c?n.createElement(Fa,{defaults:v,customs:p,recents:i,render:_}):n.createElement(Ha,{defaults:v,customs:p,recents:i,render:_,state:g})),Ia)}function Fa(e){const{defaults:t,customs:i,recents:s,render:o}=e;return n.createElement(n.Fragment,null,i.length>0&&n.createElement(n.Fragment,null,n.createElement(C.PopupMenuSeparator,null),n.createElement(ge.ToolWidgetMenuSummary,{className:Aa.menuItemHeaderTabletSmall},l.t(null,void 0,a(83300))),i.map((e=>o(e)))),t.length>0&&n.createElement(n.Fragment,null,n.createElement(C.PopupMenuSeparator,null),n.createElement(ge.ToolWidgetMenuSummary,{className:Aa.menuItemHeaderTabletSmall},l.t(null,void 0,a(46838))),t.map((e=>o(e)))))}function Ha(e){const{defaults:t,customs:i,recents:s,render:o,state:r}=e;return n.createElement(n.Fragment,null,i.length>0&&n.createElement(n.Fragment,null,n.createElement(C.PopupMenuSeparator,null),n.createElement(ge.ToolWidgetMenuSummary,{className:Aa.menuItemHeader},l.t(null,void 0,a(83300))),i.map((e=>o(e)))),i.length>0&&t.length>0&&r&&n.createElement(n.Fragment,null,n.createElement(C.PopupMenuSeparator,null),n.createElement(Le,{role:"row",summary:l.t(null,void 0,a(46838)),open:!r.get().defaultsCollapsed,onStateChange:e=>r.set({defaultsCollapsed:!e})},t.map((e=>o(e))))),0===i.length&&t.length>0&&n.createElement(n.Fragment,null,n.createElement(C.PopupMenuSeparator,null),n.createElement(ge.ToolWidgetMenuSummary,{className:Aa.menuItemHeader},l.t(null,void 0,a(46838))),t.map((e=>o(e)))))}var Oa=a(64147),Da=a(39076);class Ua{constructor(e,t){this._isFavoriteEnabled=i.enabled("items_favoriting"),this.handleFavorTemplate=e=>{if(!this._isFavoriteEnabled)return;const{id:t,is_default:a,favorite_date:i}=e;null!==i?this._removeFavoriteTemplate(t,a):this._addFavoriteTemplate(t,a)},this.handleDropdownOpen=()=>{this._setState({isLoading:!0}),this._studyTemplates.invalidate(),this._studyTemplates.refreshStudyTemplateList((()=>this._setState({isLoading:!1})))},this.handleApplyTemplate=e=>{this._studyTemplates.applyTemplate(e.name),this._recentStudyTemplatesService&&this._recentStudyTemplatesService.add(e.id)},this.handleRemoveTemplate=(e,t,a)=>{this._studyTemplates.deleteStudyTemplate(e.name,t,a)},this.handleSaveTemplate=e=>{this._studyTemplates.showSaveAsDialog(e)},this._studyTemplates=e,this._recentStudyTemplatesService=t;const a=this._recentStudyTemplatesService?.get()||[],n=this._studyTemplates.list();this._state=new Oa.WatchedValue({isLoading:!1,studyTemplatesList:n,recents:a}),this._studyTemplates.getOnChange().subscribe(this,this._handleTemplatesChange),this._recentStudyTemplatesService?.getOnChange().subscribe(this,this._handleRecentsChange),this._studyTemplates.refreshStudyTemplateList()}destroy(){
this._studyTemplates.getOnChange().unsubscribe(this,this._handleTemplatesChange),this._recentStudyTemplatesService?.getOnChange().unsubscribe(this,this._handleRecentsChange)}state(){return this._state.readonly()}getSortedFavoritesStudyTemplates(e){return e.filter((e=>null!==e.favorite_date)).sort(((e,t)=>null!==e.favorite_date&&null!==t.favorite_date&&e.favorite_date>t.favorite_date?1:-1))}getValidRecents(e){const t=e.reduce(((e,t)=>(e[t.id]=t,e)),{});return this._state.value().recents.map((e=>t[e])).filter((e=>void 0!==e))}_setState(e){this._state.setValue({...this._state.value(),...e})}_handleTemplatesChange(){this._setState({studyTemplatesList:this._studyTemplates.list()})}_handleRecentsChange(){this._setState({recents:this._recentStudyTemplatesService?.get()||[]})}_removeFavoriteTemplate(e,t){const a=t?Da.backend.favorStandardStudyTemplate:Da.backend.favorStudyTemplate;this._replaceOldFavoriteStudyTemplate(e,null,t),a(e,!1)}_addFavoriteTemplate(e,t){const a=this.getSortedFavoritesStudyTemplates(this._state.value().studyTemplatesList),i=a.length?a[a.length-1].favorite_date:0,n=window.is_authenticated?Date.now():i+1,s=t?Da.backend.favorStandardStudyTemplate:Da.backend.favorStudyTemplate;this._replaceOldFavoriteStudyTemplate(e,n,t),s(e,!0)}_replaceOldFavoriteStudyTemplate(e,t,a){const i=this._state.value().studyTemplatesList.map((i=>i.id===e&&i.is_default===a?{...i,favorite_date:t}:i));this._setState({studyTemplatesList:i})}}var Wa=a(63472),Pa=a(66114);function La(e){const{className:t,children:a,item:i,onApply:s,isActive:o,href:l,target:r}=e,h=i.name?.trim()??" ",[c,d]=(0,P.useRovingTabindexElement)(null),m=l?"a":s?"button":"div",v=i.tooltip??h,p=(0,Wa.getLeadingEmojiHtml)(v);return n.createElement(m,{ref:c,type:s&&!l?"button":void 0,className:u(t,Pa.item,Pa.accessible,"apply-common-tooltip","common-tooltip-html"),onClick:function(e){if(e.stopPropagation(),s&&l){if((0,T.modifiersFromEvent)(e)!==T.Modifiers.None||1===e.button)return;e.preventDefault()}s?.(i)},onKeyDown:"a"===m?function(e){32===(0,T.hashFromEvent)(e)&&(e.preventDefault(),c.current?.click())}:void 0,"data-tooltip":p,"aria-label":v,tabIndex:d,href:"a"===m?l:void 0,target:"a"===m?r:void 0},n.createElement("div",{className:u(Pa.round,o&&Pa.active)},a||n.createElement(ht.LeadingEmojiText,{text:h,firstSegmentOnly:!0})))}var Va=a(21233),Ba=a(88506);const za=(0,f.registryContextType)();class Za extends n.PureComponent{constructor(e,t){super(e,t),this._updateState=e=>{this.setState({...e,isActive:this.state.isActive})},this._handleApplyTemplate=e=>{this._handleClose(),this._model.handleApplyTemplate(e)},this._handleRemoveTemplate=(e,t,a)=>{this._handleClose(),this._model.handleRemoveTemplate(e,t,a)},this._handleClose=()=>{this._handleToggleDropdown(!1)},this._handleToggleDropdown=e=>{const{isActive:t}=this.state,a="boolean"==typeof e?e:!t;this.setState({isActive:a})},this._handleOpenTemplatesDialog=()=>{isNewTemplatesViewEnabled&&showIndicatorTemplatesDialog(this._model)},(0,f.validateRegistry)(t,{studyTemplates:s.any.isRequired,
templatesMenuViewStateService:s.any,recentStudyTemplatesService:s.any});const{recentStudyTemplatesService:a,studyTemplates:i}=t;this._model=new Ua(i,a),this.state={...this._model.state().value(),isActive:!1}}componentDidMount(){this._model.state().subscribe(this._updateState)}componentWillUnmount(){this._model.state().unsubscribe(this._updateState),this._model.destroy()}render(){const{studyTemplatesList:e}=this.state,{isShownQuicks:t,className:a,displayMode:i,id:s}=this.props,o=this._model.getSortedFavoritesStudyTemplates(e),l=this._model.getValidRecents(e);return n.createElement(xa.Provider,{value:this.context.templatesMenuViewStateService||null},n.createElement(Ka,{id:s,className:a,mode:i,templates:e,favorites:o,recents:l,onMenuOpen:this._model.handleDropdownOpen,onTemplateFavorite:t?this._model.handleFavorTemplate:void 0,onTemplateSelect:this._handleApplyTemplate,onTemplateRemove:this._handleRemoveTemplate,onTemplateSave:this._model.handleSaveTemplate,onOpenTemplatesDialog:this._handleOpenTemplatesDialog}))}}function Ka(e){const{id:t,className:i,mode:s,templates:o,favorites:r,recents:h,isMenuOpen:c,onTemplateSelect:u,onTemplateSave:v,onTemplateFavorite:g,onTemplateRemove:b,onOpenTemplatesDialog:S}=e,C=(0,n.useRef)(null),w=(0,n.useRef)(null),f=m()(i,Ba.wrap,{[Ba.full]:"full"===s,[Ba.medium]:"medium"===s}),k="small"!==s&&g&&r.length>0;return n.createElement(p,{id:t,className:f},n.createElement(_.MatchMedia,{rule:y.DialogBreakpoints.TabletSmall},(t=>n.createElement(d.ToolbarMenuButton,{ref:C,menuReference:w,onOpen:e.onMenuOpen,isDrawer:t,drawerPosition:"Bottom",arrow:!1,content:n.createElement(z,{tag:"div",className:m()(k&&Ba.buttonWithFavorites),displayMode:s,isOpened:c,icon:Va,forceInteractive:!0,collapseWhen:["full","medium","small"]}),onClick:M,tooltip:l.t(null,void 0,a(76443)),menuDataName:"indicator-templates-menu"},n.createElement(Na,{onTemplateSave:v,onTemplateSelect:u,onTemplateRemove:E,onTemplateFavorite:g,templates:o,recents:h,isTabletSmall:t,onOpenTemplatesDialog:S})))),k&&n.createElement(qa,{favorites:r,onTemplateSelect:function(e){u(e),M()}}));function E(e,t){if(t&&(0,q.isKeyboardClick)(t)&&w.current){const t=(0,le.queryMenuElements)(w.current),a=t.findIndex((t=>null!==t.closest(`[data-id="${e.id}"]`)));b(e,null,(()=>{if(-1!==a&&w.current){const e=t[a+1]??t[a-1];e?e.focus():(0,le.focusFirstMenuItem)(w.current),C.current?.update()}}))}else b(e)}function M(){0}}function qa(e){return n.createElement(n.Fragment,null,e.favorites.map(((t,a,i)=>n.createElement(La,{key:t.name,item:t,onApply:e.onTemplateSelect,className:m()({[Ba.first]:0===a,[Ba.last]:a===i.length-1})}))))}Za.contextType=za;a(21251);var ja=a(77665),Qa=a(96052),Xa=a(28282);const Ga={undoHotKey:(0,$.hotKeySerialize)({keys:[(0,Y.humanReadableModifiers)(Y.Modifiers.Mod,!1),"Z"],text:"{0} + {1}"}),redoHotKey:(0,$.hotKeySerialize)({keys:[(0,Y.humanReadableModifiers)(Y.Modifiers.Mod,!1),"Y"],text:"{0} + {1}"})},$a=(0,rt.mergeThemes)(g.DEFAULT_TOOLBAR_BUTTON_THEME,Xa),Ya=(0,f.registryContextType)();class Ja extends n.PureComponent{constructor(e,t){
super(e,t),this._batched=null,this._handleClickUndo=()=>{(0,K.trackEvent)("GUI","Undo");const{chartWidgetCollection:e}=this.context;e.undoHistory.undo()},this._handleClickRedo=()=>{(0,K.trackEvent)("GUI","Redo");const{chartWidgetCollection:e}=this.context;e.undoHistory.redo()},(0,f.validateRegistry)(t,{chartWidgetCollection:s.any.isRequired}),this.state=this._getStateFromUndoHistory()}componentDidMount(){const{chartWidgetCollection:e}=this.context;e.undoHistory.redoStack().onChange().subscribe(this,this._onChangeStack),e.undoHistory.undoStack().onChange().subscribe(this,this._onChangeStack)}componentWillUnmount(){const{chartWidgetCollection:e}=this.context;e.undoHistory.redoStack().onChange().unsubscribe(this,this._onChangeStack),e.undoHistory.undoStack().onChange().unsubscribe(this,this._onChangeStack),this._batched=null}render(){const{id:e}=this.props,{isEnabledRedo:t,isEnabledUndo:i,redoStack:s,undoStack:o}=this.state;return n.createElement(p,{id:e},n.createElement(g.ToolbarButton,{icon:ja,isDisabled:!i,onClick:this._handleClickUndo,theme:$a,tooltip:i?l.t(null,{replace:{hint:o}},a(66020)):void 0,"data-tooltip-hotkey":i?Ga.undoHotKey:void 0}),n.createElement(g.ToolbarButton,{icon:Qa,isDisabled:!t,onClick:this._handleClickRedo,theme:$a,tooltip:t?l.t(null,{replace:{hint:s}},a(11304)):void 0,"data-tooltip-hotkey":t?Ga.redoHotKey:void 0}))}_onChangeStack(){null===this._batched&&(this._batched=Promise.resolve().then((()=>{if(null===this._batched)return;this._batched=null;const e=this._getStateFromUndoHistory();this.setState(e)})))}_getStateFromUndoHistory(){const{chartWidgetCollection:e}=this.context,t=e.undoHistory.undoStack(),a=e.undoHistory.redoStack(),i=a.head(),n=t.head();return{isEnabledRedo:!a.isEmpty(),isEnabledUndo:!t.isEmpty(),redoStack:i?i.text().translatedText():"",undoStack:n?n.text().translatedText():""}}}Ja.contextType=Ya;const ei=e=>{if((e=>"http://www.w3.org/1999/xhtml"===e?.namespaceURI)(e)&&"true"!==e.dataset.internalAllowKeyboardNavigation){e.tabIndex=-1,e.ariaDisabled="true";for(let t=0;t<e.children.length;t++)ei(e.children.item(t))}};class ti extends n.PureComponent{constructor(){super(...arguments),this._wrapperElement=null,this._resizeObserver=null,this._mutationObserver=null,this._update=()=>{this.forceUpdate()},this._setRef=e=>{this._wrapperElement=e},this._handleMeasure=([e])=>{this.props.width.setValue(e.contentRect.width)},this._handleMutation=([e])=>{"childList"===e.type&&ei(this.props.element)}}componentDidMount(){const{element:e,isFake:t,width:a}=this.props;!t&&this._wrapperElement?(this._resizeObserver=new ResizeObserver(this._handleMeasure),this._mutationObserver=new MutationObserver(this._handleMutation),this._wrapperElement.appendChild(e),this._resizeObserver.observe(this._wrapperElement),this._mutationObserver.observe(e,{subtree:!0,childList:!0})):a.subscribe(this._update)}componentWillUnmount(){const{width:e,isFake:t}=this.props;t&&e.unsubscribe(this._update),this._resizeObserver&&this._wrapperElement&&this._resizeObserver.unobserve(this._wrapperElement),
this._mutationObserver&&(this._mutationObserver.disconnect(),this._mutationObserver=null)}render(){const{isFake:e=!1,width:t}=this.props;return n.createElement(p,{ref:this._setRef,style:e?{width:t.value()}:void 0,"data-is-custom-header-element":!0})}}function ai(e){const{displayMode:t,params:a}=e;return n.createElement(d.ToolbarMenuButton,{content:n.createElement(z,{collapseWhen:void 0!==a.icon?void 0:[],displayMode:t,icon:a.icon,text:a.title,"data-name":"dropdown","data-is-custom-header-element":!0}),drawerPosition:"Bottom",drawerBreakpoint:y.DialogBreakpoints.TabletSmall,arrow:!1,tooltip:a.tooltip},a.items.map(((e,t)=>n.createElement(oe.PopupMenuItem,{key:t,label:e.title,icon:e.icon,onClick:()=>e.onSelect(),"data-name":"dropdown-item"}))))}var ii=a(935);function ni(e){const{className:t,title:a,...i}=e;return n.createElement(Z,{...i,className:u(t,ii.customTradingViewStyleButton,ii.withoutIcon),collapseWhen:[],"data-name":"custom-tradingview-styled-button",tooltip:a})}var si=a(60448);const oi=(0,$.hotKeySerialize)({keys:[(0,Y.humanReadableModifiers)(Y.Modifiers.Mod,!1),"K"],text:"{0} + {1}"}),li=(0,f.registryContextType)();class ri extends n.PureComponent{constructor(e,t){super(e),this._dialog=null,this._updateState=e=>{this.setState({isOpened:e})},this._handleClick=e=>{const{openGlobalSearch:t}=this.context;t({shouldReturnFocus:(0,q.isKeyboardClick)(e)}).then((e=>{this._dialog?.visible().unsubscribe(this._updateState),this._dialog=e,e.visible().subscribe(this._updateState)}))},(0,f.validateRegistry)(t,{openGlobalSearch:s.any.isRequired}),this.state={isOpened:!1}}componentWillUnmount(){this._dialog?.visible().unsubscribe(this._updateState)}render(){return n.createElement(G.ToolbarIconButton,{...this.props,icon:si,isOpened:this.state.isOpened,onClick:this._handleClick,"data-tooltip-hotkey":oi,tooltip:l.t(null,void 0,a(18243))})}}ri.contextType=li;var hi=a(27514),ci=a(27906);function di(){return{Bars:i.enabled("header_chart_type")?W:void 0,Compare:i.enabled("header_compare")?X:void 0,Custom:ti,CustomTradingViewStyledButton:ni,Fullscreen:(0,ci.shouldShowFullscreen)()?ne:void 0,Indicators:i.enabled("header_indicators")?we:void 0,Intervals:i.enabled("header_resolutions")?Xe:void 0,OpenPopup:tt,Properties:i.enabled("header_settings")&&i.enabled("show_chart_property_page")?nt:void 0,SaveLoad:i.enabled("header_saveload")?Rt:void 0,Screenshot:i.enabled("header_screenshot")?ea:void 0,SymbolSearch:i.enabled("header_symbol_search")?da:void 0,Templates:i.enabled("study_templates")?Za:void 0,Dropdown:ai,UndoRedo:i.enabled("header_undo_redo")?Ja:void 0,Layout:undefined,QuickSearch:(0,hi.shouldShowQuickSearchOnLib)()?ri:void 0}}},27906:(e,t,a)=>{"use strict";a.d(t,{shouldShowFullscreen:()=>n});var i=a(56570);function n(){return i.enabled("header_fullscreen_button")}},23076:(e,t,a)=>{"use strict";a.r(t),a.d(t,{SERIES_ICONS:()=>g});var i=a(4475),n=a(94670),s=a(32162),o=a(39956),l=a(14083),r=a(45504),h=a(52867),c=a(41473),d=a(31246),u=a(15726),m=a(24464),v=a(71705),p=a(9450);const g={3:n,16:s,0:o,1:l,8:r,9:h,2:c,14:d,15:u,10:m,12:v,
13:p};g[21]=i},7986:(e,t,a)=>{"use strict";function i(e){return"https://www.tradingview.com/x/"+e+"/"}a.d(t,{convertImageNameToUrl:()=>i})},7372:(e,t,a)=>{"use strict";function i(e,t){const a=document.createElement("a");a.style.display="none",a.href=t,a.download=e,a.click()}a.d(t,{downloadFile:()=>i})},29142:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M3.5 8a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM8 2a6 6 0 1 0 3.65 10.76l3.58 3.58 1.06-1.06-3.57-3.57A6 6 0 0 0 8 2Z"/></svg>'},97268:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M8.5 6A2.5 2.5 0 0 0 6 8.5V11h1V8.5C7 7.67 7.67 7 8.5 7H11V6H8.5zM6 17v2.5A2.5 2.5 0 0 0 8.5 22H11v-1H8.5A1.5 1.5 0 0 1 7 19.5V17H6zM19.5 7H17V6h2.5A2.5 2.5 0 0 1 22 8.5V11h-1V8.5c0-.83-.67-1.5-1.5-1.5zM22 19.5V17h-1v2.5c0 .83-.67 1.5-1.5 1.5H17v1h2.5a2.5 2.5 0 0 0 2.5-2.5z"/></svg>'},36992:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M17 6v2.5a2.5 2.5 0 0 0 2.5 2.5H22v-1h-2.5A1.5 1.5 0 0 1 18 8.5V6h-1zm2.5 11a2.5 2.5 0 0 0-2.5 2.5V22h1v-2.5c0-.83.67-1.5 1.5-1.5H22v-1h-2.5zm-11 1H6v-1h2.5a2.5 2.5 0 0 1 2.5 2.5V22h-1v-2.5c0-.83-.67-1.5-1.5-1.5zM11 8.5V6h-1v2.5c0 .83-.67 1.5-1.5 1.5H6v1h2.5A2.5 2.5 0 0 0 11 8.5z"/></svg>'},13090:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M5 7.5C5 6.67 5.67 6 6.5 6h4.2l2 2h8.8c.83 0 1.5.67 1.5 1.5v10c0 .83-.67 1.5-1.5 1.5h-15A1.5 1.5 0 0 1 5 19.5v-12ZM6.5 7a.5.5 0 0 0-.5.5V11h16V9.5a.5.5 0 0 0-.5-.5h-9.2l-2-2H6.5ZM22 12H6v7.5c0 .28.22.5.5.5h15a.5.5 0 0 0 .5-.5V12Z"/></svg>'},6198:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M13.9 14.1V22h1.2v-7.9H23v-1.2h-7.9V5h-1.2v7.9H6v1.2h7.9Z"/></svg>'},99280:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M8.5 6A2.5 2.5 0 0 0 6 8.5v11A2.5 2.5 0 0 0 8.5 22h11a2.5 2.5 0 0 0 2.5-2.5v-3h-1v3c0 .83-.67 1.5-1.5 1.5h-11A1.5 1.5 0 0 1 7 19.5v-11C7 7.67 7.67 7 8.5 7h3V6h-3zm7 1h4.8l-7.49 7.48.71.7L21 7.72v4.79h1V6h-6.5v1z"/></svg>'},60448:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17 4v4h2a1 1 0 0 1 .83 1.55l-4 6A1 1 0 0 1 14 15v-4h-2a1 1 0 0 1-.83-1.55l4-6A1 1 0 0 1 17 4m-2 11 4-6h-3V4l-4 6h3z"/><path d="M5 13.5a7.5 7.5 0 0 1 6-7.35v1.02A6.5 6.5 0 1 0 18.98 13h1l.02.5a7.47 7.47 0 0 1-1.85 4.94L23 23.29l-.71.7-4.85-4.84A7.5 7.5 0 0 1 5 13.5"/></svg>'},21233:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M8 7h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1zM6 8c0-1.1.9-2 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H8a2 2 0 0 1-2-2V8zm11-1h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1zm-2 1c0-1.1.9-2 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-3a2 2 0 0 1-2-2V8zm-4 8H8a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1zm-3-1a2 2 0 0 0-2 2v3c0 1.1.9 2 2 2h3a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H8zm9 1h3a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-3a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1zm-2 1c0-1.1.9-2 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-3a2 2 0 0 1-2-2v-3z"/></svg>'},94670:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="m25.35 5.35-9.5 9.5-.35.36-.35-.36-4.65-4.64-8.15 8.14-.7-.7 8.5-8.5.35-.36.35.36 4.65 4.64 9.15-9.14.7.7ZM2 21h1v1H2v-1Zm2-1H3v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1V9h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1H8v1H7v1H6v1H5v1H4v1Zm1 0v1H4v-1h1Zm1 0H5v-1h1v1Zm1 0v1H6v-1h1Zm0-1H6v-1h1v1Zm1 0H7v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v1h1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1H8v1H7v1h1v1Zm1 0v1H8v-1h1Zm0-1H8v-1h1v1Zm1 0H9v1h1v1h1v-1h1v1h1v-1h1v1h1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1H9v1h1v1Zm1 0v1h-1v-1h1Zm0-1v-1h-1v1h1Zm0 0v1h1v1h1v-1h-1v-1h-1Zm6 2v-1h1v1h-1Zm2 0v1h-1v-1h1Zm0-1h-1v-1h1v1Zm1 0h-1v1h1v1h1v-1h1v1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h1v1Zm1 0h-1v1h1v-1Zm0-1h1v1h-1v-1Zm0-1h1v-1h-1v1Zm0 0v1h-1v-1h1Zm-4 3v1h-1v-1h1Z"/></svg>'},39956:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M19 6h-1v7h-3v1h3v8h1v-3h3v-1h-3V6ZM11 7h-1v13H7v1h3v2h1V10h3V9h-3V7Z"/></svg>'},24464:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m10.49 7.55-.42.7-2.1 3.5.86.5 1.68-2.8 1.8 2.82.84-.54-2.23-3.5-.43-.68Zm12.32 4.72-.84-.54 2.61-4 .84.54-2.61 4Zm-5.3 6.3 1.2-1.84.84.54-1.63 2.5-.43.65-.41-.65-1.6-2.5.85-.54 1.17 1.85ZM4.96 16.75l.86.52-2.4 4-.86-.52 2.4-4ZM3 14v1h1v-1H3Zm2 0h1v1H5v-1Zm2 0v1h1v-1H7Zm2 0h1v1H9v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Zm2 0v1h1v-1h-1Zm2 0h1v1h-1v-1Z"/></svg>'},14083:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17 11v6h3v-6h-3zm-.5-1h4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5z"/><path d="M18 7h1v3.5h-1zm0 10.5h1V21h-1z"/><path d="M9 8v12h3V8H9zm-.5-1h4a.5.5 0 0 1 .5.5v13a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-13a.5.5 0 0 1 .5-.5z"/><path d="M10 4h1v3.5h-1zm0 16.5h1V24h-1z"/></svg>'},53707:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><g fill="none"><path stroke="currentColor" d="M11 20.5H7.5a5 5 0 1 1 .42-9.98 7.5 7.5 0 0 1 14.57 2.1 4 4 0 0 1-1 7.877H18"/><path stroke="currentColor" d="M14.5 24V12.5M11 16l3.5-3.5L18 16"/></g></svg>'},9450:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M12 7v14h5V7h-5Zm4 1h-3v12h3V8ZM19 15v6h5v-6h-5Zm4 1h-3v4h3v-4ZM5 12h5v9H5v-9Zm1 1h3v7H6v-7Z"/></svg>'},1393:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M13.5 6a8.5 8.5 0 1 0 0 17 8.5 8.5 0 0 0 0-17zM4 14.5a9.5 9.5 0 1 1 19 0 9.5 9.5 0 0 1-19 0z"/><path fill="currentColor" d="M9 14h4v-4h1v4h4v1h-4v4h-1v-4H9v-1z"/></svg>'},45504:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M9 8v12h3V8H9zm-1-.502C8 7.223 8.215 7 8.498 7h4.004c.275 0 .498.22.498.498v13.004a.493.493 0 0 1-.498.498H8.498A.496.496 0 0 1 8 20.502V7.498z"/><path d="M10 4h1v3.5h-1z"/><path d="M17 6v6h3V6h-3zm-1-.5c0-.276.215-.5.498-.5h4.004c.275 0 .498.23.498.5v7c0 .276-.215.5-.498.5h-4.004a.503.503 0 0 1-.498-.5v-7z"/><path d="M18 2h1v3.5h-1z"/></svg>'},71705:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M7.5 7H7v14h5V7H7.5zM8 20V8h3v12H8zm7.5-11H15v10h5V9h-4.5zm.5 9v-8h3v8h-3z"/></svg>'},32162:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" d="M22 3h1v1h-1V3Zm0 2V4h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1V9h-1V8h-1V7h-1V6h-1V5h-1v1H9v1H8v1H7v1H6v1H5v1H4v1h1v1H4v1h1v-1h1v-1h1v-1h1v-1h1V9h1V8h1v1h1v1h1v1h1v1h1v1h1v-1h1v-1h1v-1h1V9h1V8h1V7h1V6h1V5h-1Zm-1 1V5h1v1h-1Zm-1 1V6h1v1h-1Zm-1 1V7h1v1h-1Zm-1 1V8h1v1h-1Zm-1 1V9h1v1h-1Zm-1 1v-1h1v1h-1Zm-1 0v-1h-1V9h-1V8h-1V7h-1V6h-1v1H9v1H8v1H7v1H6v1H5v1h1v-1h1v-1h1V9h1V8h1V7h1v1h1v1h1v1h1v1h1Zm0 0h1v1h-1v-1Zm.84 6.37 7.5-7-.68-.74-7.15 6.67-4.66-4.65-.33-.34-.36.32-5.5 5 .68.74 5.14-4.68 4.67 **********.35-.33ZM6 23H5v1h1v-1Zm0-1H5v-1h1v1Zm1 0v1H6v-1h1Zm0-1H6v-1h1v1Zm1 0v1H7v-1h1Zm0-1H7v-1h1v1Zm1 0v1H8v-1h1Zm0-1H8v-1h1v1Zm1 0v1H9v-1h1Zm0-1H9v-1h1v1Zm1 0h-1v1h1v1h1v1h1v1h1v1h1v1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h-1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1v-1h-1v-1h-1v-1h-1v1h1v1Zm0 0h1v1h-1v-1Zm2 2v1h1v1h1v1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h1v-1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v1h-1v-1h-1v-1h-1Zm0 0v-1h-1v1h1Z"/></svg>'},4475:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M19 6h-1v7v9h1v-3h3v-1h-3V6ZM11 7h-1v16h1V10h3V9h-3V7Z"/></svg>'},52867:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M17 11v6h3v-6h-3zm-.5-1h4a.5.5 0 0 1 .5.5v7a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-7a.5.5 0 0 1 .5-.5z"/><path d="M18 7h1v3.5h-1zm0 10.5h1V21h-1z"/><path d="M9 8v11h3V8H9zm-.5-1h4a.5.5 0 0 1 .5.5v12a.5.5 0 0 1-.5.5h-4a.5.5 0 0 1-.5-.5v-12a.5.5 0 0 1 .5-.5z"/><path d="M10 4h1v5h-1zm0 14h1v5h-1zM8.5 9H10v1H8.5zM11 9h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11zm-1 1h1v1h-1zm-1.5 1H10v1H8.5zm2.5 0h1.5v1H11z"/></svg>'},39681:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M20 17l-5 5M15 17l5 5M9 11.5h7M17.5 8a2.5 2.5 0 0 0-5 0v11a2.5 2.5 0 0 1-5 0"/></svg>'},31246:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" d="m18.43 15.91 6.96-8.6-.78-.62-6.96 8.6a2.49 2.49 0 0 0-2.63.2l-2.21-2.02A2.5 2.5 0 0 0 10.5 10a2.5 2.5 0 1 0 1.73 4.3l2.12 1.92a2.5 2.5 0 1 0 4.08-.31ZM10.5 14a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm7.5 3.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"/><path d="M8.37 13.8c.17.3.4.54.68.74l-5.67 6.78-.76-.64 5.75-6.88Z"/></svg>'},41473:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="m25.39 7.31-8.83 10.92-6.02-5.47-7.16 8.56-.76-.64 7.82-9.36 6 5.45L24.61 6.7l.78.62Z"/></svg>'},82436:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 21" width="21" height="21"><g fill="none" stroke="currentColor"><path d="M18.5 11v5.5a2 2 0 0 1-2 2h-13a2 2 0 0 1-2-2v-13a2 2 0 0 1 2-2H9"/><path stroke-linecap="square" d="M18 2l-8.5 8.5m4-9h5v5"/></g></svg>'},96052:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M18.293 13l-2.647 2.646.707.708 3.854-3.854-3.854-3.854-.707.708L18.293 12H12.5A5.5 5.5 0 0 0 7 17.5V19h1v-1.5a4.5 4.5 0 0 1 4.5-4.5h5.793z"/></svg>'},72644:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.118 6a.5.5 0 0 0-.447.276L9.809 8H5.5A1.5 1.5 0 0 0 4 9.5v10A1.5 1.5 0 0 0 5.5 21h16a1.5 1.5 0 0 0 1.5-1.5v-10A1.5 1.5 0 0 0 21.5 8h-4.309l-.862-1.724A.5.5 0 0 0 15.882 6h-4.764zm-1.342-.17A1.5 1.5 0 0 1 11.118 5h4.764a1.5 1.5 0 0 1 1.342.83L17.809 7H21.5A2.5 2.5 0 0 1 24 9.5v10a2.5 2.5 0 0 1-2.5 2.5h-16A2.5 2.5 0 0 1 3 19.5v-10A2.5 2.5 0 0 1 5.5 7h3.691l.585-1.17z"/><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5 18a3.5 3.5 0 1 0 0-7 3.5 3.5 0 0 0 0 7zm0 1a4.5 4.5 0 1 0 0-9 4.5 4.5 0 0 0 0 9z"/></svg>'},15726:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="currentColor"><path d="M19 5h5v1h-4v13h-6v-7h-4v12H5v-1h4V11h6v7h4V5Z"/></svg>'},77665:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M8.707 13l2.647 2.646-.707.708L6.792 12.5l3.853-3.854.708.708L8.707 12H14.5a5.5 5.5 0 0 1 5.5 5.5V19h-1v-1.5a4.5 4.5 0 0 0-4.5-4.5H8.707z"/></svg>'},36296:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8 9.5H6.5a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1V20m-8-1.5h11a1 1 0 0 0 1-1v-11a1 1 0 0 0-1-1h-11a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1z"/></svg>'},23595:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M6.5 16v4.5a1 1 0 001 1h14a1 1 0 001-1V16M14.5 5V17m-4-3.5l4 4l4-4"/></svg>'},29414:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M19 15l2.5-2.5c1-1 1.5-3.5-.5-5.5s-4.5-1.5-5.5-.5L13 9M10 12l-2.5 2.5c-1 1-1.5 3.5.5 5.5s4.5 1.5 5.5.5L16 18M17 11l-5 5"/></svg>'},67487:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M19.75 5h3.07l-6.7 7.62L24 23h-6.17l-4.84-6.3L7.46 23H4.4l7.17-8.16L4 5h6.33l4.37 5.75L19.75 5Zm-1.24 16h1.7L9.54 7H7.7l10.8 14Z"/></svg>'}}]);