(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[2751],{66783:e=>{"use strict";var t=Object.prototype.hasOwnProperty;function r(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}e.exports=function(e,n){if(r(e,n))return!0;if("object"!=typeof e||null===e||"object"!=typeof n||null===n)return!1;var o=Object.keys(e),a=Object.keys(n);if(o.length!==a.length)return!1;for(var s=0;s<o.length;s++)if(!t.call(n,o[s])||!r(e[o[s]],n[o[s]]))return!1;return!0}},88317:e=>{e.exports={pills:"pills-PVWoXu5j",primary:"primary-PVWoXu5j",gray:"gray-PVWoXu5j",selected:"selected-PVWoXu5j",grouped:"grouped-PVWoXu5j",active:"active-PVWoXu5j",disableActiveOnTouch:"disableActiveOnTouch-PVWoXu5j",disableActiveStateStyles:"disableActiveStateStyles-PVWoXu5j",withGrouped:"withGrouped-PVWoXu5j","quiet-primary":"quiet-primary-PVWoXu5j",green:"green-PVWoXu5j",red:"red-PVWoXu5j",blue:"blue-PVWoXu5j",secondary:"secondary-PVWoXu5j",ghost:"ghost-PVWoXu5j"}},1538:e=>{e.exports={lightButton:"lightButton-bYDQcOkp",link:"link-bYDQcOkp",ltr:"ltr-bYDQcOkp",rtl:"rtl-bYDQcOkp","typography-regular16px":"typography-regular16px-bYDQcOkp","typography-medium16px":"typography-medium16px-bYDQcOkp","typography-regular14px":"typography-regular14px-bYDQcOkp","typography-semibold14px":"typography-semibold14px-bYDQcOkp","typography-semibold16px":"typography-semibold16px-bYDQcOkp",content:"content-bYDQcOkp",visuallyHidden:"visuallyHidden-bYDQcOkp",nowrap:"nowrap-bYDQcOkp",ellipsisContainer:"ellipsisContainer-bYDQcOkp",textWrapContainer:"textWrapContainer-bYDQcOkp",textWrapWithEllipsis:"textWrapWithEllipsis-bYDQcOkp",slot:"slot-bYDQcOkp",caret:"caret-bYDQcOkp",activeCaret:"activeCaret-bYDQcOkp",xsmall:"xsmall-bYDQcOkp",withStartSlot:"withStartSlot-bYDQcOkp",withEndSlot:"withEndSlot-bYDQcOkp",noContent:"noContent-bYDQcOkp",wrap:"wrap-bYDQcOkp",small:"small-bYDQcOkp",medium:"medium-bYDQcOkp"}},78217:e=>{e.exports={pair:"pair-ocURKVwI",xxceptionallysmalldonotusebrv1023:"xxceptionallysmalldonotusebrv1023-ocURKVwI",xxxxsmall:"xxxxsmall-ocURKVwI",xxxsmall:"xxxsmall-ocURKVwI",xxsmall:"xxsmall-ocURKVwI",xsmall:"xsmall-ocURKVwI",small:"small-ocURKVwI",medium:"medium-ocURKVwI",large:"large-ocURKVwI",xlarge:"xlarge-ocURKVwI",xxlarge:"xxlarge-ocURKVwI",xxxlarge:"xxxlarge-ocURKVwI",logo:"logo-ocURKVwI",skeleton:"skeleton-ocURKVwI",empty:"empty-ocURKVwI"}},34869:e=>{e.exports={hidden:"hidden-DgcIT6Uz",fadeInWrapper:"fadeInWrapper-DgcIT6Uz"}},85862:e=>{e.exports={disableSelfPositioning:"disableSelfPositioning-dYiqkKAE"}},79566:e=>{e.exports={container:"container-M1mz4quA",pairContainer:"pairContainer-M1mz4quA",logo:"logo-M1mz4quA",hidden:"hidden-M1mz4quA"}},92335:e=>{e.exports={container:"container-qm7Rg5MB",mobile:"mobile-qm7Rg5MB",inputContainer:"inputContainer-qm7Rg5MB",withCancel:"withCancel-qm7Rg5MB",input:"input-qm7Rg5MB",icon:"icon-qm7Rg5MB",cancel:"cancel-qm7Rg5MB"}},10070:e=>{e.exports={actions:"actions-rarsm4ka",actionButton:"actionButton-rarsm4ka"}},94869:e=>{e.exports={logo:"logo-d0vVmGvT"}},92069:e=>{e.exports={
"tablet-small-breakpoint":"(max-width: 440px)",itemRow:"itemRow-oRSs8UQo",multiLine:"multiLine-oRSs8UQo",cell:"cell-oRSs8UQo",itemInfoCell:"itemInfoCell-oRSs8UQo",description:"description-oRSs8UQo",symbolDescription:"symbolDescription-oRSs8UQo",flag:"flag-oRSs8UQo",exchangeDescription:"exchangeDescription-oRSs8UQo",marketType:"marketType-oRSs8UQo",exchangeName:"exchangeName-oRSs8UQo",actionHandleWrap:"actionHandleWrap-oRSs8UQo",source:"source-oRSs8UQo",hover:"hover-oRSs8UQo",selected:"selected-oRSs8UQo",active:"active-oRSs8UQo",highlighted:"highlighted-oRSs8UQo",light:"light-oRSs8UQo","highlight-animation-theme-light":"highlight-animation-theme-light-oRSs8UQo",dark:"dark-oRSs8UQo","highlight-animation-theme-dark":"highlight-animation-theme-dark-oRSs8UQo",markedFlag:"markedFlag-oRSs8UQo",offset:"offset-oRSs8UQo",descriptionCell:"descriptionCell-oRSs8UQo",addition:"addition-oRSs8UQo",exchangeCell:"exchangeCell-oRSs8UQo",fixedWidth:"fixedWidth-oRSs8UQo",expandHandle:"expandHandle-oRSs8UQo",expanded:"expanded-oRSs8UQo",symbolTitle:"symbolTitle-oRSs8UQo",invalid:"invalid-oRSs8UQo",noDescription:"noDescription-oRSs8UQo",highlightedText:"highlightedText-oRSs8UQo",icon:"icon-oRSs8UQo",narrow:"narrow-oRSs8UQo",wide:"wide-oRSs8UQo",dataMode:"dataMode-oRSs8UQo",actionsCell:"actionsCell-oRSs8UQo",action:"action-oRSs8UQo",targetAction:"targetAction-oRSs8UQo",removeAction:"removeAction-oRSs8UQo",addAction:"addAction-oRSs8UQo",markedFlagWrap:"markedFlagWrap-oRSs8UQo",markedFlagMobile:"markedFlagMobile-oRSs8UQo",logo:"logo-oRSs8UQo",isExpandable:"isExpandable-oRSs8UQo",primaryIcon:"primaryIcon-oRSs8UQo"}},6963:e=>{e.exports={icon:"icon-OJpk_CAQ"}},6109:e=>{e.exports={wrap:"wrap-IxKZEhmO",libAllSelected:"libAllSelected-IxKZEhmO",container:"container-IxKZEhmO",iconWrap:"iconWrap-IxKZEhmO",icon:"icon-IxKZEhmO",title:"title-IxKZEhmO",highlighted:"highlighted-IxKZEhmO",description:"description-IxKZEhmO",mobile:"mobile-IxKZEhmO",allSelected:"allSelected-IxKZEhmO",desktop:"desktop-IxKZEhmO",allSelectedIcon:"allSelectedIcon-IxKZEhmO",selected:"selected-IxKZEhmO",focused:"focused-IxKZEhmO",titleWithoutDesc:"titleWithoutDesc-IxKZEhmO",textBlock:"textBlock-IxKZEhmO",bordered:"bordered-IxKZEhmO"}},96137:e=>{e.exports={container:"container-dfKL9A7t",contentList:"contentList-dfKL9A7t",contentListDesktop:"contentListDesktop-dfKL9A7t",searchSourceItemsContainer:"searchSourceItemsContainer-dfKL9A7t",oneColumn:"oneColumn-dfKL9A7t",searchSourceItemsContainerDesktop:"searchSourceItemsContainerDesktop-dfKL9A7t",groupTitleDesktop:"groupTitleDesktop-dfKL9A7t",column:"column-dfKL9A7t",emptyText:"emptyText-dfKL9A7t",emptyIcon:"emptyIcon-dfKL9A7t",noResultsDesktop:"noResultsDesktop-dfKL9A7t"}},6591:e=>{e.exports={wrap:"wrap-gjrLBBL3",item:"item-gjrLBBL3",small:"small-gjrLBBL3",newStyles:"newStyles-gjrLBBL3",mobile:"mobile-gjrLBBL3",text:"text-gjrLBBL3",exchange:"exchange-gjrLBBL3",filterItem:"filterItem-gjrLBBL3",brokerWrap:"brokerWrap-gjrLBBL3"}},44458:e=>{e.exports={wrap:"wrap-dlewR1s1",watchlist:"watchlist-dlewR1s1",noFeed:"noFeed-dlewR1s1",
newStyles:"newStyles-dlewR1s1",scrollContainer:"scrollContainer-dlewR1s1",listContainer:"listContainer-dlewR1s1",multiLineItemsContainer:"multiLineItemsContainer-dlewR1s1",withSpinner:"withSpinner-dlewR1s1",spinnerContainer:"spinnerContainer-dlewR1s1",largeSpinner:"largeSpinner-dlewR1s1"}},76717:e=>{e.exports={search:"search-ZXzPWcCf",upperCase:"upperCase-ZXzPWcCf",bubblesContainer:"bubblesContainer-ZXzPWcCf",mobile:"mobile-ZXzPWcCf",bubbles:"bubbles-ZXzPWcCf",withFilters:"withFilters-ZXzPWcCf",spinnerWrap:"spinnerWrap-ZXzPWcCf",emptyText:"emptyText-ZXzPWcCf",emptyIcon:"emptyIcon-ZXzPWcCf",noResultsDesktop:"noResultsDesktop-ZXzPWcCf",brokerButtonWrap:"brokerButtonWrap-ZXzPWcCf"}},92244:e=>{e.exports={flagWrap:"flagWrap-QKnxaZOG",icon:"icon-QKnxaZOG",caret:"caret-QKnxaZOG",title:"title-QKnxaZOG",button:"button-QKnxaZOG",withFlag:"withFlag-QKnxaZOG",buttonContent:"buttonContent-QKnxaZOG"}},63748:e=>{e.exports={dialog:"dialog-u2dP3kv1",tabletDialog:"tabletDialog-u2dP3kv1",desktopDialog:"desktopDialog-u2dP3kv1",backButton:"backButton-u2dP3kv1"}},24517:e=>{e.exports={childrenWrapper:"childrenWrapper-_RhDhmVQ",container:"container-_RhDhmVQ"}},95059:e=>{e.exports={highlighted:"highlighted-cwp8YRo6"}},93524:e=>{e.exports={linkItem:"linkItem-zMVwkifW"}},90854:e=>{e.exports={roundTabButton:"roundTabButton-JbssaNvk",disableFocusOutline:"disableFocusOutline-JbssaNvk",enableCursorPointer:"enableCursorPointer-JbssaNvk",large:"large-JbssaNvk",withStartIcon:"withStartIcon-JbssaNvk",iconOnly:"iconOnly-JbssaNvk",withEndIcon:"withEndIcon-JbssaNvk",startIconWrap:"startIconWrap-JbssaNvk",endIconWrap:"endIconWrap-JbssaNvk",small:"small-JbssaNvk",xsmall:"xsmall-JbssaNvk",primary:"primary-JbssaNvk",selected:"selected-JbssaNvk",disableActiveStateStyles:"disableActiveStateStyles-JbssaNvk",ghost:"ghost-JbssaNvk",fake:"fake-JbssaNvk",caret:"caret-JbssaNvk",visuallyHidden:"visuallyHidden-JbssaNvk"}},76912:e=>{e.exports={scrollWrap:"scrollWrap-vgCB17hK",overflowScroll:"overflowScroll-vgCB17hK",roundTabs:"roundTabs-vgCB17hK",center:"center-vgCB17hK",overflowWrap:"overflowWrap-vgCB17hK",start:"start-vgCB17hK"}},49128:e=>{e.exports={icon:"icon-WB2y0EnP",dropped:"dropped-WB2y0EnP"}},18429:(e,t,r)=>{"use strict";r.d(t,{SEPARATOR_PREFIX:()=>n,isSeparatorItem:()=>o});const n="###";function o(e){return e.startsWith(n)}},48199:(e,t,r)=>{"use strict";r.d(t,{BackButton:()=>v});var n,o=r(50959),a=r(64388),s=r(95694),l=r(49498),i=r(60176),c=r(35369),u=r(58478),d=r(73063),m=r(14127),p=r(18073),h=r(99243),g=r(42576);function f(e="large",t="1.2"){switch(e){case"large":return"1.2"===t?s:d;case"medium":return"1.2"===t?l:m;case"small":return"1.2"===t?i:p;case"xsmall":return"1.2"===t?c:h;case"xxsmall":return"1.2"===t?u:g;default:return l}}!function(e){e.Thin="1.2",e.Medium="1.5"}(n||(n={}));const v=o.forwardRef(((e,t)=>{const{"aria-label":r,flipIconOnRtl:n,...s}=e;return o.createElement(a.NavButton,{...s,"aria-label":r,ref:t,icon:f(e.size,e.iconStrokeWidth),flipIconOnRtl:n})}))},27011:(e,t,r)=>{"use strict";function n(e,t){
return t||null==e||("string"==typeof e||Array.isArray(e))&&0===e.length}r.d(t,{isIconOnly:()=>n})},14543:(e,t,r)=>{"use strict";r.d(t,{LightButton:()=>n.LightButton});r(9038);var n=r(15893);r(50959),r(21593),r(66860),r(1538),r(88317);r(49406)},9038:(e,t,r)=>{"use strict";r.d(t,{useLightButtonClasses:()=>c});var n=r(50959),o=r(97754),a=r(17946),s=r(27011),l=r(86332);const i=n.createContext({isInButtonGroup:!1,isGroupPrimary:!1}),c=(e,t,r)=>{const c=(0,n.useContext)(a.CustomBehaviourContext),{className:u,isSelected:d,children:m,showCaret:p,forceDirection:h,iconOnly:g,color:f="gray",variant:v="primary",size:b="medium",enableActiveStateStyles:y=c.enableActiveStateStyles,typography:S,isLink:x=!1,textWrap:w,isPills:k,isActive:C,startSlot:E,endSlot:I}=t,R=e[`typography-${((e,t,r)=>{if(r){const e=r.replace(/^\D+/g,"");return t?`semibold${e}`:r}switch(e){case"xsmall":return t?"semibold14px":"regular14px";case"small":case"medium":return t?"semibold16px":"regular16px";default:return""}})(b,d||k,S||void 0)}`],L=(0,n.useContext)(l.ControlGroupContext),{isInButtonGroup:T,isGroupPrimary:B}=(0,n.useContext)(i);return o(u,e.lightButton,x&&e.link,C&&e.active,d&&e.selected,(0,s.isIconOnly)(m,g)&&e.noContent,!!E&&e.withStartSlot,(p||!!I)&&e.withEndSlot,r&&e.withGrouped,h&&e[h],e[B?"primary":v],e[B?"gray":f],e[b],R,!y&&e.disableActiveStateStyles,L.isGrouped&&e.grouped,w&&e.wrap,T&&e.disableActiveOnTouch,k&&e.pills)}},66860:(e,t,r)=>{"use strict";r.d(t,{LightButtonContent:()=>m});var n=r(50959),o=r(97754),a=r(34094),s=r(27011),l=r(9745),i=r(2948),c=r(1538),u=r.n(c);const d=e=>n.createElement(l.Icon,{className:o(u().caret,e&&u().activeCaret),icon:i});function m(e){const{showCaret:t,iconOnly:r,ellipsis:l=!0,textWrap:i,tooltipText:c,children:m,endSlot:p,startSlot:h,isActiveCaret:g}=e;[p,t].filter((e=>!!e));return n.createElement(n.Fragment,null,h&&n.createElement("span",{className:o(u().slot,u().startSlot)},h),!(0,s.isIconOnly)(m,r)&&n.createElement("span",{className:o(u().content,!i&&u().nowrap,"apply-overflow-tooltip","apply-overflow-tooltip--check-children-recursively","apply-overflow-tooltip--allow-text"),"data-overflow-tooltip-text":c??(0,a.getTextForTooltip)(m)},i||l?n.createElement(n.Fragment,null,n.createElement("span",{className:o(!i&&l&&u().ellipsisContainer,i&&u().textWrapContainer,i&&l&&u().textWrapWithEllipsis)},m),n.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},m)):n.createElement(n.Fragment,null,m,n.createElement("span",{className:u().visuallyHidden,"aria-hidden":!0},m))),p&&n.createElement("span",{className:o(u().slot,u().endSlot)},p),t&&d(g))}},15893:(e,t,r)=>{"use strict";r.d(t,{LightButton:()=>d});var n=r(50959),o=r(86332),a=r(9038),s=r(66860),l=r(1538),i=r.n(l),c=r(88317),u=r.n(c);function d(e){
const{isGrouped:t}=n.useContext(o.ControlGroupContext),{reference:r,className:l,isSelected:c,children:d,iconOnly:m,ellipsis:p,showCaret:h,forceDirection:g,endSlot:f,startSlot:v,color:b,variant:y,size:S,enableActiveStateStyles:x,typography:w,textWrap:k=!1,maxLines:C,style:E={},isPills:I,isActive:R,tooltipText:L,role:T,...B}=e,N=k?C??2:1,M=N>0?{...E,"--ui-lib-light-button-content-max-lines":N}:E;return n.createElement("button",{...B,className:(0,a.useLightButtonClasses)({...u(),...i()},{className:l,isSelected:c,children:d,iconOnly:m,showCaret:h,forceDirection:g,endSlot:f,startSlot:v,color:b,variant:y,size:S,enableActiveStateStyles:x,typography:w,textWrap:k,isPills:I,isActive:R},t),ref:r,style:M,role:T},n.createElement(s.LightButtonContent,{showCaret:h,isActiveCaret:h&&(I||R||c),iconOnly:m,ellipsis:p,textWrap:k,tooltipText:L,endSlot:f,startSlot:v},d))}},125:(e,t,r)=>{"use strict";r.d(t,{useForceUpdate:()=>o});var n=r(50959);const o=()=>{const[,e]=(0,n.useReducer)((e=>e+1),0);return e}},34094:(e,t,r)=>{"use strict";r.d(t,{getTextForTooltip:()=>s});var n=r(50959);const o=e=>(0,n.isValidElement)(e)&&Boolean(e.props.children),a=e=>null==e||"boolean"==typeof e||"{}"===JSON.stringify(e)?"":e.toString()+" ",s=e=>Array.isArray(e)||(0,n.isValidElement)(e)?n.Children.toArray(e).reduce(((e,t)=>{let r="";return r=(0,n.isValidElement)(t)&&o(t)?s(t.props.children):(0,n.isValidElement)(t)&&!o(t)?"":a(t),e.concat(r)}),"").trim():a(e)},3685:(e,t,r)=>{"use strict";function n(){return window.configurationData?.exchanges?.map((e=>({...e,country:"",providerId:"",flag:""})))??[]}r.d(t,{getExchanges:()=>n})},36279:(e,t,r)=>{"use strict";var n;r.d(t,{LogoSize:()=>n,getLogoUrlResolver:()=>s}),function(e){e[e.Medium=0]="Medium",e[e.Large=1]="Large"}(n||(n={}));class o{getSymbolLogoUrl(e){return e}getCountryFlagUrl(){return""}getCryptoLogoUrl(e){return e}getProviderLogoUrl(e){return e}getSourceLogoUrl(e){return e}getBlockchainContractLogoUrl(e){return e}}let a;function s(){return a||(a=new o),a}},69654:(e,t,r)=>{"use strict";r.d(t,{DialogSearch:()=>d});var n=r(50959),o=r(97754),a=r.n(o),s=r(11542),l=r(9745),i=r(6347),c=r(54313),u=r(92335);function d(e){const{children:t,isMobile:o,renderInput:d,onCancel:p,containerClassName:h,inputContainerClassName:g,iconClassName:f,cancelTitle:v=s.t(null,void 0,r(4543)),...b}=e;return n.createElement("div",{className:a()(u.container,o&&u.mobile,h)},n.createElement("div",{className:a()(u.inputContainer,o&&u.mobile,g,p&&u.withCancel)},d||n.createElement(m,{isMobile:o,...b})),t,n.createElement(l.Icon,{className:a()(u.icon,o&&u.mobile,f),icon:o?c:i}),p&&(!o||""!==b.value)&&n.createElement("div",{className:a()(u.cancel,o&&u.mobile),onClick:p},v))}function m(e){const{className:t,reference:r,isMobile:o,value:s,onChange:l,onFocus:i,onBlur:c,onKeyDown:d,onSelect:m,placeholder:p,activeDescendant:h,...g}=e;return n.createElement("input",{...g,ref:r,type:"text",className:a()(t,u.input,o&&u.mobile),autoComplete:"off",role:"searchbox","data-role":"search",placeholder:p,value:s,onChange:l,onFocus:i,onBlur:c,onSelect:m,
onKeyDown:d,"aria-activedescendant":h})}},96967:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchDialogContentItem:()=>D});var n=r(50959),o=r(97754),a=r.n(o),s=(r(11542),r(50151)),l=r(9745),i=r(56570),c=r(24637),u=r(97006),d=r(84524),m=r(24633),p=r(77975),h=r(45345),g=r(32563),f=r(91682),v=r(618),b=r(36279),y=r(59695),S=r(58492),x=r(39330),w=r(19938),k=r(43010),C=r(79566);function E(e){const{className:t,placeholderLetter:r,url1:o,url2:s,size:l="xxxsmall"}=e,i=(0,n.useRef)(null),c=(0,n.useRef)(null),u=(0,n.useRef)(null),d=(0,n.useRef)(null),m=(0,n.useRef)(null),p=(0,n.useRef)(null);return(0,k.useIsomorphicLayoutEffect)((()=>{const e=void 0===o?[]:void 0===s?[o]:[o,s],t=p.current=(r=e,Promise.all(r.map((e=>(0,w.getImage)(`symbol_logo_${e}`,e,R).then((e=>e.cloneNode()))))));var r;t.catch((()=>[])).then((e=>{if(t===p.current)switch(e.length){case 0:u.current?.classList.add(C.hidden),c.current?.classList.add(y.hiddenCircleLogoClass),i.current?.classList.remove(y.hiddenCircleLogoClass);break;case 1:I(c.current,e[0]),u.current?.classList.add(C.hidden),c.current?.classList.remove(y.hiddenCircleLogoClass),i.current?.classList.add(y.hiddenCircleLogoClass);break;case 2:I(d.current,e[0]),I(m.current,e[1]),u.current?.classList.remove(C.hidden),c.current?.classList.add(y.hiddenCircleLogoClass),i.current?.classList.add(y.hiddenCircleLogoClass)}}))}),[o,s]),n.createElement("span",{className:a()(t,C.container)},n.createElement("span",{ref:u,className:a()(C.pairContainer,C.hidden)},n.createElement("span",{className:(0,x.getBlockStyleClasses)(l)},n.createElement("span",{ref:m,className:a()(C.logo,(0,x.getLogoStyleClasses)(l))}),n.createElement("span",{ref:d,className:a()(C.logo,(0,x.getLogoStyleClasses)(l))}))),n.createElement("span",{ref:c,className:a()(C.logo,y.hiddenCircleLogoClass,(0,S.getStyleClasses)(l))}),n.createElement("span",{ref:i,className:a()(C.logo,(0,S.getStyleClasses)(l))},n.createElement(y.CircleLogo,{size:l,placeholderLetter:r})))}function I(e,t){e&&(e.innerHTML="",e.appendChild(t))}function R(e){e.crossOrigin="",e.decoding="async"}var L=r(94869);function T(e){const{logoId:t,baseCurrencyLogoId:r,currencyLogoId:o,placeholder:s,className:l,size:i="xsmall"}=e,c=(0,n.useMemo)((()=>{const e={logoid:t,"currency-logoid":o,"base-currency-logoid":r};return(0,v.removeUsdFromCryptoPairLogos)((0,v.resolveLogoUrls)(e,b.LogoSize.Medium))}),[t,o,r]);return n.createElement(E,{key:i,className:a()(L.logo,l),url1:c[0],url2:c[1],placeholderLetter:s,size:i})}var B=r(29562),N=r(69533),M=r(92069);function D(e){
const{dangerousTitleHTML:t,title:r,dangerousDescriptionHTML:o,description:v,searchToken:b,exchangeName:y,marketType:S,onClick:x,isSelected:w,isEod:k=!1,isActive:C=!1,isOffset:E=!1,invalid:I=!1,isHighlighted:R=!1,hideExchange:L=!1,hideMarkedListFlag:D=!1,onExpandClick:O,isExpanded:A,hoverComponent:_,country:P,providerId:F,source:W,source2:Q,type:U,flag:V,itemRef:K,onMouseOut:z,onMouseOver:H,className:Z,actions:G,reference:q,fullSymbolName:j,logoId:$,currencyLogoId:Y,baseCurrencyLogoId:X,shortName:J,hideLogo:ee=!1,exchangeTooltip:te,hideMarketType:re,isPrimary:ne}=e,{isSmallWidth:oe,isMobile:ae}=(0,s.ensureNotNull)((0,n.useContext)(d.SymbolSearchItemsDialogContext)),se=Boolean(_),le=!I&&!L&&(ae||!se),ie=(0,p.useWatchedValueReadonly)({watchedValue:h.watchedTheme})===m.StdTheme.Dark?M.dark:M.light,ce=_,ue=i.enabled("show_symbol_logos"),de=i.enabled("show_exchange_logos"),me=ue||!1,pe=Q?.description??W,he=Q?.name??W;return n.createElement("div",{className:a()(M.itemRow,oe&&M.multiLine,R&&M.highlighted,R&&ie,w&&M.selected,C&&M.active,I&&M.invalid,!ae&&g.mobiletouch&&se&&M.hover,Z),onClick:function(e){if(!x||e.defaultPrevented)return;e.preventDefault(),x(e)},"data-role":e["data-role"]||"list-item","data-active":C,"data-type":S,"data-name":"symbol-search-dialog-content-item",onMouseOut:z,onMouseOver:H,ref:q},n.createElement("div",{ref:K,className:a()(M.itemInfoCell,M.cell,E&&M.offset)},n.createElement("div",{className:a()(M.actionHandleWrap,!me&&M.fixedWidth)},n.createElement(n.Fragment,null,!1,O&&n.createElement("div",{onClick:function(e){if(!O||e.defaultPrevented)return;e.preventDefault(),O(e)}},n.createElement(l.Icon,{className:a()(M.expandHandle,A&&M.expanded,w&&M.selected),icon:N})),me&&!E&&n.createElement("div",{className:a()(M.logo,Boolean(O)&&M.isExpandable)},n.createElement(T,{key:j,logoId:$,currencyLogoId:Y,baseCurrencyLogoId:X,placeholder:J?J[0]:void 0})))),n.createElement("div",{className:a()(M.description,me&&E&&M.offset)},r&&n.createElement("div",{className:a()(M.symbolTitle,C&&M.active,I&&M.invalid,!Boolean(o)&&M.noDescription,!g.mobiletouch&&"apply-overflow-tooltip"),"data-overflow-tooltip-text":r,"data-name":"list-item-title"},"string"==typeof r&&b?n.createElement(c.HighlightedText,{className:M.highlightedText,text:r,queryString:b,rules:(0,u.createRegExpList)(b)}):r,k&&n.createElement("span",{className:M.dataMode},"E")),!r&&t&&n.createElement("div",{className:a()(M.symbolTitle,C&&M.active,I&&M.invalid,!g.mobiletouch&&"apply-overflow-tooltip"),"data-name":"list-item-title","data-overflow-tooltip-text":(0,f.removeTags)(t)},n.createElement("span",{dangerouslySetInnerHTML:{__html:t}}),k&&n.createElement("span",{className:M.dataMode},"E")),oe&&ge())),!oe&&n.createElement("div",{className:a()(M.cell,M.descriptionCell,Boolean(ce)&&M.addition)},ge(),ce?n.createElement(ce,{...e,className:M.actions,onMouseOver:void 0,onMouseOut:void 0}):null),oe&&ce?n.createElement(ce,{...e,className:M.cell,onMouseOver:void 0,onMouseOut:void 0}):null,le&&n.createElement("div",{className:a()(M.exchangeCell,M.cell)
},n.createElement("div",{className:a()(M.exchangeDescription)},!re&&n.createElement("div",{className:a()(M.marketType,C&&M.active)},S),n.createElement("div",{className:M.source},!1,"economic"===U&&pe&&he?n.createElement("div",{className:a()(M.exchangeName,C&&M.active,"apply-common-tooltip",M.narrow,re&&M.wide),title:pe},he):n.createElement("div",{className:a()(M.exchangeName,C&&M.active,te&&"apply-common-tooltip"),title:te},y))),de&&n.createElement("div",{className:M.flag},n.createElement(B.SymbolSearchFlag,{key:de?`${j}_exchange`:`${P}_${F}_${Q?.id}_${U}_${V}`,className:M.icon,country:P,providerId:F,sourceId:"economic"===U&&Q?Q.id:void 0}))),n.createElement("div",{className:a()(M.cell,Boolean(G)&&M.actionsCell)},G));function ge(){if(I)return null;const e=a()(M.symbolDescription,C&&M.active,!g.mobiletouch&&"apply-overflow-tooltip apply-overflow-tooltip--allow-text");return v?n.createElement("div",{className:e},b?n.createElement(c.HighlightedText,{className:M.highlightedText,text:v,queryString:b,rules:(0,u.createRegExpList)(b)}):v):o?n.createElement("div",{"data-overflow-tooltip-text":(0,f.removeTags)(o),className:e,dangerouslySetInnerHTML:{__html:o}}):null}}},29562:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchFlag:()=>f});var n=r(50959),o=r(97754),a=r.n(o),s=r(24633),l=r(36279);const i=r.p+"mock-dark.16b5f3a431f502b03ae3.svg",c=r.p+"mock-light.d201313017eb2c1b989f.svg";function u(e){return e===s.StdTheme.Dark?i:c}var d=r(77975),m=r(45345),p=r(50151);const h=l.LogoSize.Medium;var g=r(6963);function f(e){const{country:t,tooltip:r,providerId:o,sourceId:s,className:i}=e,c=(0,d.useWatchedValueReadonly)({watchedValue:m.watchedTheme}),[f,v]=(0,n.useState)(function({country:e,providerId:t,sourceId:r}){const n=(0,l.getLogoUrlResolver)();return o=>{const a=e=>n.getProviderLogoUrl(e,h),s=[{value:r,resolve:a},{value:e,resolve:e=>n.getCountryFlagUrl(e.toUpperCase(),h)},{value:t,resolve:a}].find((({value:e})=>void 0!==e&&e.length>0));return void 0!==s?s.resolve((0,p.ensureDefined)(s.value)):u(o)}}({country:t,providerId:o,sourceId:s})(c));return n.createElement("img",{className:a()(i,"apply-common-tooltip",g.icon),crossOrigin:"","data-tooltip":r,src:f,onError:function(){v(u(c))}})}},58442:(e,t,r)=>{"use strict";r.d(t,{QualifiedSources:()=>n,qualifyProName:()=>s});var n,o=r(50151),a=r(56570);r(81319);function s(e){return e}!function(e){function t(e){return e.pro_name}function r(e){{const t=a.enabled("pay_attention_to_ticker_not_symbol")?e.ticker:e.name;return(0,o.ensureDefined)(t)}}e.fromQuotesSnapshot=function(e){return"error"===e.status?e.symbolname:e.values.pro_name},e.fromQuotesResponse=function(e){const{values:r,symbolname:n,status:o}=e;return"error"===o&&n?n:t(r)},e.fromQuotes=t,e.fromSymbolSearchResult=function(e,t){{const{ticker:r,symbol:n}=t??e;return a.enabled("pay_attention_to_ticker_not_symbol")?(0,o.ensureDefined)(r??n):(0,o.ensureDefined)(n)}},e.fromSymbolInfo=r,e.fromSymbolMessage=function(e,t){return"symbol_resolved"===t.method?r(t.params[1]):e}}(n||(n={}))},20882:(e,t,r)=>{"use strict";r.d(t,{createSearchSources:()=>l,
filterSearchSources:()=>a,isAllSearchSourcesSelected:()=>o,splitSearchSourcesByGroup:()=>s});const n=[];function o(e){return""===e.value()}function a(e,t){return e.filter((e=>e.includes(t)))}function s(e){const t=new Map;e.forEach((e=>{t.has(e.group())?t.get(e.group()).push(e):t.set(e.group(),[e])}));for(const e of t.values()){e[0].group()!==ExchangeGroup.NorthAmerica&&e.sort(((e,t)=>e.name().toLowerCase()>t.name().toLowerCase()?1:-1))}return new Map([...t.entries()].sort((([e],[t])=>n.indexOf(e)-n.indexOf(t))))}function l(e,t){return t.map((t=>new e(t)))}},70613:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchDialogBodyContext:()=>n});const n=r(50959).createContext(null)},84524:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchItemsDialogContext:()=>n});const n=r(50959).createContext(null)},73280:(e,t,r)=>{"use strict";r.d(t,{SymbolSearchItemsDialog:()=>Je});var n,o,a,s=r(50959),l=r(97754),i=r.n(l),c=r(11542),u=r(56570),d=r(44254),m=r(81319);function p(e){const t=function(e){let t,r=0,n=0;for(let o=0;o<e.length;o++){const a=e[o];if("whitespace"!==a.type)switch(r){case 0:if("number"!==a.type||1!=+a.value)return[];r=1;break;case 1:if(1!==r||"divide"!==a.type)return[];r=2,t=o+1;break;case 2:if("openBrace"===a.type)r=3,n=1;else if((0,d.isBinaryOperator)(a.type))return[];break;case 3:"openBrace"===a.type?n++:"closeBrace"===a.type&&(n--,n<=0&&(r=2))}}return e.slice(t)}(e);return t.length?(0,d.factorOutBraces)(t):(0,d.factorOutBraces)((0,d.tokenize)("1/("+h(e)+")"))}function h(e){return e.reduce(((e,t)=>"symbol"===t.type&&d.symbolTokenEscapeRe.test(t.value)?e+`'${t.value}'`:e+t.value),"")}function g(e){const t=function(e){const t=(0,d.tokenize)(e),r=[];return t.forEach((e=>{if("symbol"!==e.type)return;const[t]=(0,d.parseToken)(e);t&&r.push(t)})),r}(e);if(1===t.length)return t[0]}function f(e,t,r){const n=e.value,[o,a]=v(e,r),s=(0,m.getSymbolFullName)(t),l=d.symbolTokenEscapeRe.test(s)?`'${s}'`:s;return[n.substring(0,a)+l+n.substring(a+o.length),a+l.length]}function v(e,t){const{value:r,selectionStart:n}=e,o=(0,d.tokenize)(t?r.toUpperCase():r),a=(0,d.getTokenAtPos)(o,n||0);return[a?.value||"",a?a.offset:r.length,o]}!function(e){e.Init="init",e.Var="var",e.Operator="operator"}(n||(n={})),function(e){e[e.Init=0]="Init",e[e.Div=1]="Div",e[e.Expression=2]="Expression",e[e.BracedExpression=3]="BracedExpression"}(o||(o={})),function(e){e.Stocks="stocks",e.Futures="futures",e.Funds="funds",e.Forex="forex",e.Crypto="bitcoin,crypto",e.Index="index",e.Bond="bond",e.Economic="economic",e.Options="options"}(a||(a={}));const b=["futures","forex","bond","economic","options"];var y=r(84877),S=r(24437),x=r(79418),w=r(9745),k=r(86240),C=r(86781),E=r(84524),I=r(69654),R=r(3343),L=r(19291);function T(e,t,r){return`source-item-${e}-${t}-${r}`}var B=r(20882),N=r(24517);function M(e){const{children:t,className:r}=e;return s.createElement("div",{className:i()(N.container,r)},s.createElement("div",{className:N.childrenWrapper},t))}var D=r(50151),O=r(78036),A=r(24637),_=r(97006),P=r(91540),F=r(6109);function W(e){
const{searchSource:t,onClick:r,queryString:n,isFocused:o,id:a}=e,{symbolSearchContent:l,isAllSearchSourcesSelected:c,allSearchSourcesTitle:u,isMobile:d}=(0,O.useEnsuredContext)(E.SymbolSearchItemsDialogContext),p=l.currentSelectedSearchSource,h=(0,D.ensureNotNull)(p).value(),g=c(t),f=t.value()===h,v=(0,s.useMemo)((()=>(0,_.createRegExpList)(n)),[n]),b=t.description(),y=b&&!g,S=m.isSeparateSymbolSearchTabs&&g&&u?u:t.name(),x=i()(F.container,d?F.mobile:F.desktop,f&&F.selected,o&&F.focused,g&&F.allSelected,g&&F.libAllSelected,!g&&d&&F.bordered);return s.createElement("div",{className:i()(!d&&F.wrap,g&&F.libAllSelected),onClick:r,id:a},s.createElement("div",{className:x},s.createElement("div",{className:F.iconWrap},!!g&&s.createElement(w.Icon,{className:i()(F.icon,F.allSelectedIcon),icon:P})),s.createElement("div",{className:F.textBlock},s.createElement("div",{className:i()(F.title,!y&&!d&&F.titleWithoutDesc)},s.createElement(A.HighlightedText,{className:i()(f&&F.highlighted),queryString:n,text:S,rules:v})),y&&s.createElement("div",{className:i()(F.description,"apply-overflow-tooltip")},s.createElement(A.HighlightedText,{className:F.highlighted,queryString:n,rules:v,text:b})))))}var Q=r(77975),U=r(45345),V=r(24633),K=r(70613),z=r(66619),H=r(67562),Z=r(96137);const G={emptyTextClassName:Z.emptyText};function q(e){const{searchSources:t}=e,{setSelectedIndex:n,setSelectedSearchSource:o,setMode:a,isMobile:l,emptyState:u,autofocus:d}=(0,O.useEnsuredContext)(E.SymbolSearchItemsDialogContext),p=(0,Q.useWatchedValueReadonly)({watchedValue:U.watchedTheme})===V.StdTheme.Dark?z:H,h=(0,C.useMatchMedia)(k["media-phone-vertical"]),[g,f]=(0,s.useState)(""),v=(0,s.useMemo)((()=>[{group:null,sources:(0,m.createGroupColumns)((0,B.filterSearchSources)(t,g),h?1:2)}]),[t,g,h]),b=((0,s.useMemo)((()=>({})),[]),(0,s.useRef)(null)),y=(0,s.useRef)(null),{focusedItem:S,activeDescendant:x,handleKeyDown:N,resetFocusedItem:D}=function(e,t,r){const[n,o]=(0,s.useState)(null),[a,l]=(0,s.useState)("");function i(t){const r=e[t.groupIndex].sources[t.col].length-1;if(t.row===r){const e=d(t.groupIndex+1);if(null===e)return;return t.col>0&&!u({...t,groupIndex:e,row:0})?void o({groupIndex:e,col:0,row:0}):void o({...t,groupIndex:e,row:0})}o({...t,row:t.row+1})}function c(t){if(0===t.row){const r=d(t.groupIndex-1,-1);if(null===r)return;const n=e[r].sources[t.col]?.length??0;return 0===n?void o({groupIndex:r,col:0,row:0}):void o({...t,groupIndex:r,row:n-1})}o({...t,row:t.row-1})}function u(t){return Boolean(e[t.groupIndex]?.sources[t.col]?.[t.row])}function d(t=0,r=1){const n=e.length;let o=(t+n)%n;for(;!u({groupIndex:o,col:0,row:0});)if(o=(o+r+n)%n,o===t)return null;return o}return(0,s.useEffect)((()=>{if(!r.current)return;if(!n)return void l("");const e=T(n.groupIndex,n.col,n.row),t=r.current.querySelector(`#${e}`);t?.scrollIntoView({block:"nearest"}),l(e)}),[n]),(0,s.useEffect)((()=>{o(null)}),[t]),{focusedItem:n,activeDescendant:a,handleKeyDown:function(a){if(!r.current)return;const s=(0,R.hashFromEvent)(a);if(32!==s&&13!==s)switch((0,
L.mapKeyCodeToDirection)(s)){case"blockNext":if(a.preventDefault(),!n){const e=d();if(null===e)break;o({groupIndex:e,col:0,row:0});break}i(n);break;case"blockPrev":if(a.preventDefault(),!n)break;c(n);break;case"inlineNext":{if(!n||t)break;a.preventDefault();const r=e[n.groupIndex].sources.length;if(n.col===r-1||!u({...n,col:n.col+1})){i({...n,col:0});break}o({...n,col:n.col+1});break}case"inlinePrev":{if(!n||t)break;a.preventDefault();const r=e[n.groupIndex].sources.length;if(0===n.col){if(0!==n.row){c({...n,col:r-1});break}const t=d(n.groupIndex-1,-1);if(null===t)break;const a=e[t].sources.length,s=e[t].sources[0].length;if(!u({groupIndex:t,col:a-1,row:s-1})){c(n);break}o({groupIndex:t,col:a-1,row:s-1});break}o({...n,col:n.col-1});break}}else{if(!n)return;a.preventDefault();const e=r.current.querySelector(`#${T(n.groupIndex,n.col,n.row)}`);e instanceof HTMLElement&&e.click()}},resetFocusedItem:()=>o(null)}}(v,h,y);(0,s.useLayoutEffect)((()=>{d&&b?.current?.focus()}),[]);const A=u?s.createElement(u,null):s.createElement(M,{className:Z.noResultsDesktop},s.createElement(w.Icon,{icon:p,className:Z.emptyIcon}),s.createElement("div",{className:Z.emptyText},c.t(null,void 0,r(53182)))),_=!(v.length&&v.every((e=>0===e.sources.length)));return s.createElement(K.SymbolSearchDialogBodyContext.Provider,{value:G},s.createElement(I.DialogSearch,{placeholder:c.t(null,void 0,r(8573)),onChange:function(e){D(),f(e.target.value),y&&y.current&&(y.current.scrollTop=0)},reference:b,onKeyDown:N,onBlur:D,"aria-activedescendant":x}),_?s.createElement("div",{ref:y,className:i()(Z.contentList,!l&&Z.contentListDesktop),onTouchStart:function(){b.current?.blur()}},v.map(((e,t)=>{const{group:r,sources:n}=e;return 0===n.length?s.createElement(s.Fragment,{key:r}):s.createElement(s.Fragment,{key:r},!1,s.createElement("div",{className:i()(Z.searchSourceItemsContainer,!l&&Z.searchSourceItemsContainerDesktop,h&&Z.oneColumn)},n.map(((e,r)=>s.createElement("div",{key:`${t}-${r}`,className:Z.column},e.map(((e,n)=>s.createElement(W,{id:T(t,r,n),isFocused:!!S&&(S.groupIndex===t&&S.col===r&&S.row===n),key:e.value(),searchSource:e,queryString:g,onClick:P.bind(null,e)}))))))))}))):A);function P(e){o(e),a("symbolSearch"),n(-1)}}var j,$,Y,X=r(32227),J=r(14051);r(84906);function ee(e){return e.hasOwnProperty("exchange")}async function te(e){{const t=await async function(e){return new Promise((t=>{window.ChartApiInstance.searchSymbols(e.text||"",e.exchange||"",e.type||"",(e=>{t(e)}))}))}(e);return{symbols:t,symbols_remaining:0}}}!function(e){e.SourceId="source_id",e.EconomicCategory="economic_category",e.SearchType="search_type",e.Sector="sector",e.Product="product",e.Centralization="centralization",e.OnlyHasOptions="only_has_options"}(j||(j={})),function(e){e[e.Prod=0]="Prod",e[e.Local=1]="Local"}($||($={})),function(e){e[e.Paginated=0]="Paginated",e[e.NoLimit=1]="NoLimit"}(Y||(Y={}));new Map([].map((({value:e,search_type:t})=>[e,t])))
;var re=r(78136),ne=r(51768),oe=r(68335),ae=r(81348),se=r(486),le=r(81574),ie=r(35119),ce=r(32617),ue=r(69135),de=r(63861),me=r(10070);function pe(e){const{state:t,update:r}=e,{searchRef:n,forceUpdate:o,upperCaseEnabled:a}=(0,D.ensureNotNull)((0,s.useContext)(E.SymbolSearchItemsDialogContext)),l=(0,d.tokenize)(n.current?.value),i=function(e){const t={braceBalance:0,currentState:"var",warnings:[],errors:[]};if(!u.enabled("show_spread_operators"))return t;let r="init";const n=[];for(let o=0;o<e.length;o++){const a=e[o];if("whitespace"!==a.type){if("incompleteSymbol"===a.type||"incompleteNumber"===a.type){const r=o!==e.length-1,n={status:r?"error":"incomplete",reason:"incomplete_token",offset:a.offset,token:a};if(r?t.errors.push(n):t.warnings.push(n),r)continue}switch(a.type){case"symbol":case"number":if("var"===r){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}r="var";break;case"plus":case"minus":case"multiply":case"divide":case"power":if("var"!==r){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}r="operator";break;case"openBrace":if("var"===r){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}n.push(a),r="init";break;case"closeBrace":if("var"!==r){t.errors.push({status:"error",reason:"unexpected_token",offset:a.offset,token:a});continue}n.pop()||t.errors.push({status:"error",reason:"unbalanced_brace",offset:a.offset,token:a}),r="var";break;case"unparsed":t.errors.push({status:"error",reason:"unparsed_entity",offset:a.offset,token:a})}}}for(t.braceBalance=n.length,"var"!==r&&t.warnings.push({status:"incomplete",token:e[e.length-1]});n.length;){const e=n.pop();e&&t.warnings.push({status:"incomplete",reason:"unbalanced_brace",offset:e.offset,token:e})}return t.currentState=r,t}(l);let c=[{icon:se,insert:"/",type:"binaryOp",name:"division"},{icon:le,insert:"-",type:"binaryOp",name:"subtraction"},{icon:ie,insert:"+",type:"binaryOp",name:"addition"},{icon:ce,insert:"*",type:"binaryOp",name:"multiplication"}];return u.enabled("hide_exponentiation_spread_operator")||(c=c.concat([{icon:ue,insert:"^",type:"binaryOp",name:"exponentiation"}])),u.enabled("hide_reciprocal_spread_operator")||(c=c.concat([{icon:de,type:"complete",name:"1/x",callback:()=>{!n.current||i.errors.length||i.warnings.length||(n.current.value=h(p(l)),o())}}])),s.createElement("div",{className:me.actions},c.map((e=>s.createElement(ae.ToolWidgetButton,{className:me.actionButton,icon:e.icon,key:e.name,isDisabled:he(e,i),onClick:()=>function(e){if(!he(e,i)){if(e.insert&&n.current){const s=n.current.value+e.insert;n.current.value=s,n.current.setSelectionRange(s.length,s.length);const[l,,i]=v(n.current,a);t.current&&(t.current.selectedIndexValue=-1,t.current.searchSpreadsValue=(0,d.isSpread)(i),t.current.searchTokenValue=l),o(),r()}e.callback&&e.callback(),n.current?.focus(),(0,ne.trackEvent)("GUI","SS",e.name)}}(e)}))))}function he(e,t){let r=!1;if(!t.errors.length)switch(e.type){case"binaryOp":r="var"===t.currentState;break
;case"openBrace":r="var"!==t.currentState;break;case"closeBrace":r="var"===t.currentState&&t.braceBalance>0;break;case"complete":r=!t.errors.length&&!t.warnings.length}return!r}var ge=r(63932),fe=r(84952),ve=r(29006),be=r(14543),ye=r(10381),Se=r(52019),xe=r(92244);const we=(0,m.getDefaultSearchSource)();function ke(e){const{mode:t,setMode:n,searchRef:o,cachedInputValue:a,setSelectedIndex:l,setSelectedSearchSource:u,isAllSearchSourcesSelected:d,allSearchSourcesTitle:p,upperCaseEnabled:h,symbolSearchContent:g}=(0,O.useEnsuredContext)(E.SymbolSearchItemsDialogContext),f=g.currentSelectedSearchSource,v=(0,D.ensureNotNull)(f),b="symbolSearch"===t,y=d(v),S=m.isSeparateSymbolSearchTabs&&y&&p?p:v.name(),x=(0,s.useCallback)((()=>{if(m.isSeparateSymbolSearchTabs&&!y&&we)return u(we),l(-1),void o.current?.focus();o.current&&(a.current=h?o.current.value.toUpperCase():o.current.value),n("exchange")}),[y,o,h,n,u]);return m.isSeparateSymbolSearchTabs?b?s.createElement(be.LightButton,{onClick:x,isPills:!y,size:"xsmall",variant:y?"ghost":"quiet-primary",showCaret:y,endSlot:y?void 0:s.createElement(w.Icon,{icon:Se}),enableActiveStateStyles:!1,className:i()(xe.button,!y&&xe.withFlag),tabIndex:-1,"data-name":"sources-button"},s.createElement("div",{className:xe.buttonContent},null,s.createElement("span",null,S))):null:b?s.createElement("div",{className:i()(xe.flagWrap,"apply-common-tooltip",!y&&xe.withFlag),title:c.t(null,void 0,r(57640)),onClick:x,"data-name":"sources-button"},y&&s.createElement(w.Icon,{className:xe.icon,icon:P}),null,s.createElement("div",{className:i()(xe.title)},S),s.createElement(ye.ToolWidgetCaret,{className:xe.caret,dropped:!1})):null}var Ce=r(6591);function Ee(e){const{brokerButton:t=null}=e,{isSmallWidth:n,selectedFilterValues:o,setSelectedFilterValues:a,setSelectedIndex:l,isMobile:u,searchRef:d,symbolSearchContent:p}=(0,O.useEnsuredContext)(E.SymbolSearchItemsDialogContext),h=p.tabSelectFilters;return m.isSeparateSymbolSearchTabs?s.createElement("div",{className:i()(Ce.wrap,Ce.small,Ce.newStyles,u&&Ce.mobile)},t&&s.createElement("div",{className:Ce.brokerWrap},t),p.canChangeExchange&&s.createElement("div",{className:Ce.filterItem},s.createElement(ke,null)),h&&h.map((e=>{const{id:t,options:r,label:n}=e,i=r.find((e=>e.value===FILTER_DEFAULT_VALUE));if(!i)throw new Error("There must be default filter value in filter definition");const c=r.find((e=>e.value===o[p.currentSymbolType]?.[t]))||i;return s.createElement("div",{key:t,className:Ce.filterItem},s.createElement(SymbolSearchSelectFilter,{selectedOption:c,defaultOption:i,options:r,onSelect:e=>{a(p.currentSymbolType,{[t]:e.value}),trackEvent("New SS",p.currentSymbolType,null===e.value?e.analyticsLabel:e.value),l(-1),d.current?.focus()},label:n,isMobile:u,"data-name":t}))}))):s.createElement("div",{className:i()(Ce.wrap,n&&Ce.small)},s.createElement("div",{className:Ce.item},s.createElement("div",{className:Ce.text},n?c.t(null,void 0,r(74007)):c.t(null,void 0,r(95481)))),s.createElement("div",{className:Ce.item},!n&&s.createElement("div",{className:Ce.text
},c.t(null,void 0,r(78734))),p.canChangeExchange&&s.createElement("div",{className:Ce.exchange},s.createElement(ke,null))))}var Ie=r(63273),Re=r(44458);function Le(e){const{onTouchMove:t,listRef:r,className:n,listWrapRef:o,virtualListKey:a,items:l,getItemSize:c,hideFeed:u,canLoadMore:d,onLoadMoreSymbols:p}=e,{mode:h,isSmallWidth:g,handleListWidth:f}=(0,O.useEnsuredContext)(E.SymbolSearchItemsDialogContext),[v,b]=(0,s.useState)(null),y=(0,ve.useResizeObserver)((function([e]){b(e.contentRect.height),f(e.contentRect.width)})),S=(0,s.useCallback)((e=>{const{index:t,style:r}=e;return s.createElement("div",{style:r},l[t])}),[l]),x=(0,s.useCallback)((e=>(0,D.ensure)(l[e].key)),[l]),w="watchlist"===h&&null!==v;return s.createElement("div",{className:i()(Re.wrap,w&&Re.watchlist,u&&Re.noFeed,u&&m.isSeparateSymbolSearchTabs&&Re.newStyles,n),onTouchMove:t,ref:y},s.createElement("div",{ref:o,className:i()(Re.scrollContainer,u&&Re.noFeed)},w?s.createElement(fe.VariableSizeList,{key:a,ref:r,className:Re.listContainer,width:"100%",height:(0,D.ensureNotNull)(v),itemCount:l.length,itemSize:c,children:S,itemKey:x,overscanCount:20,direction:(0,Ie.isRtl)()?"rtl":"ltr"}):s.createElement(s.Fragment,null,s.createElement("div",{className:i()(Re.listContainer,g&&Re.multiLineItemsContainer)},!m.isSeparateSymbolSearchTabs&&s.createElement(Ee,null),...l,!1))))}var Te=r(96967),Be=r(47308),Ne=r(76717);const Me=u.enabled("hide_image_invalid_symbol");function De(e){const{otherSymbolsCount:t,onChangeSymbolTypeFilter:r,onResetFilters:n,onListTouchMove:o,brokerTitle:a,brokerLogoInfo:i,isBrokerActive:c,onBrokerToggle:u,listRef:d,listWrapRef:p,onLoadMoreSymbols:h,canLoadMore:g}=e,{mode:f,isMobile:v,selectedSymbolType:b,symbolTypes:y,feedItems:S,contentItem:x,emptyState:w=Oe,symbolSearchContent:k,symbolSearchState:C}=(0,O.useEnsuredContext)(E.SymbolSearchItemsDialogContext),I=a?s.createElement(BrokerButton,{brokerTitle:a,isActive:c,onToggle:u,onKeyDown:e=>{const t=(0,R.hashFromEvent)(e);t!==9+R.Modifiers.Shift&&9!==t&&e.stopPropagation()},logoInfo:i}):null,L=y.map((e=>({id:e.value,children:e.name}))),T="symbolSearch"===f&&["good","loadingWithPaginated"].includes(C),B=x??Te.SymbolSearchDialogContentItem,N=(0,s.useMemo)((()=>S.map((e=>s.createElement(B,{...e,searchToken:k.token})))),[S]);return s.createElement(s.Fragment,null,"symbolSearch"===f&&s.createElement(s.Fragment,null,s.createElement("div",{className:l(Ne.bubblesContainer,!v&&I&&Ne.withButton,v&&Ne.mobile)},y.length>0&&s.createElement(Be.RoundButtonTabs,{id:"symbol-search-tabs",isActive:e=>e.id===b,onActivate:r,overflowBehaviour:v?"scroll":"wrap",className:l(Ne.bubbles,v&&Ne.mobile,m.isSeparateSymbolSearchTabs&&(k.withFilters||I)&&!v&&Ne.withFilters),items:L},v?null:s.createElement("div",null,I)),!m.isSeparateSymbolSearchTabs&&v&&y.length>0&&a&&s.createElement("div",{className:Ne.brokerButtonWrap},I)),m.isSeparateSymbolSearchTabs&&s.createElement(Ee,{brokerButton:v?I:void 0})),s.createElement(Le,{listRef:d,listWrapRef:p,onTouchMove:o,items:N,getItemSize:()=>_e,onLoadMoreSymbols:h,canLoadMore:g,
hideFeed:!T}),"loading"===C&&s.createElement("div",{className:Ne.spinnerWrap},s.createElement(ge.Spinner,null)),"symbolSearch"===f&&s.createElement(s.Fragment,null,!1,"empty"===C&&s.createElement(w,null)))}function Oe(e){const t=(0,Q.useWatchedValueReadonly)({watchedValue:U.watchedTheme})===V.StdTheme.Dark?z:H;return s.createElement(M,{className:Ne.noResultsDesktop},!Me&&s.createElement(w.Icon,{icon:t,className:Ne.emptyIcon}),s.createElement("div",{className:Ne.emptyText},c.t(null,void 0,r(76822))))}const Ae=(0,m.getDefaultSearchSource)(),_e=52;function Pe(e){const{mode:t,setMode:n,setSelectedIndex:o,isMobile:a,selectedSearchSource:l,setSelectedSearchSource:p,isAllSearchSourcesSelected:h,selectedSymbolType:b,setSelectedSymbolType:y,symbolSearchContent:S,setSymbolSearchContent:x,searchRef:w,setSearchSpreads:k,showSpreadActions:C,selectedItem:R,forceUpdate:L,placeholder:T,initialScreen:B,footer:N,searchInput:M,upperCaseEnabled:D,externalInput:A,handleKeyDown:_,customSearchSymbols:P,filterDefinitions:F,filterQueryParams:W,searchSources:Q,symbolSearchState:U,setSymbolSearchState:V,onEmptyResults:z}=(0,O.useEnsuredContext)(E.SymbolSearchItemsDialogContext),H=P??te,Z=(0,s.useRef)(t);Z.current=t;const G=(0,s.useRef)(new AbortController),[q,j]=(0,s.useState)(0),$=(0,s.useRef)(0),[Y,ae]=(0,s.useState)(S.token),se=(0,s.useRef)(null),le=(0,s.useRef)(null),ie=(0,s.useRef)({selectedIndexValue:-1,searchTokenValue:"",searchSpreadsValue:!0}),ce=(0,s.useRef)(null),ue=(0,s.useRef)(null),de=(0,s.useRef)(null),{broker:me=null,brokerId:he,brokerTitle:ge,brokerLogoInfo:fe,isBrokerChecked:ve=!1,setIsBrokerChecked:be=()=>{},unhideSymbolSearchGroups:ye=""}={brokerId:void 0,brokerTitle:void 0,brokerLogoInfo:void 0};(0,s.useEffect)((()=>()=>{G.current.abort(),Pe(),Fe()}),[]),(0,s.useEffect)((()=>{w?.current&&ae(w.current.value)}),[]),(0,s.useEffect)((()=>{const e=w.current;if(e)return e.addEventListener("input",Ie),e.addEventListener("focus",Oe),e.addEventListener("select",Ee),e.addEventListener("click",Ee),e.addEventListener("keyup",_e),A&&_&&e.addEventListener("keydown",_),()=>{e&&(e.removeEventListener("input",Ie),e.removeEventListener("focus",Oe),e.removeEventListener("select",Ee),e.removeEventListener("click",Ee),e.removeEventListener("keyup",_e),A&&_&&e.removeEventListener("keydown",_))}}),[_]),(0,s.useEffect)((()=>{Boolean(B)&&""===Y.trim()?x((e=>{const t=Boolean(l&&Q.length>1&&!(0,m.exchangeSelectDisabled)(b)),r=F?.[b];return{...e,tabSelectFilters:r,currentSymbolType:b,canChangeExchange:t,withFilters:Boolean(t||r?.length),token:Y,currentTabAvailableSearchSources:Q,currentSelectedSearchSource:l}})):(x((e=>({...e,symbolStartIndex:0}))),Le(Y,b,l).then((()=>{se.current&&(se.current.scrollTop=0)})))}),[Y,b,l,ve,B,W]),(0,s.useEffect)((()=>{if(!R||!w.current)return;if(!u.enabled("show_spread_operators"))return w.current.value=R.symbol,void L();const e=ee(R)?R.exchange:R.parent.exchange;let t;t="contracts"in R&&R.contracts?.length?R.contracts[0]:R;const r={name:t.symbol,exchange:e,prefix:t.prefix,fullName:t.full_name},[n,o]=f(w.current,r,D)
;w.current.value=n,w.current.setSelectionRange(o,o),L()}),[R]);const Se=B??"div",xe=Boolean(B)&&"symbolSearch"!==t,we=M??I.DialogSearch,ke=(0,s.useMemo)((()=>({listRef:le,resetRecommends:Me,updateRecommends:Le,searchToken:Y,emptyTextClassName:Ne.emptyText,isBrokerChecked:ve,symbolSearchState:U,currentMode:Z})),[le,Y,ve,U,Z,W]);return s.createElement(K.SymbolSearchDialogBodyContext.Provider,{value:ke},!(A&&"symbolSearch"===t)&&s.createElement(we,{reference:w,className:i()(Ne.search,D&&Ne.upperCase),placeholder:T||c.t(null,void 0,r(8573))},C&&s.createElement(pe,{state:ie,update:Re})),xe?s.createElement(Se,null):s.createElement(De,{otherSymbolsCount:q,onListTouchMove:function(){w.current?.blur()},onChangeSymbolTypeFilter:function(e){const{id:t}=e;y(t),o(-1)},onResetFilters:function(){m.isSeparateSymbolSearchTabs?"resetFilter"===U?y((0,m.getAllSymbolTypesValue)()):Ae&&p(Ae):(y((0,m.getAllSymbolTypesValue)()),Ae&&p(Ae));be(!1),a||w.current?.focus()},brokerTitle:ge,brokerLogoInfo:fe,isBrokerActive:ve,onBrokerToggle:be,listRef:le,listWrapRef:se,onLoadMoreSymbols:void 0,canLoadMore:void 0}),N);function Ce(){if(!w.current)return;const[e,t,r]=v(w.current,D);$.current=t,ie.current={selectedIndexValue:-1,searchSpreadsValue:(0,d.isSpread)(r),searchTokenValue:e},ce.current||(ce.current=setTimeout(Re,0))}function Ee(){if(!w.current)return;const[,e]=v(w.current,D);e!==$.current&&Ce()}function Ie(){u.enabled("show_spread_operators")?Ce():w.current&&(ie.current={selectedIndexValue:-1,searchSpreadsValue:!1,searchTokenValue:w.current.value},ce.current||(ce.current=setTimeout(Re,0)))}function Re(){const{selectedIndexValue:e,searchTokenValue:t,searchSpreadsValue:r}=ie.current;ce.current=null,(0,X.unstable_batchedUpdates)((()=>{k(r),o(e),ae(D?t.toUpperCase():t)}))}async function Le(e,t,r,n){try{"noop"===U?V("loading"):n?V("loadingWithPaginated"):(Pe(),Fe(),ue.current=setTimeout((()=>{const r=Boolean(l&&Q.length>1&&!(0,m.exchangeSelectDisabled)(t)),n=F?.[t];x({token:e,canChangeExchange:r,tabSelectFilters:n,withFilters:Boolean(r||n?.length),currentSymbolType:t,currentSelectedSearchSource:l,currentTabAvailableSearchSources:Q,renderSymbolSearchList:[],symbolsRemaining:0,symbolStartIndex:0}),V("loading")}),500)),We();(0,m.getAllSymbolTypesValue)();const o=!1;let a;if(ve&&me){a=(await(0,J.respectAbort)(G.current.signal,me.accountMetainfo())).prefix}const s=u.enabled("show_spread_operators")?g(e)??a??r?.getRequestExchangeValue():l?.getRequestExchangeValue(),i=g(e)?void 0:(r||l)?.getRequestCountryValue(),[c,d]=await Promise.all([Be(G.current.signal,e,t,r,s,i,n),o&&!n?getRecent():Promise.resolve([])]),p=d.filter((e=>s?e.exchange?.toLowerCase()===s.toLowerCase():!i||e.country?.toLowerCase()===i.toLowerCase())),h=new Set(p.map((e=>`${e.exchange}_${e.symbol}`))),f=c.symbols.filter((e=>!h.has(`${e.exchange}_${e.symbol}`)));let v=function(e,t=window.ChartApiInstance.symbolsGrouping()){const r={},n=[];for(let o=0;o<e.length;++o){const a=e[o];if(a.prefix||Array.isArray(a.contracts))return e;const s=t[a.type];if(void 0===s){n.push(a);continue}
const l=s.exec(a.symbol);if(l){const e=l[1];let t;r.hasOwnProperty(e)?t=r[e]:(t=n.length,r[e]=t,n.push({type:a.type,symbol:e,exchange:a.exchange,description:a.description,full_name:a.exchange+":"+e,contracts:[]})),n[t].contracts?.push(a)}else n.push(a)}return n}([...p,...f]);if(n&&(v=[...S.renderSymbolSearchList,...v]),!v.length)return x((r=>{const n=Boolean(l&&Q.length>1&&!(0,m.exchangeSelectDisabled)(t)),o=F?.[t];return{...r,canChangeExchange:n,tabSelectFilters:o,token:e,symbolsRemaining:0,withFilters:Boolean(n||o?.length),currentSymbolType:t,currentSelectedSearchSource:l,currentTabAvailableSearchSources:Q}})),Pe(),V("empty"),void Te();Pe(),x((r=>{const n=Boolean(l&&Q.length>1&&!(0,m.exchangeSelectDisabled)(t)),o=F?.[t];return{...r,canChangeExchange:n,tabSelectFilters:o,renderSymbolSearchList:v,token:e,symbolsRemaining:c.symbols_remaining,withFilters:Boolean(n||o?.length),currentSymbolType:t,currentSelectedSearchSource:l,currentTabAvailableSearchSources:Q,symbolStartIndex:r.symbolStartIndex+c.symbols.length}})),V("good")}catch(e){(0,J.skipAbortError)(e)}}function Te(){z&&(de.current=setTimeout((()=>z()),1e3))}async function Be(e,t,r,n,o,a,s){const l={serverHighlight:!1,text:u.enabled("show_spread_operators")?(0,d.shortName)(t):w.current?.value,exchange:o,country:a,type:r,lang:window.language||"",sortByCountry:void 0,brokerId:he,onlyTradable:Boolean(he)&&ve,unhideSymbolSearchGroups:ye,signal:e,start:s,filterQueryParams:W},i=(0,re.getSearchRequestDelay)();return void 0!==i&&await(0,J.delay)(e,i),H(l)}function Me(){We(),V("empty"),ae(""),k(!1),x((e=>({...e,symbolStartIndex:0}))),Pe()}function Oe(){"watchlist"===Z.current&&(n("symbolSearch"),(0,ne.trackEvent)("Watchlist","Mobile SS","Go to SS page"))}function _e(e){switch((0,oe.hashFromEvent)(e)){case 37:case 39:Ee()}}function Pe(){ue.current&&clearTimeout(ue.current)}function Fe(){de.current&&clearTimeout(de.current)}function We(){G.current.abort(),G.current=new AbortController}}var Fe=r(48199),We=r(74395),Qe=r(58442),Ue=r(56840);function Ve(e){const[t,r]=(0,s.useState)((()=>{const{defaultSearchSource:t,searchSources:r}=e,n=Ue.getValue("symboledit.exchangefilter","");return r.find((e=>e.value()===n))||t}));return[t,(0,s.useCallback)((e=>{var t;r(e),t=e,Ue.setValue("symboledit.exchangefilter",t.value())}),[])]}function Ke(e){const[t,r]=(0,s.useState)((()=>{if(1===e.types.length)return e.types[0].value;const t=Ue.getValue("symboledit.filter",(0,m.getAllSymbolTypesValue)());return e.types.find((e=>e.value===t))?t:(0,m.getAllSymbolTypesValue)()}));return[t,(0,s.useCallback)((e=>{var t;r(e),t=e,Ue.setValue("symboledit.filter",t)}),[])]}var ze=r(36947),He=r(82708),Ze=r(88145),Ge=r(76460),qe=r(63748);const je=!1,$e=(0,m.getAvailableSearchSources)(),Ye=(0,m.getDefaultSearchSource)(),Xe=u.enabled("uppercase_instrument_names");function Je(e){
const{onClose:t,symbolTypeFilter:n,initialMode:o,defaultValue:a="",showSpreadActions:l,hideMarkedListFlag:i,selectSearchOnInit:d=!0,onSearchComplete:p,dialogTitle:h=c.t(null,void 0,r(51165)),placeholder:g,fullscreen:v,initialScreen:x,wrapper:w,dialog:k,contentItem:C,footer:I,searchInput:R,emptyState:T,autofocus:N,dialogWidth:M,onKeyDown:D,searchSourcesScreen:O,customSearchSymbols:A,isDisableFiltering:_,disableRecents:P,shouldReturnFocus:F,onSymbolFiltersParamsChange:W,onEmptyResults:Q,enableOptionsChain:U}=e,V=(0,s.useMemo)((()=>{if(_)return[];const t=e.symbolTypes??(0,m.getAvailableSymbolTypes)();return n?n(t):t}),[]),K=void 0!==e.input,z=_?[]:$e,[H,Z]=(0,s.useState)((()=>at(a,U)?"options":o)),[G,j]=(0,s.useState)((()=>at(a,U))),[$,Y]=(0,s.useState)((()=>null)),X=(0,s.useRef)(function(e,t){const r=at(e,t);return(0,m.isOptionDefaultValue)(e)?r??e.value:e}(a,U)),[J,re]=Ve({searchSources:z,defaultSearchSource:Ye}),[ne,ae]=[],[se,le]=Ke({types:V}),[ie,ce]=[{},()=>{}],[ue,de]=(0,s.useState)(!1),[me,pe]=(0,s.useState)(-1),[he,ge]=(0,s.useState)("noop"),fe=m.isSeparateSymbolSearchTabs?TAB_SELECT_FILTER_MAP:void 0,ve=m.isSeparateSymbolSearchTabs?ne?.[se]||Ye:J,be=(0,s.useMemo)((()=>{if(!m.isSeparateSymbolSearchTabs)return z;return z.filter((e=>{const t=TAB_SOURCE_FILTER_MAP[se];if(!t)return!1;if(!se)return!0;const r=e.group();return r===ExchangeGroup.AllExchanges||r&&t.value.includes(r)}))}),[z,se]),[ye,Se]=(0,s.useState)((()=>{const e=Boolean(J&&$e.length>1&&!(0,m.exchangeSelectDisabled)(se)),t=fe?.[se];return{canChangeExchange:e,tabSelectFilters:t,withFilters:Boolean(e||t?.length),renderSymbolSearchList:[],token:X.current,symbolsRemaining:0,currentSymbolType:se,currentSelectedSearchSource:ve,currentTabAvailableSearchSources:be,symbolStartIndex:0}})),xe=(0,s.useCallback)((e=>{trackEvent("New SS",se,"Change sources"),ae?.(se,e),Se((t=>({...t,currentSelectedSearchSource:e})))}),[se,Se]),we=(0,s.useRef)(e.input??null),[ke,Ce]=(0,s.useState)(!1),Ee=(0,ze.useForceUpdate)(),[Re,Le]=(0,s.useState)(new Set),{broker:Te=null,brokerId:Be,unhideSymbolSearchGroups:Ne="",displayBrokerSymbol:Me=!1}={brokerId:void 0};(0,s.useLayoutEffect)((()=>{!we?.current||!K&&Boolean(we.current?.value)||(K||"compare"===H||(we.current.value=X.current),!N||K&&"symbolSearch"!==H||we.current.focus())}),[H]),(0,s.useEffect)((()=>{we?.current&&d&&N&&we.current.select()}),[]);const De=(0,s.useMemo)((()=>ye.renderSymbolSearchList.reduce(((e,t)=>{const r=rt(t),n=Re.has(r);return e.push(t),n&&t.contracts&&e.push(...t.contracts.map((e=>({...e,parent:t})))),e}),[])),[ye.renderSymbolSearchList,Re]),Oe=(0,s.useRef)(null);(0,s.useEffect)((()=>{-1!==me&&Oe.current?.scrollIntoView({block:"nearest"})}),[me,Oe]);const Ae=b.includes(se),_e=(0,s.useMemo)((()=>De.map(((e,t)=>{if(ee(e)){const r=rt(e),n=e.contracts?Re.has(r):void 0,o=t===me,a=ye.renderSymbolSearchList.findIndex((t=>t.symbol===e.symbol&&t.exchange===e.exchange))+1;return{key:t,numberInList:a,id:r,title:tt(e,Me),description:e.description,isOffset:!1,onClick:mt.bind(null,e,a),providerId:e.provider_id,
source:e.source,source2:e.source2,country:e.country?.toLocaleLowerCase(),type:e.type,exchangeName:null===e.exchange?void 0:e.exchange,exchangeTooltip:"",prefix:e.prefix||void 0,marketType:(0,We.marketType)(e.type,e.typespecs,!1),hideMarketType:Ae,isEod:e.params?.includes("eod")&&"economic"!==e.type,isYield:(0,Ze.isYield)(e),isExpanded:n,onExpandClick:e.contracts?pt.bind(null,r):void 0,fullSymbolName:e.contracts?Qe.QualifiedSources.fromSymbolSearchResult(e,e.contracts[0]):Qe.QualifiedSources.fromSymbolSearchResult(e),itemRef:o?Oe:void 0,isSelected:t===me,hideMarkedListFlag:i,item:e,logoId:e.logoid,currencyLogoId:e["currency-logoid"],baseCurrencyLogoId:e["base-currency-logoid"],shortName:(0,He.safeShortName)(Qe.QualifiedSources.fromSymbolSearchResult(e)),currencyCode:e.currency_code,isPrimary:e.is_primary_listing}}{const{parent:r}=e,n=rt(r),o=t===me,a=ye.renderSymbolSearchList.findIndex((e=>e.symbol===r.symbol&&e.exchange===r.exchange))+1;return{key:t,numberInList:a,id:n+e.symbol,dangerousTitleHTML:tt(e,Me),dangerousDescriptionHTML:`${r.description}`+(e.description?` (${e.description})`:""),isOffset:!0,isEod:e.params?.includes("eod"),isYield:(0,Ze.isYield)(e),onClick:ht.bind(null,e.parent,e,a),providerId:r.provider_id,country:r.country?.toLowerCase(),type:r.type,exchangeName:null===r.exchange?void 0:r.exchange,exchangeTooltip:"",marketType:(0,We.marketType)(r.type,e.typespecs,!1),hideMarketType:Ae,fullSymbolName:Qe.QualifiedSources.fromSymbolSearchResult(e.parent,e),itemRef:o?Oe:void 0,isSelected:o,hideMarkedListFlag:i,item:e}}}))),[ye.renderSymbolSearchList,Re,H,me,D]),Ue=(0,s.useMemo)((()=>function(e,t,r){const n=t?.[e],o=new Map(n?.map((e=>[e.id,e.urlParam]))),a=r[e];let s;if(a){s={};for(const[e,t]of Object.entries(a)){const r=o.get(e);r&&(s[r]=t)}}return s}(se,fe,ie)),[se,fe,ie]),Je=(0,s.useMemo)((()=>ye.renderSymbolSearchList.slice(0,20).map((e=>e.contracts?Qe.QualifiedSources.fromSymbolSearchResult(e,e.contracts[0]):Qe.QualifiedSources.fromSymbolSearchResult(e)))),[ye.renderSymbolSearchList]);(0,s.useEffect)((()=>{if(!W)return;const e=["resetFilter","resetTabFilter","empty"].includes(he)?[]:Je,t={...Ue,result_list:e};if(t.search_type||(t.search_type="bitcoin,crypto"===se?"crypto":se),!m.isSeparateSymbolSearchTabs)return t.exchange=ve?.getRequestCountryValue()??null,void W(t);if(se){const e=ve?.getRequestCountryValue()??null;e&&(t.country=e);const r=ve?.getRequestExchangeValue()??null;r&&(t.exchange=r)}W(t)}),[se,Ue,Je,ve,he]);const st=(0,s.useMemo)((()=>{if(A)return A}),[se,A,Ue,U]),lt=k??ot,it=lt!==ot&&!K,ct=(e,r)=>({mode:H,setMode:Z,selectedSearchSource:ve,setSelectedSearchSource:m.isSeparateSymbolSearchTabs?xe:re,isAllSearchSourcesSelected:B.isAllSearchSourcesSelected,allSearchSourcesTitle:m.isSeparateSymbolSearchTabs?TAB_SOURCE_FILTER_MAP[ye.currentSymbolType]?.allSearchSourcesTitle:void 0,selectedSymbolType:se,setSelectedSymbolType:le,selectedIndex:me,setSelectedIndex:pe,onClose:t,setSymbolSearchContent:Se,symbolSearchContent:ye,searchRef:we,cachedInputValue:X,searchSpreads:ue,setSearchSpreads:de,
handleListWidth:gt,isSmallWidth:ke,feedItems:_e,isMobile:e,showSpreadActions:l,selectSearchOnInit:d,isTablet:r,selectedItem:De[me],forceUpdate:Ee,placeholder:g,initialScreen:x,toggleExpand:pt,openedItems:Re,onSubmit:bt,onSearchComplete:p,footer:I,symbolTypes:V,contentItem:C,searchInput:R,emptyState:T,autofocus:N,upperCaseEnabled:Xe,externalInput:K,handleKeyDown:it?void 0:vt,customSearchSymbols:st,searchSources:be,filterDefinitions:fe,selectedFilterValues:ie,setSelectedFilterValues:ce,filterQueryParams:Ue,symbolSearchState:he,setSymbolSearchState:ge,onEmptyResults:void 0}),ut=O??q,dt=w??"div";return s.createElement(dt,null,s.createElement(y.MatchMediaMap,{rules:S.DialogBreakpoints},(({TabletSmall:e,TabletNormal:n})=>s.createElement(E.SymbolSearchItemsDialogContext.Provider,{value:ct(e,n)},s.createElement(lt,{..."exchange"===H?{title:c.t(null,void 0,r(28628)),dataName:"exchanges-search",render:()=>s.createElement(ut,{searchSources:ye.currentTabAvailableSearchSources}),additionalHeaderElement:s.createElement(Fe.BackButton,{onClick:()=>Z("symbolSearch"),className:qe.backButton,size:"medium","aria-label":c.t(null,{context:"input"},r(41256)),preservePaddings:!0,flipIconOnRtl:(0,Ie.isRtl)()}),additionalElementPos:"before"}:{title:h,dataName:"symbol-search-items-dialog",render:()=>s.createElement(Pe,null),additionalElementPos:"after"},shouldReturnFocus:F,fullScreen:v,onClose:t,onClickOutside:t,onKeyDown:it?void 0:vt,isOpened:!0})))));function mt(e,t,r){if(e.contracts)return e.contracts.length?void ht(e,e.contracts[0],t,r):void pt(rt(e));ht(e,void 0,t,r)}function pt(e){const t=new Set(Re);t.has(e)?t.delete(e):t.add(e),Le(t)}function ht(e,r,n,o){const a=r||e,{exchange:s}=e;if(u.enabled("show_spread_operators")){const e={name:a.symbol,exchange:s,prefix:a.prefix,fullName:a.full_name};if(ue)return ft(e),void Ee();if(we.current&&we.current.value.includes(","))return void ft(e)}yt([{resolved:!0,symbol:Qe.QualifiedSources.fromSymbolSearchResult(e,r),result:a}],n,o),t()}function gt(e){Ce("fixed"===M||e<=640)}function ft(e){if(!we.current)return;const[t,r]=f(we.current,e,Xe);we.current.value=t,we.current.setSelectionRange(r,r),we.current.focus()}function vt(e){if(e.target&&e.target!==we.current)return;const r=(0,oe.hashFromEvent)(e);switch(r){case 13:e.preventDefault(),bt(!0);break;case 27:if(e.preventDefault(),"exchange"===H)return void Z("symbolSearch");if("options"===H)return Z("symbolSearch"),j(null),void Y(null);t()}switch((0,L.mapKeyCodeToDirection)(r)){case"blockPrev":if(e.preventDefault(),0===me||"good"!==he)return;if(-1===me)return void pe(0);pe(me-1);break;case"blockNext":if(e.preventDefault(),me===_e.length-1||"good"!==he)return;pe(me+1);break;case"inlinePrev":{if(-1===me)return;const t=_e[me],{id:r,isOffset:n,onExpandClick:o}=t;if(!n&&r&&Re.has(r)&&Boolean(o)&&!Boolean(D)&&(e.preventDefault(),pt(r)),o)return void D?.(e,!0);break}case"inlineNext":{if(-1===me)return;const t=_e[me],{id:r,isOffset:n,onExpandClick:o}=t;if(n||!r||Re.has(r)||!Boolean(o)||Boolean(D)||(e.preventDefault(),pt(r)),o)return void D?.(e,!0);break}}D?.(e)
}function bt(e){if(!we.current)return;let r=we.current.value;if(u.enabled("show_spread_operators")&&ue&&r){const n=_e[me];if(n&&void 0!==n.isExpanded&&(n.onClick(),r=we.current.value),r.includes(",")){return yt(nt(r).map(et),null),void(e&&t())}return yt([{symbol:Xe?r.toUpperCase():r,resolved:!1}],null),void(e&&t())}if(r.includes(","))return yt(nt(r).map(et),null),void(e&&t());if(-1!==me){_e[me].onClick()}else if(u.enabled("allow_arbitrary_symbol_search_input")){const n=Xe?r.toUpperCase():r;if(n&&""!==n.trim()){const e=nt(n);if(je||void 0===Be||-1!==n.indexOf(":")){yt(e.map(et),null)}else(function(e){let t=!1;return Promise.all(e.map((e=>-1!==e.indexOf(":")||t?Promise.resolve({symbol:e,resolved:!1}):(t=!0,async function(e){await(Te?.accountMetainfo());const t=void 0,r=await te({strictMatch:!0,serverHighlight:!1,text:e,lang:window.language||"",brokerId:Be,onlyTradable:!0,unhideSymbolSearchGroups:Ne,exchange:t});if(0!==r.symbols.length){const e=r.symbols[0],{contracts:t}=e,n=t&&t.length>0?t[0]:void 0,o=e.prefix||e.exchange,a=n?n.symbol:e.symbol;if(o&&a)return{symbol:Qe.QualifiedSources.fromSymbolSearchResult(e,n),resolved:!0,result:e}}return{symbol:e,resolved:!1}}(e)))))})(e).then((e=>yt(e,null)))}e&&t()}else if("empty"!==he&&_e.length>0){_e[0].onClick()}}async function yt(e,t,r){const[{result:n,symbol:o,resolved:a}]=e,s=we.current?.value,l=!r||(0,Ge.isKeyboardClick)(r);let i=ue;void 0!==n&&ee(n)&&(i="spread"===n.type),p(e,{symbolType:se,isKeyboardEvent:l,numberInList:t,inputValue:s,isSpread:i})}}function et(e){return{symbol:Xe?e.toUpperCase():e,resolved:!1}}function tt(e,t){const{broker_symbol:r,symbol:n,description:o}=e;return`${"spread"===e.type?o:n}${t&&r?` (${r})`:""}`}function rt(e){return e.symbol+e.exchange+e.description}function nt(e){return e.split(",").map((e=>e.trim())).filter((e=>""!==e))}function ot(e){const{isMobile:t,isTablet:r}=(0,O.useEnsuredContext)(E.SymbolSearchItemsDialogContext);return s.createElement(x.AdaptivePopupDialog,{...e,className:i()(qe.dialog,!t&&(r?qe.tabletDialog:qe.desktopDialog)),backdrop:!0,draggable:!1})}function at(e,t){return null}},81319:(e,t,r)=>{"use strict";r.d(t,{createGroupColumns:()=>p,exchangeSelectDisabled:()=>m,getAllSymbolTypesValue:()=>d,getAvailableSearchSources:()=>c,getAvailableSymbolTypes:()=>u,getDefaultSearchSource:()=>i,getSymbolFullName:()=>l,isOptionDefaultValue:()=>g,isSeparateSymbolSearchTabs:()=>h});var n=r(11542),o=r(20882);class a{constructor(e){this._exchange=e}value(){return this._exchange.value}name(){return(0,o.isAllSearchSourcesSelected)(this)?n.t(null,void 0,r(34040)):this._exchange.name}description(){return this._exchange.desc}country(){return this._exchange.country}providerId(){return this._exchange.providerId}group(){return this._exchange.group}includes(e){return function(e,t){const r=t.toLowerCase(),{name:n,desc:o,searchTerms:a}=e;return n.toLowerCase().includes(r)||o.toLowerCase().includes(r)||void 0!==a&&a.some((e=>e.toLowerCase().includes(r)))}(this._exchange,e)}getRequestExchangeValue(){return this._exchange.value}getRequestCountryValue(){}
}var s=r(3685);function l(e){if(e.fullName)return e.fullName;let t;return t=e.prefix||e.exchange?(e.prefix||e.exchange)+":"+e.name:e.name,t.replace(/<\/?[^>]+(>|$)/g,"")}function i(){const e=c();return e.find(o.isAllSearchSourcesSelected)||e[0]||null}function c(){return(0,o.createSearchSources)(a,(0,s.getExchanges)())}function u(){return window.ChartApiInstance.supportedSymbolsTypes()}function d(){return""}function m(e){return!!h&&!TAB_SOURCE_FILTER_MAP[e]}function p(e,t=2){if(0===e.length)return[];if(1===t)return[e];const r=Math.floor(e.length/2)+e.length%2;return[e.slice(0,r),e.slice(r)].filter((e=>e.length>0))}const h=!1;function g(e){return"string"!=typeof e}},82708:(e,t,r)=>{"use strict";r.d(t,{safeShortName:()=>o});var n=r(13665);function o(e){try{return(0,n.shortName)(e)}catch(t){return e}}},44254:(e,t,r)=>{"use strict";r.d(t,{factorOutBraces:()=>m,getTokenAtPos:()=>d,isBinaryOperator:()=>c,isSpread:()=>u,parseToken:()=>h,shortName:()=>p,symbolTokenEscapeRe:()=>a,tokenize:()=>i});var n,o=r(18429);!function(e){e.Symbol="symbol",e.IncompleteSymbol="incompleteSymbol",e.Number="number",e.IncompleteNumber="incompleteNumber",e.SeparatorPrefix="separatorPrefix",e.OpenBrace="openBrace",e.CloseBrace="closeBrace",e.Plus="plus",e.Minus="minus",e.Multiply="multiply",e.Divide="divide",e.Power="power",e.Whitespace="whitespace",e.Unparsed="unparsed"}(n||(n={}));const a=/[+\-/*]/,s={number:/\d+(?:\.\d*|(?![a-zA-Z0-9_!:.&]))|\.\d+/,incompleteNumber:/\./,symbol:/(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0370-\u1FFF_\u2E80-\uFFFF^])(?:[^-+\/*^\s]'|[a-zA-Z0-9_\u0020\u0370-\u1FFF_\u2E80-\uFFFF_!:.&])*|'.+?'/,incompleteSymbol:/'[^']*/,separatorPrefix:o.SEPARATOR_PREFIX,openBrace:"(",closeBrace:")",plus:"+",minus:"-",multiply:"*",divide:"/",power:"^",whitespace:/[\0-\x20\s]+/,unparsed:null},l=new RegExp(Object.values(s).map((e=>{return null===e?"":`(${"string"==typeof e?(t=e,t.replace(/[\^$()[\]{}*+?|\\]/g,"\\$&")):e.source})`;var t})).filter((e=>""!==e)).concat(".").join("|"),"g");function i(e){if(!e)return[];const t=[],r=Object.keys(s);let n;for(;n=l.exec(e);){let e=!1;for(let o=r.length;o--;)if(n[o+1]){r[o]&&t.push({value:n[o+1],type:r[o],precedence:0,offset:n.index}),e=!0;break}e||t.push({value:n[0],type:"unparsed",precedence:0,offset:n.index})}return t}function c(e){return"plus"===e||"minus"===e||"multiply"===e||"divide"===e||"power"===e}function u(e){return e.length>1&&e.some((e=>c(e.type)))}function d(e,t){for(let r=0;r<e.length;r++){const n=e[r],o="symbol"===n.type||"incompleteSymbol"===n.type||"number"===n.type;if(n.offset<=t&&t<=n.offset+n.value.length&&o)return n}return null}function m(e){e=function(e){const t=[];for(const r of e)"whitespace"!==r.type&&t.push(r);return t}(e);const t=[],r=[];let n;for(let o=0;o<e.length;o++){const a=e[o];switch(a.type){case"plus":case"minus":case"multiply":case"divide":case"power":r.length&&r[r.length-1].minPrecedence>a.precedence&&(r[r.length-1].minPrecedence=a.precedence);break;case"openBrace":n={minPrecedence:1/0,openBraceIndex:o},r.push(n);break;case"closeBrace":{if(n=r.pop(),!n)break
;const a=e[n.openBraceIndex-1],s=e[o+1],l=a&&("plus"===a.type||"multiply"===a.type);(!c(s?.type)||s?.precedence<=n.minPrecedence)&&(!c(a?.type)||a?.precedence<n?.minPrecedence||a?.precedence===n?.minPrecedence&&l)&&(t.unshift(n.openBraceIndex),t.push(o),r.length&&r[r.length-1].minPrecedence>n.minPrecedence&&(r[r.length-1].minPrecedence=n.minPrecedence))}}}for(let r=t.length;r--;)e.splice(t[r],1);return e}function p(e){return m(i(e)).reduce(((e,t)=>{if("symbol"!==t.type)return e+t.value;const[,r]=h(t);return r?e+r:e}),"")}function h(e){const t=/^'?(?:([A-Z0-9_]+):)?(.*?)'?$/i.exec(e.value);return null===t?[void 0,void 0]:[t[1],t[2]]}},618:(e,t,r)=>{"use strict";r.d(t,{removeUsdFromCryptoPairLogos:()=>s,resolveLogoUrls:()=>a});var n=r(36279);const o=(0,n.getLogoUrlResolver)();function a(e,t=n.LogoSize.Medium){const r=e.logoid,a=e["base-currency-logoid"],s=e["currency-logoid"],l=r&&o.getSymbolLogoUrl(r,t);if(l)return[l];const i=a&&o.getSymbolLogoUrl(a,t),c=s&&o.getSymbolLogoUrl(s,t);return i&&c?[i,c]:i?[i]:c?[c]:[]}function s(e){return 2!==e.length?e:function(e){return e.some((e=>l(e)))}(e)&&!function(e){return e.some((e=>e.includes("country")&&!l(e)))}(e)?e.filter((e=>!l(e))):e}function l(e){return!1}},39330:(e,t,r)=>{"use strict";r.d(t,{getBlockStyleClasses:()=>l,getLogoStyleClasses:()=>i});var n=r(97754),o=r(52292),a=r(78217),s=r.n(a);function l(e,t){return n(s().pair,s()[e],t)}function i(e,t=2,r=!0){return n(s().logo,s()[e],s().skeleton,o.skeletonTheme.wrapper,!r&&s().empty,1===t&&n(o.skeletonTheme.animated))}},58492:(e,t,r)=>{"use strict";r.d(t,{getStyleClasses:()=>n.getStyleClasses});var n=r(53885)},97006:(e,t,r)=>{"use strict";r.d(t,{createRegExpList:()=>l,getHighlightedChars:()=>i,rankedSearch:()=>s});var n=r(37265);function o(e){return e.replace(/[!-/[-^{-}?]/g,"\\$&")}var a;function s(e){const{data:t,rules:r,queryString:o,isPreventedFromFiltering:a,primaryKey:s,secondaryKey:l=s,optionalPrimaryKey:i,tertiaryKey:c}=e;return t.map((e=>{const t=i&&e[i]?e[i]:e[s],a=e[l],u=c&&e[c];let d,m=0;return r.forEach((e=>{const{re:r,fullMatch:s}=e;if(r.lastIndex=0,(0,n.isString)(t)&&t&&t.toLowerCase()===o.toLowerCase())return m=4,void(d=t.match(s)?.index);if((0,n.isString)(t)&&s.test(t))return m=3,void(d=t.match(s)?.index);if((0,n.isString)(a)&&s.test(a))return m=2,void(d=a.match(s)?.index);if((0,n.isString)(a)&&r.test(a))return m=2,void(d=a.match(r)?.index);if(Array.isArray(u))for(const e of u)if(s.test(e))return m=1,void(d=e.match(s)?.index)})),{matchPriority:m,matchIndex:d,item:e}})).filter((e=>a||e.matchPriority)).sort(((e,t)=>{if(e.matchPriority<t.matchPriority)return 1;if(e.matchPriority>t.matchPriority)return-1;if(e.matchPriority===t.matchPriority){if(void 0===e.matchIndex||void 0===t.matchIndex)return 0;if(e.matchIndex>t.matchIndex)return 1;if(e.matchIndex<t.matchIndex)return-1}return 0})).map((({item:e})=>e))}function l(e,t){const r=[],n=e.toLowerCase(),a=e.split("").map(((e,t)=>`(${0!==t?`[/\\s-]${o(e)}`:o(e)})`)).join("(.*?)")+"(.*)";return r.push({fullMatch:new RegExp(`(${o(e)})`,"i"),re:new RegExp(`^${a}`,"i"),
reserveRe:new RegExp(a,"i"),fuzzyHighlight:!0}),t&&t.hasOwnProperty(n)&&r.push({fullMatch:t[n],re:t[n],fuzzyHighlight:!1}),r}function i(e,t,r){const n=[];return e&&r?(r.forEach((e=>{const{fullMatch:r,re:o,reserveRe:a}=e;r.lastIndex=0,o.lastIndex=0;const s=r.exec(t),l=s||o.exec(t)||a&&a.exec(t);if(e.fuzzyHighlight=!s,l)if(e.fuzzyHighlight){let e=l.index;for(let t=1;t<l.length;t++){const r=l[t],o=l[t].length;if(t%2){const t=r.startsWith(" ")||r.startsWith("/")||r.startsWith("-");n[t?e+1:e]=!0}e+=o}}else for(let e=0;e<l[0].length;e++)n[l.index+e]=!0})),n):n}!function(e){e[e.Low=0]="Low",e[e.MediumLow=1]="MediumLow",e[e.Medium=2]="Medium",e[e.High=3]="High",e[e.Highest=4]="Highest"}(a||(a={}))},24637:(e,t,r)=>{"use strict";r.d(t,{HighlightedText:()=>l});var n=r(50959),o=r(97754),a=r(97006),s=r(95059);function l(e){const{queryString:t,rules:r,text:l,className:i}=e,c=(0,n.useMemo)((()=>(0,a.getHighlightedChars)(t,l,r)),[t,r,l]);return n.createElement(n.Fragment,null,c.length?l.split("").map(((e,t)=>n.createElement(n.Fragment,{key:t},c[t]?n.createElement("span",{className:o(s.highlighted,i)},e):n.createElement("span",null,e)))):l)}},78036:(e,t,r)=>{"use strict";r.d(t,{useEnsuredContext:()=>a});var n=r(50959),o=r(50151);function a(e){return(0,o.ensureNotNull)((0,n.useContext)(e))}},36947:(e,t,r)=>{"use strict";r.d(t,{useForceUpdate:()=>n.useForceUpdate});var n=r(125)},29006:(e,t,r)=>{"use strict";r.d(t,{useResizeObserver:()=>n.useResizeObserver});var n=r(67842)},77975:(e,t,r)=>{"use strict";r.d(t,{useWatchedValueReadonly:()=>o});var n=r(50959);const o=(e,t=!1,r=[])=>{const o="watchedValue"in e?e.watchedValue:void 0,a="defaultValue"in e?e.defaultValue:e.watchedValue.value(),[s,l]=(0,n.useState)(o?o.value():a);return(t?n.useLayoutEffect:n.useEffect)((()=>{if(o){l(o.value());const e=e=>l(e);return o.subscribe(e),()=>o.unsubscribe(e)}return()=>{}}),[o,...r]),s}},84877:(e,t,r)=>{"use strict";r.d(t,{MatchMediaMap:()=>s});var n=r(50959),o=r(66783),a=r.n(o);class s extends n.Component{constructor(e){super(e),this._handleMediaChange=()=>{const e=i(this.state.queries,((e,t)=>t.matches));let t=!1;for(const r in e)if(e.hasOwnProperty(r)&&this.state.matches[r]!==e[r]){t=!0;break}t&&this.setState({matches:e})};const{rules:t}=this.props;this.state=l(t)}shouldComponentUpdate(e,t){return!a()(e,this.props)||(!a()(t.rules,this.state.rules)||!a()(t.matches,this.state.matches))}componentDidMount(){this._migrate(null,this.state.queries)}componentDidUpdate(e,t){a()(e.rules,this.props.rules)||this._migrate(t.queries,this.state.queries)}componentWillUnmount(){this._migrate(this.state.queries,null)}render(){return this.props.children(this.state.matches)}static getDerivedStateFromProps(e,t){if(a()(e.rules,t.rules))return null;const{rules:r}=e;return l(r)}_migrate(e,t){null!==e&&i(e,((e,t)=>{t.removeEventListener("change",this._handleMediaChange)})),null!==t&&i(t,((e,t)=>{t.addEventListener("change",this._handleMediaChange)}))}}function l(e){const t=i(e,((e,t)=>window.matchMedia(t)));return{queries:t,matches:i(t,((e,t)=>t.matches)),rules:{...e}}}
function i(e,t){const r={};for(const n in e)e.hasOwnProperty(n)&&(r[n]=t(n,e[n]));return r}},47308:(e,t,r)=>{"use strict";r.d(t,{RoundButtonTabs:()=>$});var n=r(50959),o=r(97754),a=r(11542),s=r(63273),l=r(47201),i=r(35020),c=r(86240),u=r(86781);var d=r(95854),m=r(36966),p=r(7953),h=r(38528),g=r(66686);r(34869);const f=n.createContext({children:{},setIsReady:()=>{}});function v(){return!function(){const[e,t]=(0,n.useState)(!0);return(0,n.useEffect)((()=>{t(!1)}),[]),e}()}var b=r(67842);function y(e,t,r){const{id:o,items:a,activationType:s,orientation:y,disabled:S,onActivate:x,isActive:w,overflowBehaviour:k,enableActiveStateStyles:C,tablistLabelId:E,tablistLabel:I,preventDefaultIfKeyboardActionHandled:R,stopPropagationIfKeyboardActionHandled:L,keyboardNavigationLoop:T,defaultKeyboardFocus:B,focusableItemAttributes:N}=t,M=(0,i.useMobileTouchState)(),D=function(e){const t=(0,u.useSafeMatchMedia)(c["media-mf-phone-landscape"],!0),r=(0,i.useMobileTouchState)();return e??(r||!t?"scroll":"collapse")}(k),O=(0,n.useRef)(!1),A=(0,n.useCallback)((e=>e.id),[]),_=C??!M,P=function(){const{setIsReady:e,children:t}=(0,n.useContext)(f),r=(0,n.useRef)((0,n.useId)());return t[r.current]||(t[r.current]={isReady:!1}),(0,n.useCallback)((()=>{t[r.current].isReady=!0,e(Object.values(t).every((e=>e.isReady)))}),[t,e])}(),{visibleItems:F,hiddenItems:W,containerRefCallback:Q,innerContainerRefCallback:U,moreButtonRef:V,setItemRef:K,itemsMeasurements:z}=(0,d.useCollapsible)(a,A,w),H=function(e){const t=(0,n.useRef)(null);return(0,n.useEffect)((()=>{t.current=e}),[e]),t.current}(z.current?.containerWidth)??0,Z=v(),G=z.current?.containerWidth??0;let q=!1;z.current&&Z&&(q=function(e,t,r,n,o){if("collapse"!==n)return!0;const a=function(e,t,r){const n=e.filter((e=>t.find((t=>t.id===e[0]))));return t.length>0?n[0][1]+r:0}(Array.from(e.widthsMap.entries()),t,o),s=e.moreButtonWidth??0;let l=function(e,t){return e.reduce(((e,r)=>e+(t.get(r.id)??0)),0)}(r,e.widthsMap);return l+=t.length>0?s:0,function(e,t,r,n){return 0!==e?t-r<e&&t-r>n:r<t}(a,e.containerWidth,l,o)}(z.current,W,F,D,r.gap??0)||0===G);const j=(0,b.useResizeObserver)((([e])=>{const t=Z&&0===H&&0===W.length;(q&&e.contentRect.width===H||t)&&P()})),$="collapse"===D?F:a,Y=(0,n.useMemo)((()=>"collapse"===D?W:[]),[D,W]),X=(0,n.useCallback)((e=>Y.includes(e)),[Y]),{isOpened:J,open:ee,close:te,onButtonClick:re}=(0,p.useDisclosure)({id:o,disabled:S}),{tabsBindings:ne,tablistBinding:oe,scrollWrapBinding:ae,onActivate:se,onHighlight:le,isHighlighted:ie}=(0,m.useTabs)({id:o,items:[...$,...Y],activationType:s,orientation:y,disabled:S,tablistLabelId:E,tablistLabel:I,preventDefaultIfKeyboardActionHandled:R,scrollIntoViewOptions:r.scrollIntoViewOptions,onActivate:x,isActive:w,isCollapsed:X,isRtl:r.isRtl,isDisclosureOpened:J,isRadioGroup:r.isRadioGroup,stopPropagationIfKeyboardActionHandled:L,keyboardNavigationLoop:T,defaultKeyboardFocus:B,focusableItemAttributes:N}),ce=Y.find(ie),ue=(0,n.useCallback)((()=>{const e=a.find(w);e&&le(e)}),[le,w,a]),de=(0,n.useCallback)((e=>ne.find((t=>t.id===e.id))),[ne]),me=(0,
n.useCallback)((()=>{te(),ue(),O.current=!0}),[te,ue]),pe=(0,n.useCallback)((()=>{ce&&(se(ce),le(ce,250))}),[se,le,ce]);ae.ref=(0,h.useMergedRefs)([j,ae.ref,Q]),oe.ref=(0,h.useMergedRefs)([oe.ref,U]),oe.onKeyDown=(0,l.createSafeMulticastEventHandler)((0,g.useKeyboardEventHandler)([(0,g.useKeyboardClose)(J,me),(0,g.useKeyboardActionHandler)([13,32],pe,(0,n.useCallback)((()=>Boolean(ce)),[ce]))],R),oe.onKeyDown);const he=(0,n.useCallback)((e=>{O.current=!0,re(e)}),[O,re]),ge=(0,n.useCallback)((e=>{e&&se(e)}),[se]);return(0,n.useEffect)((()=>{O.current?O.current=!1:(ce&&!J&&ee(),!ce&&J&&te())}),[ce,J,ee,te]),{enableActiveStateStyles:_,moreButtonRef:V,setItemRef:K,getBindings:de,handleMoreButtonClick:he,handleCollapsedItemClick:ge,scrollWrapBinding:ae,overflowBehaviour:D,tablistBinding:oe,visibleTabs:$,hiddenTabs:Y,handleActivate:se,isMobileTouch:M,getItemId:A,isDisclosureOpened:J,isHighlighted:ie,closeDisclosure:te}}var S=r(8304),x=r(53017),w=r(17946),k=r(9745),C=r(2948),E=r(90854);const I="xsmall",R="primary";function L(e){const t=(0,n.useContext)(w.CustomBehaviourContext),{size:r="xsmall",variant:a="primary",active:s,fake:l,startIcon:i,endIcon:c,showCaret:u,iconOnly:d,anchor:m,enableActiveStateStyles:p=t.enableActiveStateStyles,disableFocusOutline:h=!1,tooltip:g}=e;return o(E.roundTabButton,E[r],E[a],i&&E.withStartIcon,(c||u)&&E.withEndIcon,d&&E.iconOnly,s&&E.selected,l&&E.fake,m&&E.enableCursorPointer,!p&&E.disableActiveStateStyles,h&&E.disableFocusOutline,g&&"apply-common-tooltip")}function T(e){const{startIcon:t,endIcon:r,showCaret:a,iconOnly:s,children:l}=e;return n.createElement(n.Fragment,null,t&&n.createElement(k.Icon,{icon:t,className:E.startIconWrap,"aria-hidden":!0}),l&&n.createElement("span",{className:o(E.content,s&&E.visuallyHidden)},l),(!s&&r||a)&&n.createElement(B,{icon:r,showCaret:a}))}function B(e){const{icon:t,showCaret:r}=e;return n.createElement(k.Icon,{className:o(E.endIconWrap,r&&E.caret),icon:r?C:t,"aria-hidden":!0})}const N=(0,n.forwardRef)(((e,t)=>{const{id:r,size:o,variant:a,active:s,fake:l,startIcon:i,endIcon:c,showCaret:u,iconOnly:d,children:m,enableActiveStateStyles:p,disableFocusOutline:h,tooltip:g,...f}=e;return n.createElement("button",{...f,id:r,ref:t,"data-tooltip":g,className:L({size:o,variant:a,active:s,fake:l,startIcon:i,endIcon:c,showCaret:u,iconOnly:d,enableActiveStateStyles:p,disableFocusOutline:h,tooltip:g})},n.createElement(T,{startIcon:i,endIcon:c,showCaret:u,iconOnly:d},m))}));N.displayName="RoundTabsBaseButton";const M=(0,n.createContext)({size:"small",variant:"primary",isHighlighted:!1,isCollapsed:!1,disabled:!1});function D(e){const{item:t,highlighted:r,handleItemRef:o,reference:a,onClick:s,"aria-disabled":l,...i}=e,c=(0,n.useCallback)((e=>{i.disabled&&e.preventDefault(),s&&s(t)}),[s,t,i.disabled]),u=(0,n.useCallback)((e=>{o&&o(t,e),(0,x.isomorphicRef)(a)(e)}),[t,o]),d={size:i.size??I,variant:i.variant??R,isHighlighted:Boolean(i.active),isCollapsed:!1,disabled:i.disabled??!1};return n.createElement(N,{...i,id:t.id,onClick:c,ref:u,startIcon:t.startIcon,endIcon:t.endIcon,
tooltip:t.tooltip,"aria-label":"radio"===i.role?t.children:void 0},n.createElement(M.Provider,{value:d},t.children))}var O=r(16396),A=r(4523),_=r(16829),P=r(89882),F=r(2057),W=r(93524);function Q(e){const{disabled:t,isOpened:r,enableActiveStateStyles:o,disableFocusOutline:a,fake:s,items:l,buttonText:i,buttonPreset:c="text",buttonRef:u,size:d,variant:m,isAnchorTabs:p,isHighlighted:g,onButtonClick:f,onItemClick:v,onClose:b}=e,y=(0,n.useRef)(null),S=(0,h.useMergedRefs)([u,y]),x="text"===c?void 0:"xsmall"===d?P:F;return n.createElement(A.PopupMenuDisclosureView,{buttonRef:y,listboxTabIndex:-1,isOpened:r,onClose:b,listboxAria:{"aria-hidden":!0},button:n.createElement(N,{"aria-hidden":!0,disabled:t,active:r,onClick:f,ref:S,tabIndex:-1,size:d,variant:m,startIcon:x,showCaret:"text"===c,iconOnly:"meatballs"===c,enableActiveStateStyles:o,disableFocusOutline:a,fake:s},i),popupChildren:n.createElement(n.Fragment,null,"meatballs"===c&&n.createElement(_.ToolWidgetMenuSummary,null,i),l.map((e=>n.createElement(O.PopupMenuItem,{key:e.id,className:p?W.linkItem:void 0,onClick:v,onClickArg:e,isActive:g(e),label:n.createElement(U,{isHighlighted:g(e),size:d,variant:m,disabled:e.disabled},e.children),isDisabled:e.disabled,link:"href"in e?e.href:void 0,rel:"rel"in e?e.rel:void 0,target:"target"in e?e.target:void 0,icon:e.startIcon,toolbox:e.endIcon&&n.createElement(k.Icon,{icon:e.endIcon}),renderComponent:"renderComponent"in e?e.renderComponent:void 0,dontClosePopup:!0}))))})}function U(e){const{isHighlighted:t,size:r,variant:o,children:a,disabled:s}=e,l={size:r??I,variant:o??R,isHighlighted:t,isCollapsed:!0,disabled:s??!1};return n.createElement(M.Provider,{value:l},a)}var V,K,z,H,Z=r(76912);function G(e){const{overflowBehaviour:t}=e;return o(Z.scrollWrap,"scroll"===t&&Z.overflowScroll,"wrap"===t&&Z.overflowWrap)}function q(e){const{align:t="start"}=e;return o(Z.roundTabs,Z[t])}function j(e){const{children:t,disabled:l,moreButtonText:i=a.t(null,void 0,r(37117)),moreButtonPreset:c,className:u,size:d,variant:m,align:p,style:h={},"data-name":g,isRadioGroup:f,"aria-controls":v}=e,b=function(e="xsmall"){switch(e){case"small":return 8;case"xsmall":return 4;default:return 16}}(d),{enableActiveStateStyles:x,moreButtonRef:w,setItemRef:k,getBindings:C,handleMoreButtonClick:E,handleCollapsedItemClick:I,scrollWrapBinding:R,overflowBehaviour:L,tablistBinding:T,visibleTabs:B,hiddenTabs:N,handleActivate:M,isMobileTouch:O,getItemId:A,isDisclosureOpened:_,isHighlighted:P,closeDisclosure:F}=y(S.TabNames.RoundButtonTabs,e,{isRtl:s.isRtl,scrollIntoViewOptions:{additionalScroll:b},isRadioGroup:f,gap:b});return n.createElement("div",{...R,className:o(G({overflowBehaviour:L}),u),style:{...h,"--ui-lib-round-tabs-gap":`${b}px`},"data-name":g},n.createElement("div",{...T,className:q({align:p,overflowBehaviour:L})},B.map((e=>n.createElement(D,{...C(e),key:e.id,item:e,onClick:()=>M(e),variant:m,size:d,enableActiveStateStyles:x,disableFocusOutline:O,reference:k(A(e)),...e.dataId&&{"data-id":e.dataId},"aria-controls":v}))),N.map((e=>n.createElement(D,{...C(e),key:e.id,
item:e,variant:m,size:d,reference:k(A(e)),"aria-controls":v,fake:!0}))),n.createElement(Q,{disabled:l,isOpened:_,items:N,buttonText:i,buttonPreset:c,buttonRef:w,isHighlighted:P,onButtonClick:E,onItemClick:I,onClose:F,variant:m,size:d,enableActiveStateStyles:x,disableFocusOutline:O,fake:0===N.length}),t))}function $(e){const{"data-name":t="round-tabs-buttons",...r}=e;return n.createElement(j,{...r,"data-name":t})}!function(e){e.Primary="primary",e.Ghost="ghost"}(V||(V={})),function(e){e.XSmall="xsmall",e.Small="small",e.Large="large"}(K||(K={})),function(e){e.Start="start",e.Center="center"}(z||(z={})),function(e){e.Text="text",e.Meatballs="meatballs"}(H||(H={}));r(21593)},63932:(e,t,r)=>{"use strict";r.d(t,{Spinner:()=>i});var n=r(50959),o=r(97754),a=r(58096),s=(r(15216),r(85862)),l=r.n(s);function i(e){const{ariaLabel:t,ariaLabelledby:r,className:s,style:i,size:c,id:u,disableSelfPositioning:d}=e;return n.createElement("div",{className:o(s,"tv-spinner","tv-spinner--shown",`tv-spinner--size_${a.spinnerSizeMap[c||a.DEFAULT_SIZE]}`,d&&l().disableSelfPositioning),style:i,role:"progressbar",id:u,"aria-label":t,"aria-labelledby":r})}},10381:(e,t,r)=>{"use strict";r.d(t,{ToolWidgetCaret:()=>i});var n=r(50959),o=r(97754),a=r(9745),s=r(49128),l=r(578);function i(e){const{dropped:t,className:r}=e;return n.createElement(a.Icon,{className:o(r,s.icon,{[s.dropped]:t}),icon:l})}},4237:(e,t,r)=>{"use strict";var n=r(32227);t.createRoot=n.createRoot,n.hydrateRoot},38576:e=>{e.exports={button:"button-GwQQdU8S",hover:"hover-GwQQdU8S",clicked:"clicked-GwQQdU8S",isInteractive:"isInteractive-GwQQdU8S",accessible:"accessible-GwQQdU8S",isGrouped:"isGrouped-GwQQdU8S",isActive:"isActive-GwQQdU8S",isOpened:"isOpened-GwQQdU8S",isDisabled:"isDisabled-GwQQdU8S",text:"text-GwQQdU8S",icon:"icon-GwQQdU8S",endIcon:"endIcon-GwQQdU8S"}},55973:e=>{e.exports={title:"title-u3QJgF_p"}},81348:(e,t,r)=>{"use strict";r.d(t,{DEFAULT_TOOL_WIDGET_BUTTON_THEME:()=>l,ToolWidgetButton:()=>i});var n=r(50959),o=r(97754),a=r(9745),s=r(38576);const l=s,i=n.forwardRef(((e,t)=>{const{tag:r="div",icon:l,endIcon:i,isActive:c,isOpened:u,isDisabled:d,isGrouped:m,isHovered:p,isClicked:h,onClick:g,text:f,textBeforeIcon:v,title:b,theme:y=s,className:S,forceInteractive:x,inactive:w,"data-name":k,"data-tooltip":C,...E}=e,I=o(S,y.button,(b||C)&&"apply-common-tooltip",{[y.isActive]:c,[y.isOpened]:u,[y.isInteractive]:(x||Boolean(g))&&!d&&!w,[y.isDisabled]:Boolean(d||w),[y.isGrouped]:m,[y.hover]:p,[y.clicked]:h}),R=l&&("string"==typeof l?n.createElement(a.Icon,{className:y.icon,icon:l}):n.cloneElement(l,{className:o(y.icon,l.props.className)}));return"button"===r?n.createElement("button",{...E,ref:t,type:"button",className:o(I,y.accessible),disabled:d&&!w,onClick:g,title:b,"data-name":k,"data-tooltip":C},v&&f&&n.createElement("div",{className:o("js-button-text",y.text)},f),R,!v&&f&&n.createElement("div",{className:o("js-button-text",y.text)},f)):n.createElement("div",{...E,ref:t,"data-role":"button",className:I,onClick:d?void 0:g,title:b,"data-name":k,"data-tooltip":C
},v&&f&&n.createElement("div",{className:o("js-button-text",y.text)},f),R,!v&&f&&n.createElement("div",{className:o("js-button-text",y.text)},f),i&&n.createElement(a.Icon,{icon:i,className:s.endIcon}))}))},16829:(e,t,r)=>{"use strict";r.d(t,{ToolWidgetMenuSummary:()=>s});var n=r(50959),o=r(97754),a=r(55973);function s(e){return n.createElement("div",{className:o(e.className,a.title)},e.children)}},74395:(e,t,r)=>{"use strict";r.d(t,{VISIBLE_TYPESPECS:()=>s,marketType:()=>l});var n=r(11542);const o=new Map([["cfd",n.t(null,void 0,r(79599))],["dr",n.t(null,void 0,r(47268))],["index",n.t(null,void 0,r(87464))],["forex",n.t(null,void 0,r(17770))],["right",n.t(null,{context:"symbol_type"},r(53174))],["bond",n.t(null,void 0,r(42358))],["bitcoin",n.t(null,void 0,r(46128))],["crypto",n.t(null,void 0,r(46128))],["economic",n.t(null,void 0,r(54094))],["indices",n.t(null,void 0,r(90250))],["futures",n.t(null,void 0,r(4723))],["stock",n.t(null,void 0,r(76752))],["commodity",n.t(null,void 0,r(70932))]]);r(21251);const a=new Map,s=new Set(["cfd","spreadbet","defi","yield","government","corporate","mutual","money","etf","unit","trust","reit","etn","convertible","closedend","crypto","oracle"]);function l(e,t=[],r=!0){const n=t.filter((e=>s.has(e))),l=`${e}_${n.sort().join("_")}`,i=a.get(l);if(void 0!==i)return i;const c=r?function(e){return o.get(e)||e}(e):e,u=Boolean(t.length)?[c,...n].join(" "):c;return a.set(l,u),u}},52019:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M13.35 5.35a.5.5 0 0 0-.7-.7L9 8.29 5.35 4.65a.5.5 0 1 0-.7.7L8.29 9l-3.64 3.65a.5.5 0 0 0 .7.7L9 9.71l3.65 3.64a.5.5 0 0 0 .7-.7L9.71 9l3.64-3.65z"/></svg>'},89882:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18"><path fill="currentColor" d="M5 9a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm6 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0zm4 2a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/></svg>'},2057:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" d="M9 14a3 3 0 1 1-6 0 3 3 0 0 1 6 0zm8 0a3 3 0 1 1-6 0 3 3 0 0 1 6 0zm5 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/></svg>'},95694:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M17 22.5 6.85 12.35a.5.5 0 0 1 0-.7L17 1.5"/></svg>'},49498:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M12 16.5 4.85 9.35a.5.5 0 0 1 0-.7L12 1.5"/></svg>'},60176:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M9.5 12.5 3.9 7.37a.5.5 0 0 1 0-.74L9.5 1.5"/></svg>'},35369:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M8 10.5 3.85 6.35a.5.5 0 0 1 0-.7L8 1.5"/></svg>'},58478:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.2" d="M7 8.5 3.85 5.35a.5.5 0 0 1 0-.7L7 1.5"/></svg>'},73063:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M17 22.5 6.85 12.35a.5.5 0 0 1 0-.7L17 1.5"/></svg>'},14127:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M12 16.5 4.85 9.35a.5.5 0 0 1 0-.7L12 1.5"/></svg>'},18073:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" width="14" height="14" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M9.5 12.5 3.9 7.37a.5.5 0 0 1 0-.74L9.5 1.5"/></svg>'},99243:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 12 12" width="12" height="12" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M8 10.5 3.85 6.35a.5.5 0 0 1 0-.7L8 1.5"/></svg>'},42576:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 10 10" width="10" height="10" fill="none"><path stroke="currentColor" stroke-linecap="round" stroke-width="1.5" d="M7 8.5 3.85 5.35a.5.5 0 0 1 0-.7L7 1.5"/></svg>'},578:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 8" width="16" height="8"><path fill="currentColor" d="M0 1.475l7.396 6.04.596.485.593-.49L16 1.39 14.807 0 7.393 6.122 8.58 6.12 1.186.08z"/></svg>'},91540:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 18 18" width="18" height="18" fill="none"><path stroke="currentColor" d="M2.5 14.5c1.68-1.26 3.7-2 6.5-2s4.91.74 6.5 2m-13-11c1.68 1.26 3.7 2 6.5 2s4.91-.74 6.5-2"/><circle stroke="currentColor" cx="9" cy="9" r="8.5"/><path stroke="currentColor" d="M13.5 9c0 2.42-.55 4.58-1.4 6.12-.87 1.56-1.98 2.38-3.1 2.38s-2.23-.82-3.1-2.38c-.85-1.54-1.4-3.7-1.4-6.12s.55-4.58 1.4-6.12C6.77 1.32 7.88.5 9 .5s2.23.82 3.1 2.38c.85 1.54 1.4 3.7 1.4 6.12z"/></svg>'},66619:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="#B2B5BE" fill-rule="evenodd" d="M23 39a36 36 0 0 1 72 0v13.15l15.1 8.44 2.16 1.2-1.64 1.86-12.85 14.59 3.73 4.03L98.57 85 95 81.13V117H77v-12H67v9H50V95H40v22H23V81.28l-3.8 3.61-2.76-2.9 4.05-3.84-12.77-14.5-1.64-1.86 2.16-1.2L23 52.34V39Zm72 36.33 10.98-12.46L95 56.73v18.6ZM23 56.92v18.03L12.35 62.87 23 56.92ZM59 7a32 32 0 0 0-32 32v74h9V91h18v19h9v-9h18v12h10V39A32 32 0 0 0 59 7Zm-7 36a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm19 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>'},67562:e=>{
e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120" width="120" height="120"><path fill="#131722" fill-rule="evenodd" d="M23 39a36 36 0 0 1 72 0v13.15l15.1 8.44 2.16 1.2-1.64 1.86-12.85 14.59 3.73 4.03L98.57 85 95 81.13V117H77v-12H67v9H50V95H40v22H23V81.28l-3.8 3.61-2.76-2.9 4.05-3.84-12.77-14.5-1.64-1.86 2.16-1.2L23 52.34V39Zm72 36.33 10.98-12.46L95 56.73v18.6ZM23 56.92v18.03L12.35 62.87 23 56.92ZM59 7a32 32 0 0 0-32 32v74h9V91h18v19h9v-9h18v12h10V39A32 32 0 0 0 59 7Zm-7 36a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm19 3a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>'},69533:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="none"><path stroke="currentColor" d="M8 5l3.5 3.5L8 12"/></svg>'},486:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h9"/><circle fill="currentColor" cx="7" cy="3" r="1"/><circle fill="currentColor" cx="7" cy="10" r="1"/></svg>'},63861:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><g fill="none" fill-rule="evenodd" stroke="currentColor"><path stroke-linecap="square" stroke-linejoin="round" d="M3.5 10V2.5L1 5"/><path stroke-linecap="square" d="M1.5 10.5h4"/><path d="M8 12l3-11"/></g></svg>'},81574:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h8"/></svg>'},32617:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M3 10l7-7M3 3l7 7"/></svg>'},35119:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M2.5 6.5h8m-4-4v8"/></svg>'},69135:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 13 13" width="13" height="13"><path fill="none" stroke="currentColor" stroke-linecap="square" d="M3 7l3.5-3.5L10 7"/></svg>'},54313:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28"><path fill="currentColor" fill-rule="evenodd" d="M18.5 12.5a6 6 0 1 1-12 0 6 6 0 0 1 12 0Zm-1.25 5.8a7.5 7.5 0 1 1 1.06-1.06l4.22 4.23.53.53L22 23.06l-.53-.53-4.22-4.22Z"/></svg>'},6347:e=>{e.exports='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 28 28" width="28" height="28" fill="none"><path stroke="currentColor" d="M17.4 17.5a7 7 0 1 0-4.9 2c1.9 0 3.64-.76 4.9-2zm0 0l5.1 5"/></svg>'}}]);