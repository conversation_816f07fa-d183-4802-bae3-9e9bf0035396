(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[7746],{43705:(e,t,r)=>{"use strict";r.d(t,{$:()=>Q,A:()=>j,B:()=>I,C:()=>H,D:()=>y,E:()=>b,F:()=>F,G:()=>Y,H:()=>u,I:()=>R,J:()=>$,L:()=>V,M:()=>te,N:()=>re,O:()=>ee,P:()=>_,Q:()=>ie,R:()=>k,S:()=>q,T:()=>L,U:()=>oe,V:()=>M,Z:()=>J,a:()=>U,a4:()=>ne,b:()=>B,d:()=>K,e:()=>A,f:()=>G,g:()=>W,h:()=>z,i:()=>m,j:()=>D,k:()=>a,l:()=>T,m:()=>S,n:()=>x,o:()=>h,p:()=>l,q:()=>v,r:()=>f,s:()=>w,t:()=>c,u:()=>d,v:()=>C,y:()=>E,z:()=>O});var n=r(45807),o=r(15882),i=r(17443);const s=function(e,t){var r;void 0===t&&(t=!0);var o=new Promise((function(n){r=setTimeout(n,Math.min(2147483647,e),t)}));return o[n.CANCEL]=function(){clearTimeout(r)},o};var a=function(e){return function(){return e}}(!0),c=function(){};var u=function(e){return e};"function"==typeof Symbol&&Symbol.asyncIterator&&Symbol.asyncIterator;var l=function(e,t){(0,o.default)(e,t),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(t).forEach((function(r){e[r]=t[r]}))},d=function(e,t){var r;return(r=[]).concat.apply(r,t.map(e))};function f(e,t){var r=e.indexOf(t);r>=0&&e.splice(r,1)}function h(e){var t=!1;return function(){t||(t=!0,e())}}var g=function(e){throw e},p=function(e){return{value:e,done:!0}};function v(e,t,r){void 0===t&&(t=g),void 0===r&&(r="iterator");var n={meta:{name:r},next:e,throw:t,return:p,isSagaIterator:!0};return"undefined"!=typeof Symbol&&(n[Symbol.iterator]=function(){return n}),n}function y(e,t){var r=t.sagaStack;console.error(e),console.error(r)}var m=function(e){return new Error("\n  redux-saga: Error checking hooks detected an inconsistent state. This is likely a bug\n  in redux-saga code and not yours. Thanks for reporting this in the project's github repo.\n  Error: "+e+"\n")},S=function(e){return Array.apply(null,new Array(e))},b=function(e){return function(t){return e(Object.defineProperty(t,n.SAGA_ACTION,{value:!0}))}},O=function(e){return e===n.TERMINATE},E=function(e){return e===n.TASK_CANCEL},w=function(e){return O(e)||E(e)};function T(e,t){var r=Object.keys(e),n=r.length;var o,s=0,a=(0,i.array)(e)?S(n):{},u={};return r.forEach((function(e){var r=function(r,i){o||(i||w(r)?(t.cancel(),t(r,i)):(a[e]=r,++s===n&&(o=!0,t(a))))};r.cancel=c,u[e]=r})),t.cancel=function(){o||(o=!0,r.forEach((function(e){return u[e].cancel()})))},u}function D(e){return{name:e.name||"anonymous",location:C(e)}}function C(e){return e[n.SAGA_LOCATION]}function I(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}var N={isEmpty:a,put:c,take:c};function P(e,t){void 0===e&&(e=10);var r=new Array(e),n=0,o=0,i=0,s=function(t){r[o]=t,o=(o+1)%e,n++},a=function(){if(0!=n){var t=r[i];return r[i]=null,n--,i=(i+1)%e,t}},c=function(){for(var e=[];n;)e.push(a());return e};return{isEmpty:function(){return 0==n},put:function(a){var u;if(n<e)s(a);else switch(t){case 1:throw new Error("Channel's Buffer overflow!");case 3:r[o]=a,i=o=(o+1)%e
;break;case 4:u=2*e,r=c(),n=r.length,o=r.length,i=0,r.length=u,e=u,s(a)}},take:a,flush:c}}var x=function(){return N},M=function(e){return P(e,3)},A=function(e){return P(e,4)},R=Object.freeze({__proto__:null,none:x,fixed:function(e){return P(e,1)},dropping:function(e){return P(e,2)},sliding:M,expanding:A}),L="TAKE",_="PUT",j="ALL",k="RACE",H="CALL",U="CPS",F="FORK",$="JOIN",B="CANCEL",q="SELECT",K="ACTION_CHANNEL",G="CANCELLED",W="FLUSH",Y="GET_CONTEXT",z="SET_CONTEXT",X=function(e,t){var r;return(r={})[n.IO]=!0,r.combinator=!1,r.type=e,r.payload=t,r};function V(e,t){return void 0===e&&(e="*"),(0,i.pattern)(e)?((0,i.notUndef)(t)&&console.warn("take(pattern) takes one argument but two were provided. Consider passing an array for listening to several action types"),X(L,{pattern:e})):(0,i.multicast)(e)&&(0,i.notUndef)(t)&&(0,i.pattern)(t)?X(L,{channel:e,pattern:t}):(0,i.channel)(e)?((0,i.notUndef)(t)&&console.warn("take(channel) takes one argument but two were provided. Second argument is ignored."),X(L,{channel:e})):void 0}function J(e,t){return(0,i.undef)(t)&&(t=e,e=void 0),X(_,{channel:e,action:t})}function Q(e){var t=X(j,e);return t.combinator=!0,t}function Z(e,t){var r,n=null;return(0,i.func)(e)?r=e:((0,i.array)(e)?(n=e[0],r=e[1]):(n=e.context,r=e.fn),n&&(0,i.string)(r)&&(0,i.func)(n[r])&&(r=n[r])),{context:n,fn:r,args:t}}function ee(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return X(H,Z(e,r))}function te(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return X(F,Z(e,r))}function re(e){return void 0===e&&(e=n.SELF_CANCELLATION),X(B,e)}function ne(e){void 0===e&&(e=u);for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return X(q,{selector:e,args:r})}function oe(e,t){return X(K,{pattern:e,buffer:t})}var ie=ee.bind(null,s)},17443:(e,t,r)=>{"use strict";r.d(t,{array:()=>c,channel:()=>f,func:()=>s,iterator:()=>l,multicast:()=>p,notUndef:()=>i,pattern:()=>d,promise:()=>u,string:()=>a,stringableFunc:()=>h,symbol:()=>g,undef:()=>o});var n=r(45807),o=function(e){return null==e},i=function(e){return null!=e},s=function(e){return"function"==typeof e},a=function(e){return"string"==typeof e},c=Array.isArray,u=function(e){return e&&s(e.then)},l=function(e){return e&&s(e.next)&&s(e.throw)},d=function e(t){return t&&(a(t)||g(t)||s(t)||c(t)&&t.every(e))},f=function(e){return e&&s(e.take)&&s(e.close)},h=function(e){return s(e)&&e.hasOwnProperty("toString")},g=function(e){return Boolean(e)&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype},p=function(e){return f(e)&&e[n.MULTICAST]}},45807:(e,t,r)=>{"use strict";r.d(t,{CANCEL:()=>o,CHANNEL_END_TYPE:()=>i,IO:()=>s,MATCH:()=>a,MULTICAST:()=>c,SAGA_ACTION:()=>u,SAGA_LOCATION:()=>g,SELF_CANCELLATION:()=>l,TASK:()=>d,TASK_CANCEL:()=>f,TERMINATE:()=>h});var n=function(e){return"@@redux-saga/"+e
},o=n("CANCEL_PROMISE"),i=n("CHANNEL_END"),s=n("IO"),a=n("MATCH"),c=n("MULTICAST"),u=n("SAGA_ACTION"),l=n("SELF_CANCELLATION"),d=n("TASK"),f=n("TASK_CANCEL"),h=n("TERMINATE"),g=n("LOCATION")},59142:function(e,t){var r,n,o;n=[t],r=function(e){"use strict";function t(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)}Object.defineProperty(e,"__esModule",{value:!0});var r=!1;if("undefined"!=typeof window){var n={get passive(){r=!0}};window.addEventListener("testPassive",null,n),window.removeEventListener("testPassive",null,n)}var o="undefined"!=typeof window&&window.navigator&&window.navigator.platform&&/iP(ad|hone|od)/.test(window.navigator.platform),i=[],s=!1,a=-1,c=void 0,u=void 0,l=function(e){return i.some((function(t){return!(!t.options.allowTouchMove||!t.options.allowTouchMove(e))}))},d=function(e){var t=e||window.event;return!!l(t.target)||1<t.touches.length||(t.preventDefault&&t.preventDefault(),!1)},f=function(){setTimeout((function(){void 0!==u&&(document.body.style.paddingRight=u,u=void 0),void 0!==c&&(document.body.style.overflow=c,c=void 0)}))};e.disableBodyScroll=function(e,n){if(o){if(!e)return void console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");if(e&&!i.some((function(t){return t.targetElement===e}))){var f={targetElement:e,options:n||{}};i=[].concat(t(i),[f]),e.ontouchstart=function(e){1===e.targetTouches.length&&(a=e.targetTouches[0].clientY)},e.ontouchmove=function(t){var r,n,o,i;1===t.targetTouches.length&&(n=e,i=(r=t).targetTouches[0].clientY-a,!l(r.target)&&(n&&0===n.scrollTop&&0<i||(o=n)&&o.scrollHeight-o.scrollTop<=o.clientHeight&&i<0?d(r):r.stopPropagation()))},s||(document.addEventListener("touchmove",d,r?{passive:!1}:void 0),s=!0)}}else{g=n,setTimeout((function(){if(void 0===u){var e=!!g&&!0===g.reserveScrollBarGap,t=window.innerWidth-document.documentElement.clientWidth;e&&0<t&&(u=document.body.style.paddingRight,document.body.style.paddingRight=t+"px")}void 0===c&&(c=document.body.style.overflow,document.body.style.overflow="hidden")}));var h={targetElement:e,options:n||{}};i=[].concat(t(i),[h])}var g},e.clearAllBodyScrollLocks=function(){o?(i.forEach((function(e){e.targetElement.ontouchstart=null,e.targetElement.ontouchmove=null})),s&&(document.removeEventListener("touchmove",d,r?{passive:!1}:void 0),s=!1),i=[],a=-1):(f(),i=[])},e.enableBodyScroll=function(e){if(o){if(!e)return void console.error("enableBodyScroll unsuccessful - targetElement must be provided when calling enableBodyScroll on IOS devices.");e.ontouchstart=null,e.ontouchmove=null,i=i.filter((function(t){return t.targetElement!==e})),s&&0===i.length&&(document.removeEventListener("touchmove",d,r?{passive:!1}:void 0),s=!1)}else 1===i.length&&i[0].targetElement===e?(f(),i=[]):i=i.filter((function(t){return t.targetElement!==e}))}},void 0===(o="function"==typeof r?r.apply(t,n):r)||(e.exports=o)},3341:e=>{"use strict";e.exports=function e(t,r){if(t===r)return!0
;if(t&&r&&"object"==typeof t&&"object"==typeof r){if(t.constructor!==r.constructor)return!1;var n,o,i;if(Array.isArray(t)){if((n=t.length)!=r.length)return!1;for(o=n;0!=o--;)if(!e(t[o],r[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===r.source&&t.flags===r.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===r.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===r.toString();if((n=(i=Object.keys(t)).length)!==Object.keys(r).length)return!1;for(o=n;0!=o--;)if(!Object.prototype.hasOwnProperty.call(r,i[o]))return!1;for(o=n;0!=o--;){var s=i[o];if(!e(t[s],r[s]))return!1}return!0}return t!=t&&r!=r}},72535:(e,t,r)=>{"use strict";var n=r(56237),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function c(e){return n.isMemo(e)?s:a[e.$$typeof]||o}a[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[n.Memo]=s;var u=Object.defineProperty,l=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,h=Object.getPrototypeOf,g=Object.prototype;e.exports=function e(t,r,n){if("string"!=typeof r){if(g){var o=h(r);o&&o!==g&&e(t,o,n)}var s=l(r);d&&(s=s.concat(d(r)));for(var a=c(t),p=c(r),v=0;v<s.length;++v){var y=s[v];if(!(i[y]||n&&n[y]||p&&p[y]||a&&a[y])){var m=f(r,y);try{u(t,y,m)}catch(e){}}}}return t}},4237:(e,t,r)=>{"use strict";var n=r(32227);t.createRoot=n.createRoot,n.hydrateRoot},60198:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,o=r?Symbol.for("react.portal"):60106,i=r?Symbol.for("react.fragment"):60107,s=r?Symbol.for("react.strict_mode"):60108,a=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,l=r?Symbol.for("react.async_mode"):60111,d=r?Symbol.for("react.concurrent_mode"):60111,f=r?Symbol.for("react.forward_ref"):60112,h=r?Symbol.for("react.suspense"):60113,g=r?Symbol.for("react.suspense_list"):60120,p=r?Symbol.for("react.memo"):60115,v=r?Symbol.for("react.lazy"):60116,y=r?Symbol.for("react.block"):60121,m=r?Symbol.for("react.fundamental"):60117,S=r?Symbol.for("react.responder"):60118,b=r?Symbol.for("react.scope"):60119;function O(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case l:case d:case i:case a:case s:case h:return e;default:switch(e=e&&e.$$typeof){case u:case f:case v:case p:case c:return e;default:return t}}case o:return t}}}function E(e){return O(e)===d}t.AsyncMode=l,t.ConcurrentMode=d,t.ContextConsumer=u,t.ContextProvider=c,t.Element=n,t.ForwardRef=f,t.Fragment=i,t.Lazy=v,t.Memo=p,t.Portal=o,t.Profiler=a,t.StrictMode=s,t.Suspense=h,t.isAsyncMode=function(e){return E(e)||O(e)===l},t.isConcurrentMode=E,t.isContextConsumer=function(e){
return O(e)===u},t.isContextProvider=function(e){return O(e)===c},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},t.isForwardRef=function(e){return O(e)===f},t.isFragment=function(e){return O(e)===i},t.isLazy=function(e){return O(e)===v},t.isMemo=function(e){return O(e)===p},t.isPortal=function(e){return O(e)===o},t.isProfiler=function(e){return O(e)===a},t.isStrictMode=function(e){return O(e)===s},t.isSuspense=function(e){return O(e)===h},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===i||e===d||e===a||e===s||e===h||e===g||"object"==typeof e&&null!==e&&(e.$$typeof===v||e.$$typeof===p||e.$$typeof===c||e.$$typeof===u||e.$$typeof===f||e.$$typeof===m||e.$$typeof===S||e.$$typeof===b||e.$$typeof===y)},t.typeOf=O},56237:(e,t,r)=>{"use strict";e.exports=r(60198)},11195:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),u=Symbol.for("react.context"),l=Symbol.for("react.server_context"),d=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),g=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),v=Symbol.for("react.offscreen");function y(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case i:case a:case s:case f:case h:return e;default:switch(e=e&&e.$$typeof){case l:case u:case d:case p:case g:case c:return e;default:return t}}case o:return t}}}r=Symbol.for("react.module.reference"),t.isContextConsumer=function(e){return y(e)===u}},61357:(e,t,r)=>{"use strict";e.exports=r(11195)},43766:(e,t,r)=>{"use strict";r.d(t,{Provider:()=>L,connect:()=>R});var n=r(83363),o=r(34730),i=r(32227);let s=function(e){e()};const a=()=>s;var c=r(50959);const u=(0,c.createContext)(null);let l=null;var d=r(15882),f=r(30950),h=r(72535),g=r.n(h),p=r(61357);const v=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function y(e,t,r,n,{areStatesEqual:o,areOwnPropsEqual:i,areStatePropsEqual:s}){let a,c,u,l,d,f=!1;function h(f,h){const g=!i(h,c),p=!o(f,a,h,c);return a=f,c=h,g&&p?(u=e(a,c),t.dependsOnOwnProps&&(l=t(n,c)),d=r(u,l,c),d):g?(e.dependsOnOwnProps&&(u=e(a,c)),t.dependsOnOwnProps&&(l=t(n,c)),d=r(u,l,c),d):p?function(){const t=e(a,c),n=!s(t,u);return u=t,n&&(d=r(u,l,c)),d}():d}return function(o,i){return f?h(o,i):(a=o,c=i,u=e(a,c),l=t(n,c),d=r(u,l,c),f=!0,d)}}function m(e){return function(t){const r=e(t);function n(){return r}return n.dependsOnOwnProps=!1,n}}function S(e){return e.dependsOnOwnProps?Boolean(e.dependsOnOwnProps):1!==e.length}function b(e,t){return function(t,{displayName:r}){const n=function(e,t){return n.dependsOnOwnProps?n.mapToProps(e,t):n.mapToProps(e,void 0)};return n.dependsOnOwnProps=!0,n.mapToProps=function(t,r){n.mapToProps=e,n.dependsOnOwnProps=S(e);let o=n(t,r);return"function"==typeof o&&(n.mapToProps=o,n.dependsOnOwnProps=S(o),o=n(t,r)),o},n}}function O(e,t){return(r,n)=>{
throw new Error(`Invalid value of type ${typeof e} for ${t} argument when connecting component ${n.wrappedComponentName}.`)}}function E(e,t,r){return(0,d.default)({},r,e,t)}const w={notify(){},get:()=>[]};function T(e,t){let r,n=w;function o(){s.onStateChange&&s.onStateChange()}function i(){r||(r=t?t.addNestedSub(o):e.subscribe(o),n=function(){const e=a();let t=null,r=null;return{clear(){t=null,r=null},notify(){e((()=>{let e=t;for(;e;)e.callback(),e=e.next}))},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let n=!0,o=r={callback:e,next:null,prev:r};return o.prev?o.prev.next=o:t=o,function(){n&&null!==t&&(n=!1,o.next?o.next.prev=o.prev:r=o.prev,o.prev?o.prev.next=o.next:t=o.next)}}}}())}const s={addNestedSub:function(e){return i(),n.subscribe(e)},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return Boolean(r)},trySubscribe:i,tryUnsubscribe:function(){r&&(r(),r=void 0,n.clear(),n=w)},getListeners:()=>n};return s}const D=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement)?c.useLayoutEffect:c.useEffect;function C(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}function I(e,t){if(C(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;const r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(let n=0;n<r.length;n++)if(!Object.prototype.hasOwnProperty.call(t,r[n])||!C(e[r[n]],t[r[n]]))return!1;return!0}const N=["reactReduxForwardedRef"];let P=()=>{throw new Error("uSES not initialized!")};const x=[null,null];function M(e,t,r,n,o,i){e.current=n,r.current=!1,o.current&&(o.current=null,i())}function A(e,t){return e===t}const R=function(e,t,r,{pure:n,areStatesEqual:o=A,areOwnPropsEqual:i=I,areStatePropsEqual:s=I,areMergedPropsEqual:a=I,forwardRef:l=!1,context:h=u}={}){const S=h,w=function(e){return e?"function"==typeof e?b(e):O(e,"mapStateToProps"):m((()=>({})))}(e),C=function(e){return e&&"object"==typeof e?m((t=>function(e,t){const r={};for(const n in e){const o=e[n];"function"==typeof o&&(r[n]=(...e)=>t(o(...e)))}return r}(e,t))):e?"function"==typeof e?b(e):O(e,"mapDispatchToProps"):m((e=>({dispatch:e})))}(t),R=function(e){return e?"function"==typeof e?function(e){return function(t,{displayName:r,areMergedPropsEqual:n}){let o,i=!1;return function(t,r,s){const a=e(t,r,s);return i?n(a,o)||(o=a):(i=!0,o=a),o}}}(e):O(e,"mergeProps"):()=>E}(r),L=Boolean(e);return e=>{const t=e.displayName||e.name||"Component",r=`Connect(${t})`,n={shouldHandleStateChanges:L,displayName:r,wrappedComponentName:t,WrappedComponent:e,initMapStateToProps:w,initMapDispatchToProps:C,initMergeProps:R,areStatesEqual:o,areStatePropsEqual:s,areOwnPropsEqual:i,areMergedPropsEqual:a};function u(t){const[r,o,i]=(0,c.useMemo)((()=>{const{reactReduxForwardedRef:e}=t,r=(0,f.default)(t,N);return[t.context,e,r]}),[t]),s=(0,c.useMemo)((()=>r&&r.Consumer&&(0,p.isContextConsumer)(c.createElement(r.Consumer,null))?r:S),[r,S]),a=(0,
c.useContext)(s),u=Boolean(t.store)&&Boolean(t.store.getState)&&Boolean(t.store.dispatch),l=Boolean(a)&&Boolean(a.store);const h=u?t.store:a.store,g=l?a.getServerState:h.getState,m=(0,c.useMemo)((()=>function(e,t){let{initMapStateToProps:r,initMapDispatchToProps:n,initMergeProps:o}=t,i=(0,f.default)(t,v);return y(r(e,i),n(e,i),o(e,i),e,i)}(h.dispatch,n)),[h]),[b,O]=(0,c.useMemo)((()=>{if(!L)return x;const e=T(h,u?void 0:a.subscription),t=e.notifyNestedSubs.bind(e);return[e,t]}),[h,u,a]),E=(0,c.useMemo)((()=>u?a:(0,d.default)({},a,{subscription:b})),[u,a,b]),w=(0,c.useRef)(),C=(0,c.useRef)(i),I=(0,c.useRef)(),A=(0,c.useRef)(!1),R=((0,c.useRef)(!1),(0,c.useRef)(!1)),_=(0,c.useRef)();D((()=>(R.current=!0,()=>{R.current=!1})),[]);const j=(0,c.useMemo)((()=>()=>I.current&&i===C.current?I.current:m(h.getState(),i)),[h,i]),k=(0,c.useMemo)((()=>e=>b?function(e,t,r,n,o,i,s,a,c,u,l){if(!e)return()=>{};let d=!1,f=null;const h=()=>{if(d||!a.current)return;const e=t.getState();let r,h;try{r=n(e,o.current)}catch(e){h=e,f=e}h||(f=null),r===i.current?s.current||u():(i.current=r,c.current=r,s.current=!0,l())};return r.onStateChange=h,r.trySubscribe(),h(),()=>{if(d=!0,r.tryUnsubscribe(),r.onStateChange=null,f)throw f}}(L,h,b,m,C,w,A,R,I,O,e):()=>{}),[b]);var H,U,F;let $;H=M,U=[C,w,A,i,I,O],D((()=>H(...U)),F);try{$=P(k,j,g?()=>m(g(),i):j)}catch(e){throw _.current&&(e.message+=`\nThe error may be correlated with this previous error:\n${_.current.stack}\n\n`),e}D((()=>{_.current=void 0,I.current=void 0,w.current=$}));const B=(0,c.useMemo)((()=>c.createElement(e,(0,d.default)({},$,{ref:o}))),[o,e,$]);return(0,c.useMemo)((()=>L?c.createElement(s.Provider,{value:E},B):B),[s,B,E])}const h=c.memo(u);if(h.WrappedComponent=e,h.displayName=u.displayName=r,l){const t=c.forwardRef((function(e,t){return c.createElement(h,(0,d.default)({},e,{reactReduxForwardedRef:t}))}));return t.displayName=r,t.WrappedComponent=e,g()(t,e)}return g()(h,e)}};const L=function({store:e,context:t,children:r,serverState:n}){const o=(0,c.useMemo)((()=>{const t=T(e);return{store:e,subscription:t,getServerState:n?()=>n:void 0}}),[e,n]),i=(0,c.useMemo)((()=>e.getState()),[e]);D((()=>{const{subscription:t}=o;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),i!==e.getState()&&t.notifyNestedSubs(),()=>{t.tryUnsubscribe(),t.onStateChange=void 0}}),[o,i]);const s=t||u;return c.createElement(s.Provider,{value:o},r)};var _,j;_=o.useSyncExternalStoreWithSelector,l=_,(e=>{P=e})(n.useSyncExternalStore),j=i.unstable_batchedUpdates,s=j},3354:(e,t,r)=>{"use strict";var n=r(50959),o=Symbol.for("react.element"),i=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,a=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,r){var n,i={},u=null,l=null;for(n in void 0!==r&&(u=""+r),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(l=t.ref),t)s.call(t,n)&&!c.hasOwnProperty(n)&&(i[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===i[n]&&(i[n]=t[n]);return{$$typeof:o,type:e,key:u,ref:l,props:i,
_owner:a.current}}t.jsx=u},11527:(e,t,r)=>{"use strict";e.exports=r(3354)},46212:(e,t,r)=>{"use strict";r.d(t,{buffers:()=>a.I,default:()=>z,eventChannel:()=>I});var n=r(45807),o=r(15882),i=r(30950),s=r(17443),a=r(43705);function c(){var e={};return e.promise=new Promise((function(t,r){e.resolve=t,e.reject=r})),e}const u=c;var l=[],d=0;function f(e){try{p(),e()}finally{v()}}function h(e){l.push(e),d||(p(),y())}function g(e){try{return p(),e()}finally{y()}}function p(){d++}function v(){d--}function y(){var e;for(v();!d&&void 0!==(e=l.shift());)f(e)}var m=function(e){return function(t){return e.some((function(e){return w(e)(t)}))}},S=function(e){return function(t){return e(t)}},b=function(e){return function(t){return t.type===String(e)}},O=function(e){return function(t){return t.type===e}},E=function(){return a.k};function w(e){var t="*"===e?E:(0,s.string)(e)?b:(0,s.array)(e)?m:(0,s.stringableFunc)(e)?b:(0,s.func)(e)?S:(0,s.symbol)(e)?O:null;if(null===t)throw new Error("invalid pattern: "+e);return t(e)}var T={type:n.CHANNEL_END_TYPE},D=function(e){return e&&e.type===n.CHANNEL_END_TYPE};function C(e){void 0===e&&(e=(0,a.e)());var t=!1,r=[];return{take:function(n){t&&e.isEmpty()?n(T):e.isEmpty()?(r.push(n),n.cancel=function(){(0,a.r)(r,n)}):n(e.take())},put:function(n){if(!t){if(0===r.length)return e.put(n);r.shift()(n)}},flush:function(r){t&&e.isEmpty()?r(T):r(e.flush())},close:function(){if(!t){t=!0;var e=r;r=[];for(var n=0,o=e.length;n<o;n++){(0,e[n])(T)}}}}}function I(e,t){void 0===t&&(t=(0,a.n)());var r,n=!1,o=C(t),i=function(){n||(n=!0,(0,s.func)(r)&&r(),o.close())};return r=e((function(e){D(e)?i():o.put(e)})),r=(0,a.o)(r),n&&r(),{take:o.take,flush:o.flush,close:i}}function N(){var e,t,r,o,i,s,c=(t=!1,o=r=[],i=function(){o===r&&(o=r.slice())},s=function(){t=!0;var e=r=o;o=[],e.forEach((function(e){e(T)}))},(e={})[n.MULTICAST]=!0,e.put=function(e){if(!t)if(D(e))s();else for(var i=r=o,a=0,c=i.length;a<c;a++){var u=i[a];u[n.MATCH](e)&&(u.cancel(),u(e))}},e.take=function(e,r){void 0===r&&(r=E),t?e(T):(e[n.MATCH]=r,i(),o.push(e),e.cancel=(0,a.o)((function(){i(),(0,a.r)(o,e)})))},e.close=s,e),u=c.put;return c.put=function(e){e[n.SAGA_ACTION]?u(e):h((function(){u(e)}))},c}function P(e,t){var r=e[n.CANCEL];(0,s.func)(r)&&(t.cancel=r),e.then(t,(function(e){t(e,!0)}))}var x,M=0,A=function(){return++M};function R(e){e.isRunning()&&e.cancel()}var L=((x={})[a.T]=function(e,t,r){var o=t.channel,i=void 0===o?e.channel:o,a=t.pattern,c=t.maybe,u=function(e){e instanceof Error?r(e,!0):!D(e)||c?r(e):r(n.TERMINATE)};try{i.take(u,(0,s.notUndef)(a)?w(a):null)}catch(e){return void r(e,!0)}r.cancel=u.cancel},x[a.P]=function(e,t,r){var n=t.channel,o=t.action,i=t.resolve;h((function(){var t;try{t=(n?n.put:e.dispatch)(o)}catch(e){return void r(e,!0)}i&&(0,s.promise)(t)?P(t,r):r(t)}))},x[a.A]=function(e,t,r,n){var o=n.digestEffect,i=M,c=Object.keys(t);if(0!==c.length){var u=(0,a.l)(t,r);c.forEach((function(e){o(t[e],i,u[e],e)}))}else r((0,s.array)(t)?[]:{})},x[a.R]=function(e,t,r,n){var o=n.digestEffect,i=M,c=Object.keys(t),u=(0,
s.array)(t)?(0,a.m)(c.length):{},l={},d=!1;c.forEach((function(e){var t=function(t,n){d||(n||(0,a.s)(t)?(r.cancel(),r(t,n)):(r.cancel(),d=!0,u[e]=t,r(u)))};t.cancel=a.t,l[e]=t})),r.cancel=function(){d||(d=!0,c.forEach((function(e){return l[e].cancel()})))},c.forEach((function(e){d||o(t[e],i,l[e],e)}))},x[a.C]=function(e,t,r,n){var o=t.context,i=t.fn,c=t.args,u=n.task;try{var l=i.apply(o,c);if((0,s.promise)(l))return void P(l,r);if((0,s.iterator)(l))return void G(e,l,u.context,M,(0,a.j)(i),!1,r);r(l)}catch(e){r(e,!0)}},x[a.a]=function(e,t,r){var n=t.context,o=t.fn,i=t.args;try{var a=function(e,t){(0,s.undef)(e)?r(t):r(e,!0)};o.apply(n,i.concat(a)),a.cancel&&(r.cancel=a.cancel)}catch(e){r(e,!0)}},x[a.F]=function(e,t,r,n){var o=t.context,i=t.fn,c=t.args,u=t.detached,l=n.task,d=function(e){var t=e.context,r=e.fn,n=e.args;try{var o=r.apply(t,n);if((0,s.iterator)(o))return o;var i=!1;return(0,a.q)((function(e){return i?{value:e,done:!0}:(i=!0,{value:o,done:!(0,s.promise)(o)})}))}catch(e){return(0,a.q)((function(){throw e}))}}({context:o,fn:i,args:c}),f=function(e,t){return e.isSagaIterator?{name:e.meta.name}:(0,a.j)(t)}(d,i);g((function(){var t=G(e,d,l.context,M,f,u,void 0);u?r(t):t.isRunning()?(l.queue.addTask(t),r(t)):t.isAborted()?l.queue.abort(t.error()):r(t)}))},x[a.J]=function(e,t,r,n){var o=n.task,i=function(e,t){if(e.isRunning()){var r={task:o,cb:t};t.cancel=function(){e.isRunning()&&(0,a.r)(e.joiners,r)},e.joiners.push(r)}else e.isAborted()?t(e.error(),!0):t(e.result())};if((0,s.array)(t)){if(0===t.length)return void r([]);var c=(0,a.l)(t,r);t.forEach((function(e,t){i(e,c[t])}))}else i(t,r)},x[a.b]=function(e,t,r,o){var i=o.task;t===n.SELF_CANCELLATION?R(i):(0,s.array)(t)?t.forEach(R):R(t),r()},x[a.S]=function(e,t,r){var n=t.selector,o=t.args;try{r(n.apply(void 0,[e.getState()].concat(o)))}catch(e){r(e,!0)}},x[a.d]=function(e,t,r){var n=t.pattern,o=C(t.buffer),i=w(n),s=function t(r){D(r)||e.channel.take(t,i),o.put(r)},a=o.close;o.close=function(){s.cancel(),a()},e.channel.take(s,i),r(o)},x[a.f]=function(e,t,r,n){r(n.task.isCancelled())},x[a.g]=function(e,t,r){t.flush(r)},x[a.G]=function(e,t,r,n){r(n.task.context[t])},x[a.h]=function(e,t,r,n){var o=n.task;(0,a.p)(o.context,t),r()},x);function _(e,t){return e+"?"+t}function j(e){var t=e.name,r=e.location;return r?t+"  "+_(r.fileName,r.lineNumber):t}function k(e){var t=(0,a.u)((function(e){return e.cancelledTasks}),e);return t.length?["Tasks cancelled due to error:"].concat(t).join("\n"):""}var H=null,U=[],F=function(e){e.crashedEffect=H,U.push(e)},$=function(){H=null,U.length=0},B=function(e){H=e},q=function(){var e=U[0],t=U.slice(1),r=e.crashedEffect?function(e){var t=(0,a.v)(e);return t?t.code+"  "+_(t.fileName,t.lineNumber):""}(e.crashedEffect):null;return["The above error occurred in task "+j(e.meta)+(r?" \n when executing effect "+r:"")].concat(t.map((function(e){return"    created by "+j(e.meta)})),[k(U)]).join("\n")};function K(e,t,r,o,i,s,c){var l;void 0===c&&(c=a.t);var d,f,h=0,g=null,p=[],v=Object.create(r),y=function(e,t,r){var n,o=[],i=!1;function s(e){
t(),u(),r(e,!0)}function c(t){o.push(t),t.cont=function(c,u){i||((0,a.r)(o,t),t.cont=a.t,u?s(c):(t===e&&(n=c),o.length||(i=!0,r(n))))}}function u(){i||(i=!0,o.forEach((function(e){e.cont=a.t,e.cancel()})),o=[])}return c(e),{addTask:c,cancelAll:u,abort:s,getTasks:function(){return o}}}(t,(function(){p.push.apply(p,y.getTasks().map((function(e){return e.meta.name})))}),m);function m(t,r){if(r){if(h=2,F({meta:i,cancelledTasks:p}),S.isRoot){var o=q();$(),e.onError(t,{sagaStack:o})}f=t,g&&g.reject(t)}else t===n.TASK_CANCEL?h=1:1!==h&&(h=3),d=t,g&&g.resolve(t);S.cont(t,r),S.joiners.forEach((function(e){e.cb(t,r)})),S.joiners=null}var S=((l={})[n.TASK]=!0,l.id=o,l.meta=i,l.isRoot=s,l.context=v,l.joiners=[],l.queue=y,l.cancel=function(){0===h&&(h=1,y.cancelAll(),m(n.TASK_CANCEL,!1))},l.cont=c,l.end=m,l.setContext=function(e){(0,a.p)(v,e)},l.toPromise=function(){return g||(g=u(),2===h?g.reject(f):0!==h&&g.resolve(d)),g.promise},l.isRunning=function(){return 0===h},l.isCancelled=function(){return 1===h||0===h&&1===t.status},l.isAborted=function(){return 2===h},l.result=function(){return d},l.error=function(){return f},l);return S}function G(e,t,r,o,i,c,u){var l=e.finalizeRunEffect((function(t,r,o){if((0,s.promise)(t))P(t,o);else if((0,s.iterator)(t))G(e,t,f.context,r,i,!1,o);else if(t&&t[n.IO]){(0,L[t.type])(e,t.payload,o,h)}else o(t)}));g.cancel=a.t;var d={meta:i,cancel:function(){0===d.status&&(d.status=1,g(n.TASK_CANCEL))},status:0},f=K(e,d,r,o,i,c,u),h={task:f,digestEffect:p};return u&&(u.cancel=f.cancel),g(),f;function g(e,r){try{var i;r?(i=t.throw(e),$()):(0,a.y)(e)?(d.status=1,g.cancel(),i=(0,s.func)(t.return)?t.return(n.TASK_CANCEL):{done:!0,value:n.TASK_CANCEL}):i=(0,a.z)(e)?(0,s.func)(t.return)?t.return():{done:!0}:t.next(e),i.done?(1!==d.status&&(d.status=3),d.cont(i.value)):p(i.value,o,g)}catch(e){if(1===d.status)throw e;d.status=2,d.cont(e,!0)}}function p(t,r,n,o){void 0===o&&(o="");var i,s=A();function c(r,o){i||(i=!0,n.cancel=a.t,e.sagaMonitor&&(o?e.sagaMonitor.effectRejected(s,r):e.sagaMonitor.effectResolved(s,r)),o&&B(t),n(r,o))}e.sagaMonitor&&e.sagaMonitor.effectTriggered({effectId:s,parentEffectId:r,label:o,effect:t}),c.cancel=a.t,n.cancel=function(){i||(i=!0,c.cancel(),c.cancel=a.t,e.sagaMonitor&&e.sagaMonitor.effectCancelled(s))},l(t,s,c)}}function W(e,t){var r=e.channel,n=void 0===r?N():r,o=e.dispatch,i=e.getState,s=e.context,c=void 0===s?{}:s,u=e.sagaMonitor,l=e.effectMiddlewares,d=e.onError,f=void 0===d?a.D:d;for(var h=arguments.length,p=new Array(h>2?h-2:0),v=2;v<h;v++)p[v-2]=arguments[v];var y=t.apply(void 0,p);var m,S=A();if(u&&(u.rootSagaStarted=u.rootSagaStarted||a.t,u.effectTriggered=u.effectTriggered||a.t,u.effectResolved=u.effectResolved||a.t,u.effectRejected=u.effectRejected||a.t,u.effectCancelled=u.effectCancelled||a.t,u.actionDispatched=u.actionDispatched||a.t,u.rootSagaStarted({effectId:S,saga:t,args:p})),l){var b=a.B.apply(void 0,l);m=function(e){return function(t,r,n){return b((function(t){return e(t,r,n)}))(t)}}}else m=a.H;var O={channel:n,dispatch:(0,a.E)(o),getState:i,sagaMonitor:u,
onError:f,finalizeRunEffect:m};return g((function(){var e=G(O,y,c,S,(0,a.j)(t),!0,void 0);return u&&u.effectResolved(S,e),e}))}const Y=function(e){var t,r=void 0===e?{}:e,n=r.context,s=void 0===n?{}:n,c=r.channel,u=void 0===c?N():c,l=r.sagaMonitor,d=(0,i.default)(r,["context","channel","sagaMonitor"]);function f(e){var r=e.getState,n=e.dispatch;return t=W.bind(null,(0,o.default)({},d,{context:s,channel:u,dispatch:n,getState:r,sagaMonitor:l})),function(e){return function(t){l&&l.actionDispatched&&l.actionDispatched(t);var r=e(t);return u.put(t),r}}}return f.run=function(){return t.apply(void 0,arguments)},f.setContext=function(e){(0,a.p)(s,e)},f},z=Y},29885:(e,t,r)=>{"use strict";r.d(t,{all:()=>o.$,call:()=>o.O,cancel:()=>o.N,fork:()=>o.M,put:()=>o.Z,select:()=>o.a4,take:()=>o.L,throttle:()=>l});var n=r(17443),o=r(43705),i=function(e){return{done:!0,value:e}},s={};function a(e){return(0,n.channel)(e)?"channel":(0,n.stringableFunc)(e)?String(e):(0,n.func)(e)?e.name:String(e)}function c(e,t,r){var n,a,c,u=t;function l(t,r){if(u===s)return i(t);if(r&&!a)throw u=s,r;n&&n(t);var o=r?e[a](r):e[u]();return u=o.nextState,c=o.effect,n=o.stateUpdater,a=o.errorState,u===s?i(t):c}return(0,o.q)(l,(function(e){return l(null,e)}),r)}function u(e,t,r){for(var i=arguments.length,s=new Array(i>3?i-3:0),u=3;u<i;u++)s[u-3]=arguments[u];var l,d,f={done:!1,value:(0,o.Q)(e)},h=function(e){return l=e},g=function(e){return d=e},p=!(0,n.channel)(t);return p||g(t),c({q1:function(){return{nextState:"q2",effect:{done:!1,value:(0,o.U)(t,(0,o.V)(1))},stateUpdater:g}},q2:function(){return{nextState:"q3",effect:{done:!1,value:(0,o.L)(d)},stateUpdater:h}},q3:function(){return{nextState:"q4",effect:(e=l,{done:!1,value:o.M.apply(void 0,[r].concat(s,[e]))})};var e},q4:function(){return{nextState:"q2",effect:f}}},p?"q1":"q2","throttle("+a(t)+", "+r.name+")")}function l(e,t,r){for(var n=arguments.length,i=new Array(n>3?n-3:0),s=3;s<n;s++)i[s-3]=arguments[s];return o.M.apply(void 0,[u,e,t,r].concat(i))}},6047:(e,t,r)=>{"use strict";function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e){var t=function(e,t){if("object"!==n(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===n(t)?t:String(t)}function i(e,t,r){return(t=o(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){i(e,t,r[t])
})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function c(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}r.d(t,{applyMiddleware:()=>m,bindActionCreators:()=>v,combineReducers:()=>g,createStore:()=>h});var u="function"==typeof Symbol&&Symbol.observable||"@@observable",l=function(){return Math.random().toString(36).substring(7).split("").join(".")},d={INIT:"@@redux/INIT"+l(),REPLACE:"@@redux/REPLACE"+l(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+l()}};function f(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function h(e,t,r){var n;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(c(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(c(1));return r(h)(e,t)}if("function"!=typeof e)throw new Error(c(2));var o=e,i=t,s=[],a=s,l=!1;function g(){a===s&&(a=s.slice())}function p(){if(l)throw new Error(c(3));return i}function v(e){if("function"!=typeof e)throw new Error(c(4));if(l)throw new Error(c(5));var t=!0;return g(),a.push(e),function(){if(t){if(l)throw new Error(c(6));t=!1,g();var r=a.indexOf(e);a.splice(r,1),s=null}}}function y(e){if(!f(e))throw new Error(c(7));if(void 0===e.type)throw new Error(c(8));if(l)throw new Error(c(9));try{l=!0,i=o(i,e)}finally{l=!1}for(var t=s=a,r=0;r<t.length;r++){(0,t[r])()}return e}return y({type:d.INIT}),(n={dispatch:y,subscribe:v,getState:p,replaceReducer:function(e){if("function"!=typeof e)throw new Error(c(10));o=e,y({type:d.REPLACE})}})[u]=function(){var e,t=v;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(c(11));function r(){e.next&&e.next(p())}return r(),{unsubscribe:t(r)}}})[u]=function(){return this},e},n}function g(e){for(var t=Object.keys(e),r={},n=0;n<t.length;n++){var o=t[n];0,"function"==typeof e[o]&&(r[o]=e[o])}var i,s=Object.keys(r);try{!function(e){Object.keys(e).forEach((function(t){var r=e[t];if(void 0===r(void 0,{type:d.INIT}))throw new Error(c(12));if(void 0===r(void 0,{type:d.PROBE_UNKNOWN_ACTION()}))throw new Error(c(13))}))}(r)}catch(e){i=e}return function(e,t){if(void 0===e&&(e={}),i)throw i;for(var n=!1,o={},a=0;a<s.length;a++){var u=s[a],l=r[u],d=e[u],f=l(d,t);if(void 0===f){t&&t.type;throw new Error(c(14))}o[u]=f,n=n||f!==d}return(n=n||s.length!==Object.keys(e).length)?o:e}}function p(e,t){return function(){return t(e.apply(this,arguments))}}function v(e,t){if("function"==typeof e)return p(e,t);if("object"!=typeof e||null===e)throw new Error(c(16));var r={};for(var n in e){var o=e[n];"function"==typeof o&&(r[n]=p(o,t))}return r}function y(){
for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function m(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return function(e){return function(){var r=e.apply(void 0,arguments),n=function(){throw new Error(c(15))},o={getState:r.getState,dispatch:function(){return n.apply(void 0,arguments)}},i=t.map((function(e){return e(o)}));return n=y.apply(void 0,i)(r.dispatch),a(a({},r),{},{dispatch:n})}}}},77145:(e,t,r)=>{"use strict";function n(e,t){return e===t}r.d(t,{createSelector:()=>o});var o=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];var i=0,s=n.pop(),a=function(e){var t=Array.isArray(e[0])?e[0]:e;if(!t.every((function(e){return"function"==typeof e}))){var r=t.map((function(e){return typeof e})).join(", ");throw new Error("Selector creators expect all input-selectors to be functions, instead received the following types: ["+r+"]")}return t}(n),c=e.apply(void 0,[function(){return i++,s.apply(null,arguments)}].concat(r)),u=e((function(){for(var e=[],t=a.length,r=0;r<t;r++)e.push(a[r].apply(null,arguments));return c.apply(null,e)}));return u.resultFunc=s,u.dependencies=a,u.recomputations=function(){return i},u.resetRecomputations=function(){return i=0},u}}((function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n,r=null,o=null;return function(){return function(e,t,r){if(null===t||null===r||t.length!==r.length)return!1;for(var n=t.length,o=0;o<n;o++)if(!e(t[o],r[o]))return!1;return!0}(t,r,arguments)||(o=e.apply(null,arguments)),r=arguments,o}}))},71052:(e,t,r)=>{"use strict";var n=r(50959);var o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,s=n.useEffect,a=n.useLayoutEffect,c=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),o=n[0].inst,l=n[1];return a((function(){o.value=r,o.getSnapshot=t,u(o)&&l({inst:o})}),[e,r,t]),s((function(){return u(o)&&l({inst:o}),e((function(){u(o)&&l({inst:o})}))}),[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:l},28283:(e,t,r)=>{"use strict";var n=r(50959),o=r(83363);var i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},s=o.useSyncExternalStore,a=n.useRef,c=n.useEffect,u=n.useMemo,l=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,o){var d=a(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=u((function(){function e(e){if(!c){if(c=!0,s=e,e=n(e),void 0!==o&&f.hasValue){var t=f.value;if(o(t,e))return a=t}return a=e
}if(t=a,i(s,e))return t;var r=n(e);return void 0!==o&&o(t,r)?t:(s=e,a=r)}var s,a,c=!1,u=void 0===r?null:r;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]}),[t,r,n,o]);var h=s(e,d[0],d[1]);return c((function(){f.hasValue=!0,f.value=h}),[h]),l(h),h}},83363:(e,t,r)=>{"use strict";e.exports=r(71052)},34730:(e,t,r)=>{"use strict";e.exports=r(28283)},30950:(e,t,r)=>{"use strict";function n(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}r.d(t,{default:()=>n})},6346:(e,t,r)=>{"use strict";function n(e,t,...r){if("undefined"!=typeof process&&void 0===t)throw new Error("invariant requires an error message argument");if(!e){let e;if(void 0===t)e=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let n=0;e=new Error(t.replace(/%s/g,(function(){return r[n++]}))),e.name="Invariant Violation"}throw e.framesToPop=1,e}}r.d(t,{invariant:()=>n})},98789:(e,t,r)=>{"use strict";function n(e,t,r,n){let o=r?r.call(n,e,t):void 0;if(void 0!==o)return!!o;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;const i=Object.keys(e),s=Object.keys(t);if(i.length!==s.length)return!1;const a=Object.prototype.hasOwnProperty.bind(t);for(let s=0;s<i.length;s++){const c=i[s];if(!a(c))return!1;const u=e[c],l=t[c];if(o=r?r.call(n,u,l,c):void 0,!1===o||void 0===o&&u!==l)return!1}return!0}r.d(t,{shallowEqual:()=>n})},98314:(e,t,r)=>{"use strict";let n;function o(){return n||(n=new Image,n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="),n}r.d(t,{getEmptyImage:()=>o})},10170:(e,t,r)=>{"use strict";r.d(t,{HTML5Backend:()=>T});var n={};function o(e){let t=null;return()=>(null==t&&(t=e()),t)}r.r(n),r.d(n,{FILE:()=>a,HTML:()=>l,TEXT:()=>u,URL:()=>c});class i{enter(e){const t=this.entered.length;return this.entered=function(e,t){const r=new Set,n=e=>r.add(e);e.forEach(n),t.forEach(n);const o=[];return r.forEach((e=>o.push(e))),o}(this.entered.filter((t=>this.isNodeInDocument(t)&&(!t.contains||t.contains(e)))),[e]),0===t&&this.entered.length>0}leave(e){const t=this.entered.length;var r,n;return this.entered=(r=this.entered.filter(this.isNodeInDocument),n=e,r.filter((e=>e!==n))),t>0&&0===this.entered.length}reset(){this.entered=[]}constructor(e){this.entered=[],this.isNodeInDocument=e}}class s{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach((e=>{Object.defineProperty(this.item,e,{configurable:!0,enumerable:!0,get:()=>(console.warn(`Browser doesn't allow reading "${e}" until the drop event.`),null)})}))}loadDataTransfer(e){if(e){const t={};Object.keys(this.config.exposeProperties).forEach((r=>{const n=this.config.exposeProperties[r];null!=n&&(t[r]={value:n(e,this.config.matchesTypes),configurable:!0,enumerable:!0})})),Object.defineProperties(this.item,t)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(e,t){return t===e.getSourceId()}endDrag(){}constructor(e){this.config=e,
this.item={},this.initializeExposedProperties()}}const a="__NATIVE_FILE__",c="__NATIVE_URL__",u="__NATIVE_TEXT__",l="__NATIVE_HTML__";function d(e,t,r){const n=t.reduce(((t,r)=>t||e.getData(r)),"");return null!=n?n:r}const f={[a]:{exposeProperties:{files:e=>Array.prototype.slice.call(e.files),items:e=>e.items,dataTransfer:e=>e},matchesTypes:["Files"]},[l]:{exposeProperties:{html:(e,t)=>d(e,t,""),dataTransfer:e=>e},matchesTypes:["Html","text/html"]},[c]:{exposeProperties:{urls:(e,t)=>d(e,t,"").split("\n"),dataTransfer:e=>e},matchesTypes:["Url","text/uri-list"]},[u]:{exposeProperties:{text:(e,t)=>d(e,t,""),dataTransfer:e=>e},matchesTypes:["Text","text/plain"]}};function h(e){if(!e)return null;const t=Array.prototype.slice.call(e.types||[]);return Object.keys(f).filter((e=>{const r=f[e];return!!(null==r?void 0:r.matchesTypes)&&r.matchesTypes.some((e=>t.indexOf(e)>-1))}))[0]||null}const g=o((()=>/firefox/i.test(navigator.userAgent))),p=o((()=>Boolean(window.safari)));class v{interpolate(e){const{xs:t,ys:r,c1s:n,c2s:o,c3s:i}=this;let s=t.length-1;if(e===t[s])return r[s];let a,c=0,u=i.length-1;for(;c<=u;){a=Math.floor(.5*(c+u));const n=t[a];if(n<e)c=a+1;else{if(!(n>e))return r[a];u=a-1}}s=Math.max(0,u);const l=e-t[s],d=l*l;return r[s]+n[s]*l+o[s]*d+i[s]*l*d}constructor(e,t){const{length:r}=e,n=[];for(let e=0;e<r;e++)n.push(e);n.sort(((t,r)=>e[t]<e[r]?-1:1));const o=[],i=[],s=[];let a,c;for(let n=0;n<r-1;n++)a=e[n+1]-e[n],c=t[n+1]-t[n],i.push(a),o.push(c),s.push(c/a);const u=[s[0]];for(let e=0;e<i.length-1;e++){const t=s[e],r=s[e+1];if(t*r<=0)u.push(0);else{a=i[e];const n=i[e+1],o=a+n;u.push(3*o/((o+n)/t+(o+a)/r))}}u.push(s[s.length-1]);const l=[],d=[];let f;for(let e=0;e<u.length-1;e++){f=s[e];const t=u[e],r=1/i[e],n=t+u[e+1]-f-f;l.push((f-t-n)*r),d.push(n*r*r)}this.xs=e,this.ys=t,this.c1s=u,this.c2s=l,this.c3s=d}}function y(e){const t=1===e.nodeType?e:e.parentElement;if(!t)return null;const{top:r,left:n}=t.getBoundingClientRect();return{x:n,y:r}}function m(e){return{x:e.clientX,y:e.clientY}}function S(e,t,r,n,o){const i="IMG"===(s=t).nodeName&&(g()||!(null===(a=document.documentElement)||void 0===a?void 0:a.contains(s)));var s,a;const c=y(i?e:t),u={x:r.x-c.x,y:r.y-c.y},{offsetWidth:l,offsetHeight:d}=e,{anchorX:f,anchorY:h}=n,{dragPreviewWidth:m,dragPreviewHeight:S}=function(e,t,r,n){let o=e?t.width:r,i=e?t.height:n;return p()&&e&&(i/=window.devicePixelRatio,o/=window.devicePixelRatio),{dragPreviewWidth:o,dragPreviewHeight:i}}(i,t,l,d),{offsetX:b,offsetY:O}=o,E=0===O||O;return{x:0===b||b?b:new v([0,.5,1],[u.x,u.x/l*m,u.x+m-l]).interpolate(f),y:E?O:(()=>{let e=new v([0,.5,1],[u.y,u.y/d*S,u.y+S-d]).interpolate(h);return p()&&i&&(e+=(window.devicePixelRatio-1)*S),e})()}}class b{get window(){return this.globalContext?this.globalContext:"undefined"!=typeof window?window:void 0}get document(){var e;return(null===(e=this.globalContext)||void 0===e?void 0:e.document)?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var e
;return(null===(e=this.optionsArgs)||void 0===e?void 0:e.rootElement)||this.window}constructor(e,t){this.ownerDocument=null,this.globalContext=e,this.optionsArgs=t}}function O(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){O(e,t,r[t])}))}return e}class w{profile(){var e,t;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:(null===(e=this.dragStartSourceIds)||void 0===e?void 0:e.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:(null===(t=this.dragOverTargetIds)||void 0===t?void 0:t.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){const e=this.rootElement;if(void 0!==e){if(e.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");e.__isReactDndBackendSetUp=!0,this.addEventListeners(e)}}teardown(){const e=this.rootElement;var t;void 0!==e&&(e.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId&&(null===(t=this.window)||void 0===t||t.cancelAnimationFrame(this.asyncEndDragFrameId)))}connectDragPreview(e,t,r){return this.sourcePreviewNodeOptions.set(e,r),this.sourcePreviewNodes.set(e,t),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDragSource(e,t,r){this.sourceNodes.set(e,t),this.sourceNodeOptions.set(e,r);const n=t=>this.handleDragStart(t,e),o=e=>this.handleSelectStart(e);return t.setAttribute("draggable","true"),t.addEventListener("dragstart",n),t.addEventListener("selectstart",o),()=>{this.sourceNodes.delete(e),this.sourceNodeOptions.delete(e),t.removeEventListener("dragstart",n),t.removeEventListener("selectstart",o),t.setAttribute("draggable","false")}}connectDropTarget(e,t){const r=t=>this.handleDragEnter(t,e),n=t=>this.handleDragOver(t,e),o=t=>this.handleDrop(t,e);return t.addEventListener("dragenter",r),t.addEventListener("dragover",n),t.addEventListener("drop",o),()=>{t.removeEventListener("dragenter",r),t.removeEventListener("dragover",n),t.removeEventListener("drop",o)}}addEventListeners(e){e.addEventListener&&(e.addEventListener("dragstart",this.handleTopDragStart),e.addEventListener("dragstart",this.handleTopDragStartCapture,!0),e.addEventListener("dragend",this.handleTopDragEndCapture,!0),e.addEventListener("dragenter",this.handleTopDragEnter),e.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),
e.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.addEventListener("dragover",this.handleTopDragOver),e.addEventListener("dragover",this.handleTopDragOverCapture,!0),e.addEventListener("drop",this.handleTopDrop),e.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(e){e.removeEventListener&&(e.removeEventListener("dragstart",this.handleTopDragStart),e.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),e.removeEventListener("dragend",this.handleTopDragEndCapture,!0),e.removeEventListener("dragenter",this.handleTopDragEnter),e.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.removeEventListener("dragover",this.handleTopDragOver),e.removeEventListener("dragover",this.handleTopDragOverCapture,!0),e.removeEventListener("drop",this.handleTopDrop),e.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const e=this.monitor.getSourceId(),t=this.sourceNodeOptions.get(e);return E({dropEffect:this.altKeyPressed?"copy":"move"},t||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const e=this.monitor.getSourceId();return E({anchorX:.5,anchorY:.5,captureDraggingState:!1},this.sourcePreviewNodeOptions.get(e)||{})}isDraggingNativeItem(){const e=this.monitor.getItemType();return Object.keys(n).some((t=>n[t]===e))}beginDragNativeItem(e,t){this.clearCurrentDragSourceNode(),this.currentNativeSource=function(e,t){const r=f[e];if(!r)throw new Error(`native type ${e} has no configuration`);const n=new s(r);return n.loadDataTransfer(t),n}(e,t),this.currentNativeHandle=this.registry.addSource(e,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(e){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=e;this.mouseMoveTimeoutTimer=setTimeout((()=>{var e;return null===(e=this.rootElement)||void 0===e?void 0:e.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}),1e3)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){var e;if(this.currentDragSourceNode=null,this.rootElement)null===(e=this.window)||void 0===e||e.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0);return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(e,t){e.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(t))}handleDragEnter(e,t){this.dragEnterTargetIds.unshift(t)}handleDragOver(e,t){null===this.dragOverTargetIds&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(t)}handleDrop(e,t){this.dropTargetIds.unshift(t)}constructor(e,t,r){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],
this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=e=>{const t=this.sourceNodes.get(e);return t&&y(t)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=e=>Boolean(e&&this.document&&this.document.body&&this.document.body.contains(e)),this.endDragIfSourceWasRemovedFromDOM=()=>{const e=this.currentDragSourceNode;null==e||this.isNodeInDocument(e)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=e=>{null===this.hoverRafId&&"undefined"!=typeof requestAnimationFrame&&(this.hoverRafId=requestAnimationFrame((()=>{this.monitor.isDragging()&&this.actions.hover(e||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null})))},this.cancelHover=()=>{null!==this.hoverRafId&&"undefined"!=typeof cancelAnimationFrame&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=e=>{if(e.defaultPrevented)return;const{dragStartSourceIds:t}=this;this.dragStartSourceIds=null;const r=m(e);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(t||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:r});const{dataTransfer:n}=e,o=h(n);if(this.monitor.isDragging()){if(n&&"function"==typeof n.setDragImage){const e=this.monitor.getSourceId(),t=this.sourceNodes.get(e),o=this.sourcePreviewNodes.get(e)||t;if(o){const{anchorX:e,anchorY:i,offsetX:s,offsetY:a}=this.getCurrentSourcePreviewNodeOptions(),c=S(t,o,r,{anchorX:e,anchorY:i},{offsetX:s,offsetY:a});n.setDragImage(o,c.x,c.y)}}try{null==n||n.setData("application/json",{})}catch(e){}this.setCurrentDragSourceNode(e.target);const{captureDraggingState:t}=this.getCurrentSourcePreviewNodeOptions();t?this.actions.publishDragSource():setTimeout((()=>this.actions.publishDragSource()),0)}else if(o)this.beginDragNativeItem(o);else{if(n&&!n.types&&(e.target&&!e.target.hasAttribute||!e.target.hasAttribute("draggable")))return;e.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=e=>{var t;(this.dragEnterTargetIds=[],this.isDraggingNativeItem())&&(null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer));if(!this.enterLeaveCounter.enter(e.target)||this.monitor.isDragging())return;const{dataTransfer:r}=e,n=h(r);n&&this.beginDragNativeItem(n,r)},this.handleTopDragEnter=e=>{const{dragEnterTargetIds:t}=this;if(this.dragEnterTargetIds=[],
!this.monitor.isDragging())return;this.altKeyPressed=e.altKey,t.length>0&&this.actions.hover(t,{clientOffset:m(e)});t.some((e=>this.monitor.canDropOnTarget(e)))&&(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect()))},this.handleTopDragOverCapture=e=>{var t;(this.dragOverTargetIds=[],this.isDraggingNativeItem())&&(null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer))},this.handleTopDragOver=e=>{const{dragOverTargetIds:t}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging())return e.preventDefault(),void(e.dataTransfer&&(e.dataTransfer.dropEffect="none"));this.altKeyPressed=e.altKey,this.lastClientOffset=m(e),this.scheduleHover(t);(t||[]).some((e=>this.monitor.canDropOnTarget(e)))?(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?e.preventDefault():(e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=e=>{this.isDraggingNativeItem()&&e.preventDefault();this.enterLeaveCounter.leave(e.target)&&(this.isDraggingNativeItem()&&setTimeout((()=>this.endDragNativeItem()),0),this.cancelHover())},this.handleTopDropCapture=e=>{var t;(this.dropTargetIds=[],this.isDraggingNativeItem())?(e.preventDefault(),null===(t=this.currentNativeSource)||void 0===t||t.loadDataTransfer(e.dataTransfer)):h(e.dataTransfer)&&e.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=e=>{const{dropTargetIds:t}=this;this.dropTargetIds=[],this.actions.hover(t,{clientOffset:m(e)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=e=>{const t=e.target;"function"==typeof t.dragDrop&&("INPUT"===t.tagName||"SELECT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable||(e.preventDefault(),t.dragDrop()))},this.options=new b(t,r),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.enterLeaveCounter=new i(this.isNodeInDocument)}}const T=function(e,t,r){return new w(e,t,r)}},7809:(e,t,r)=>{"use strict";r.d(t,{TouchBackend:()=>g});var n,o=r(6346);!function(e){e.mouse="mouse",e.touch="touch",e.keyboard="keyboard"}(n||(n={}));class i{get delay(){var e;return null!==(e=this.args.delay)&&void 0!==e?e:0}get scrollAngleRanges(){return this.args.scrollAngleRanges}get getDropTargetElementsAtPoint(){return this.args.getDropTargetElementsAtPoint}get ignoreContextMenu(){var e;return null!==(e=this.args.ignoreContextMenu)&&void 0!==e&&e}get enableHoverOutsideTarget(){var e;return null!==(e=this.args.enableHoverOutsideTarget)&&void 0!==e&&e}get enableKeyboardEvents(){var e;return null!==(e=this.args.enableKeyboardEvents)&&void 0!==e&&e}get enableMouseEvents(){var e;return null!==(e=this.args.enableMouseEvents)&&void 0!==e&&e}get enableTouchEvents(){var e;return null===(e=this.args.enableTouchEvents)||void 0===e||e}get touchSlop(){return this.args.touchSlop||0}
get delayTouchStart(){var e,t,r,n;return null!==(n=null!==(r=null===(e=this.args)||void 0===e?void 0:e.delayTouchStart)&&void 0!==r?r:null===(t=this.args)||void 0===t?void 0:t.delay)&&void 0!==n?n:0}get delayMouseStart(){var e,t,r,n;return null!==(n=null!==(r=null===(e=this.args)||void 0===e?void 0:e.delayMouseStart)&&void 0!==r?r:null===(t=this.args)||void 0===t?void 0:t.delay)&&void 0!==n?n:0}get window(){return this.context&&this.context.window?this.context.window:"undefined"!=typeof window?window:void 0}get document(){var e;return(null===(e=this.context)||void 0===e?void 0:e.document)?this.context.document:this.window?this.window.document:void 0}get rootElement(){var e;return(null===(e=this.args)||void 0===e?void 0:e.rootElement)||this.document}constructor(e,t){this.args=e,this.context=t}}const s=1,a=0;function c(e){return void 0===e.button||e.button===a}function u(e){return!!e.targetTouches}function l(e,t){return u(e)?function(e,t){return 1===e.targetTouches.length?l(e.targetTouches[0]):t&&1===e.touches.length&&e.touches[0].target===t.target?l(e.touches[0]):void 0}(e,t):{x:e.clientX,y:e.clientY}}const d=(()=>{let e=!1;try{addEventListener("test",(()=>{}),Object.defineProperty({},"passive",{get:()=>(e=!0,!0)}))}catch(e){}return e})(),f={[n.mouse]:{start:"mousedown",move:"mousemove",end:"mouseup",contextmenu:"contextmenu"},[n.touch]:{start:"touchstart",move:"touchmove",end:"touchend"},[n.keyboard]:{keydown:"keydown"}};class h{profile(){var e;return{sourceNodes:this.sourceNodes.size,sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,targetNodes:this.targetNodes.size,dragOverTargetIds:(null===(e=this.dragOverTargetIds)||void 0===e?void 0:e.length)||0}}get document(){return this.options.document}setup(){const e=this.options.rootElement;e&&((0,o.invariant)(!h.isSetUp,"Cannot have two Touch backends at the same time."),h.isSetUp=!0,this.addEventListener(e,"start",this.getTopMoveStartHandler()),this.addEventListener(e,"start",this.handleTopMoveStartCapture,!0),this.addEventListener(e,"move",this.handleTopMove),this.addEventListener(e,"move",this.handleTopMoveCapture,!0),this.addEventListener(e,"end",this.handleTopMoveEndCapture,!0),this.options.enableMouseEvents&&!this.options.ignoreContextMenu&&this.addEventListener(e,"contextmenu",this.handleTopMoveEndCapture),this.options.enableKeyboardEvents&&this.addEventListener(e,"keydown",this.handleCancelOnEscape,!0))}teardown(){const e=this.options.rootElement;e&&(h.isSetUp=!1,this._mouseClientOffset={},this.removeEventListener(e,"start",this.handleTopMoveStartCapture,!0),this.removeEventListener(e,"start",this.handleTopMoveStart),this.removeEventListener(e,"move",this.handleTopMoveCapture,!0),this.removeEventListener(e,"move",this.handleTopMove),this.removeEventListener(e,"end",this.handleTopMoveEndCapture,!0),this.options.enableMouseEvents&&!this.options.ignoreContextMenu&&this.removeEventListener(e,"contextmenu",this.handleTopMoveEndCapture),
this.options.enableKeyboardEvents&&this.removeEventListener(e,"keydown",this.handleCancelOnEscape,!0),this.uninstallSourceNodeRemovalObserver())}addEventListener(e,t,r,n=!1){const o=d?{capture:n,passive:!1}:n;this.listenerTypes.forEach((function(n){const i=f[n][t];i&&e.addEventListener(i,r,o)}))}removeEventListener(e,t,r,n=!1){const o=d?{capture:n,passive:!1}:n;this.listenerTypes.forEach((function(n){const i=f[n][t];i&&e.removeEventListener(i,r,o)}))}connectDragSource(e,t){const r=this.handleMoveStart.bind(this,e);return this.sourceNodes.set(e,t),this.addEventListener(t,"start",r),()=>{this.sourceNodes.delete(e),this.removeEventListener(t,"start",r)}}connectDragPreview(e,t,r){return this.sourcePreviewNodeOptions.set(e,r),this.sourcePreviewNodes.set(e,t),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDropTarget(e,t){const r=this.options.rootElement;if(!this.document||!r)return()=>{};const n=n=>{if(!this.document||!r||!this.monitor.isDragging())return;let o;switch(n.type){case f.mouse.move:o={x:n.clientX,y:n.clientY};break;case f.touch.move:var i,s;o={x:(null===(i=n.touches[0])||void 0===i?void 0:i.clientX)||0,y:(null===(s=n.touches[0])||void 0===s?void 0:s.clientY)||0}}const a=null!=o?this.document.elementFromPoint(o.x,o.y):void 0,c=a&&t.contains(a);return a===t||c?this.handleMove(n,e):void 0};return this.addEventListener(this.document.body,"move",n),this.targetNodes.set(e,t),()=>{this.document&&(this.targetNodes.delete(e),this.removeEventListener(this.document.body,"move",n))}}getTopMoveStartHandler(){return this.options.delayTouchStart||this.options.delayMouseStart?this.handleTopMoveStartDelay:this.handleTopMoveStart}installSourceNodeRemovalObserver(e){this.uninstallSourceNodeRemovalObserver(),this.draggedSourceNode=e,this.draggedSourceNodeRemovalObserver=new MutationObserver((()=>{e&&!e.parentElement&&(this.resurrectSourceNode(),this.uninstallSourceNodeRemovalObserver())})),e&&e.parentElement&&this.draggedSourceNodeRemovalObserver.observe(e.parentElement,{childList:!0})}resurrectSourceNode(){this.document&&this.draggedSourceNode&&(this.draggedSourceNode.style.display="none",this.draggedSourceNode.removeAttribute("data-reactid"),this.document.body.appendChild(this.draggedSourceNode))}uninstallSourceNodeRemovalObserver(){this.draggedSourceNodeRemovalObserver&&this.draggedSourceNodeRemovalObserver.disconnect(),this.draggedSourceNodeRemovalObserver=void 0,this.draggedSourceNode=void 0}constructor(e,t,r){this.getSourceClientOffset=e=>{const t=this.sourceNodes.get(e);return t&&function(e){const t=1===e.nodeType?e:e.parentElement;if(!t)return;const{top:r,left:n}=t.getBoundingClientRect();return{x:n,y:r}}(t)},this.handleTopMoveStartCapture=e=>{c(e)&&(this.moveStartSourceIds=[])},this.handleMoveStart=e=>{Array.isArray(this.moveStartSourceIds)&&this.moveStartSourceIds.unshift(e)},this.handleTopMoveStart=e=>{if(!c(e))return;const t=l(e);t&&(u(e)&&(this.lastTargetTouchFallback=e.targetTouches[0]),this._mouseClientOffset=t),this.waitingForDelay=!1},this.handleTopMoveStartDelay=e=>{
if(!c(e))return;const t=e.type===f.touch.start?this.options.delayTouchStart:this.options.delayMouseStart;this.timeout=setTimeout(this.handleTopMoveStart.bind(this,e),t),this.waitingForDelay=!0},this.handleTopMoveCapture=()=>{this.dragOverTargetIds=[]},this.handleMove=(e,t)=>{this.dragOverTargetIds&&this.dragOverTargetIds.unshift(t)},this.handleTopMove=e=>{if(this.timeout&&clearTimeout(this.timeout),!this.document||this.waitingForDelay)return;const{moveStartSourceIds:t,dragOverTargetIds:r}=this,n=this.options.enableHoverOutsideTarget,o=l(e,this.lastTargetTouchFallback);if(!o)return;if(this._isScrolling||!this.monitor.isDragging()&&function(e,t,r,n,o){if(!o)return!1;const i=180*Math.atan2(n-t,r-e)/Math.PI+180;for(let e=0;e<o.length;++e){const t=o[e];if(t&&(null==t.start||i>=t.start)&&(null==t.end||i<=t.end))return!0}return!1}(this._mouseClientOffset.x||0,this._mouseClientOffset.y||0,o.x,o.y,this.options.scrollAngleRanges))return void(this._isScrolling=!0);var i,s,a,c;if(!this.monitor.isDragging()&&this._mouseClientOffset.hasOwnProperty("x")&&t&&(i=this._mouseClientOffset.x||0,s=this._mouseClientOffset.y||0,a=o.x,c=o.y,Math.sqrt(Math.pow(Math.abs(a-i),2)+Math.pow(Math.abs(c-s),2))>(this.options.touchSlop?this.options.touchSlop:0))&&(this.moveStartSourceIds=void 0,this.actions.beginDrag(t,{clientOffset:this._mouseClientOffset,getSourceClientOffset:this.getSourceClientOffset,publishSource:!1})),!this.monitor.isDragging())return;const u=this.sourceNodes.get(this.monitor.getSourceId());this.installSourceNodeRemovalObserver(u),this.actions.publishDragSource(),e.cancelable&&e.preventDefault();const d=(r||[]).map((e=>this.targetNodes.get(e))).filter((e=>!!e)),f=this.options.getDropTargetElementsAtPoint?this.options.getDropTargetElementsAtPoint(o.x,o.y,d):this.document.elementsFromPoint(o.x,o.y),h=[];for(const e in f){if(!f.hasOwnProperty(e))continue;let t=f[e];for(null!=t&&h.push(t);t;)t=t.parentElement,t&&-1===h.indexOf(t)&&h.push(t)}const g=h.filter((e=>d.indexOf(e)>-1)).map((e=>this._getDropTargetId(e))).filter((e=>!!e)).filter(((e,t,r)=>r.indexOf(e)===t));if(n)for(const e in this.targetNodes){const t=this.targetNodes.get(e);if(u&&t&&t.contains(u)&&-1===g.indexOf(e)){g.unshift(e);break}}g.reverse(),this.actions.hover(g,{clientOffset:o})},this._getDropTargetId=e=>{const t=this.targetNodes.keys();let r=t.next();for(;!1===r.done;){const n=r.value;if(e===this.targetNodes.get(n))return n;r=t.next()}},this.handleTopMoveEndCapture=e=>{this._isScrolling=!1,this.lastTargetTouchFallback=void 0,function(e){return void 0===e.buttons||!(e.buttons&s)}(e)&&(this.monitor.isDragging()&&!this.monitor.didDrop()?(e.cancelable&&e.preventDefault(),this._mouseClientOffset={},this.uninstallSourceNodeRemovalObserver(),this.actions.drop(),this.actions.endDrag()):this.moveStartSourceIds=void 0)},this.handleCancelOnEscape=e=>{"Escape"===e.key&&this.monitor.isDragging()&&(this._mouseClientOffset={},this.uninstallSourceNodeRemovalObserver(),this.actions.endDrag())},this.options=new i(r,t),this.actions=e.getActions(),this.monitor=e.getMonitor(),
this.sourceNodes=new Map,this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.targetNodes=new Map,this.listenerTypes=[],this._mouseClientOffset={},this._isScrolling=!1,this.options.enableMouseEvents&&this.listenerTypes.push(n.mouse),this.options.enableTouchEvents&&this.listenerTypes.push(n.touch),this.options.enableKeyboardEvents&&this.listenerTypes.push(n.keyboard)}}const g=function(e,t={},r={}){return new h(e,t,r)}},79178:(e,t,r)=>{"use strict";r.d(t,{DndContext:()=>n});const n=(0,r(50959).createContext)({dragDropManager:void 0})},94212:(e,t,r)=>{"use strict";r.d(t,{DndProvider:()=>ye});var n=r(11527);function o(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var i="function"==typeof Symbol&&Symbol.observable||"@@observable",s=function(){return Math.random().toString(36).substring(7).split("").join(".")},a={INIT:"@@redux/INIT"+s(),REPLACE:"@@redux/REPLACE"+s(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+s()}};function c(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function u(e,t,r){var n;if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(o(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(o(1));return r(u)(e,t)}if("function"!=typeof e)throw new Error(o(2));var s=e,l=t,d=[],f=d,h=!1;function g(){f===d&&(f=d.slice())}function p(){if(h)throw new Error(o(3));return l}function v(e){if("function"!=typeof e)throw new Error(o(4));if(h)throw new Error(o(5));var t=!0;return g(),f.push(e),function(){if(t){if(h)throw new Error(o(6));t=!1,g();var r=f.indexOf(e);f.splice(r,1),d=null}}}function y(e){if(!c(e))throw new Error(o(7));if(void 0===e.type)throw new Error(o(8));if(h)throw new Error(o(9));try{h=!0,l=s(l,e)}finally{h=!1}for(var t=d=f,r=0;r<t.length;r++){(0,t[r])()}return e}return y({type:a.INIT}),(n={dispatch:y,subscribe:v,getState:p,replaceReducer:function(e){if("function"!=typeof e)throw new Error(o(10));s=e,y({type:a.REPLACE})}})[i]=function(){var e,t=v;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(o(11));function r(){e.next&&e.next(p())}return r(),{unsubscribe:t(r)}}})[i]=function(){return this},e},n}var l=r(6346);function d(e){return"object"==typeof e}const f="dnd-core/INIT_COORDS",h="dnd-core/BEGIN_DRAG",g="dnd-core/PUBLISH_DRAG_SOURCE",p="dnd-core/HOVER",v="dnd-core/DROP",y="dnd-core/END_DRAG";function m(e,t){return{type:f,payload:{sourceClientOffset:t||null,clientOffset:e||null}}}const S={type:f,payload:{clientOffset:null,sourceClientOffset:null}};function b(e){return function(t=[],r={publishSource:!0}){const{publishSource:n=!0,clientOffset:o,getSourceClientOffset:i}=r,s=e.getMonitor(),a=e.getRegistry();e.dispatch(m(o)),function(e,t,r){(0,
l.invariant)(!t.isDragging(),"Cannot call beginDrag while dragging."),e.forEach((function(e){(0,l.invariant)(r.getSource(e),"Expected sourceIds to be registered.")}))}(t,s,a);const c=function(e,t){let r=null;for(let n=e.length-1;n>=0;n--)if(t.canDragSource(e[n])){r=e[n];break}return r}(t,s);if(null==c)return void e.dispatch(S);let u=null;if(o){if(!i)throw new Error("getSourceClientOffset must be defined");!function(e){(0,l.invariant)("function"==typeof e,"When clientOffset is provided, getSourceClientOffset must be a function.")}(i),u=i(c)}e.dispatch(m(o,u));const f=a.getSource(c).beginDrag(s,c);if(null==f)return;!function(e){(0,l.invariant)(d(e),"Item must be an object.")}(f),a.pinSource(c);const g=a.getSourceType(c);return{type:h,payload:{itemType:g,item:f,sourceId:c,clientOffset:o||null,sourceClientOffset:u||null,isSourcePublic:!!n}}}}function O(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function E(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){O(e,t,r[t])}))}return e}function w(e){return function(t={}){const r=e.getMonitor(),n=e.getRegistry();!function(e){(0,l.invariant)(e.isDragging(),"Cannot call drop while not dragging."),(0,l.invariant)(!e.didDrop(),"Cannot call drop twice during one drag operation.")}(r);const o=function(e){const t=e.getTargetIds().filter(e.canDropOnTarget,e);return t.reverse(),t}(r);o.forEach(((o,i)=>{const s=function(e,t,r,n){const o=r.getTarget(e);let i=o?o.drop(n,e):void 0;(function(e){(0,l.invariant)(void 0===e||d(e),"Drop result must either be an object or undefined.")})(i),void 0===i&&(i=0===t?{}:n.getDropResult());return i}(o,i,n,r),a={type:v,payload:{dropResult:E({},t,s)}};e.dispatch(a)}))}}function T(e){return function(){const t=e.getMonitor(),r=e.getRegistry();!function(e){(0,l.invariant)(e.isDragging(),"Cannot call endDrag while not dragging.")}(t);const n=t.getSourceId();if(null!=n){r.getSource(n,!0).endDrag(t,n),r.unpinSource()}return{type:y}}}function D(e,t){return null===t?null===e:Array.isArray(e)?e.some((e=>e===t)):e===t}function C(e){return function(t,{clientOffset:r}={}){!function(e){(0,l.invariant)(Array.isArray(e),"Expected targetIds to be an array.")}(t);const n=t.slice(0),o=e.getMonitor(),i=e.getRegistry();return function(e,t,r){for(let n=e.length-1;n>=0;n--){const o=e[n];D(t.getTargetType(o),r)||e.splice(n,1)}}(n,i,o.getItemType()),function(e,t,r){(0,l.invariant)(t.isDragging(),"Cannot call hover while not dragging."),(0,l.invariant)(!t.didDrop(),"Cannot call hover after drop.");for(let t=0;t<e.length;t++){const n=e[t];(0,l.invariant)(e.lastIndexOf(n)===t,"Expected targetIds to be unique in the passed array.");const o=r.getTarget(n);(0,l.invariant)(o,"Expected targetIds to be registered.")}}(n,o,i),function(e,t,r){e.forEach((function(e){
r.getTarget(e).hover(t,e)}))}(n,o,i),{type:p,payload:{targetIds:n,clientOffset:r||null}}}}function I(e){return function(){if(e.getMonitor().isDragging())return{type:g}}}class N{receiveBackend(e){this.backend=e}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const e=this,{dispatch:t}=this.store;const r=function(e){return{beginDrag:b(e),publishDragSource:I(e),hover:C(e),drop:w(e),endDrag:T(e)}}(this);return Object.keys(r).reduce(((n,o)=>{const i=r[o];var s;return n[o]=(s=i,(...r)=>{const n=s.apply(e,r);void 0!==n&&t(n)}),n}),{})}dispatch(e){this.store.dispatch(e)}constructor(e,t){this.isSetUp=!1,this.handleRefCountChange=()=>{const e=this.store.getState().refCount>0;this.backend&&(e&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!e&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=e,this.monitor=t,e.subscribe(this.handleRefCountChange)}}function P(e,t){return{x:e.x-t.x,y:e.y-t.y}}const x=[],M=[];x.__IS_NONE__=!0,M.__IS_ALL__=!0;class A{subscribeToStateChange(e,t={}){const{handlerIds:r}=t;(0,l.invariant)("function"==typeof e,"listener must be a function."),(0,l.invariant)(void 0===r||Array.isArray(r),"handlerIds, when specified, must be an array of strings.");let n=this.store.getState().stateId;return this.store.subscribe((()=>{const t=this.store.getState(),o=t.stateId;try{const i=o===n||o===n+1&&!function(e,t){return e!==x&&(e===M||void 0===t||(r=e,t.filter((e=>r.indexOf(e)>-1))).length>0);var r}(t.dirtyHandlerIds,r);i||e()}finally{n=o}}))}subscribeToOffsetChange(e){(0,l.invariant)("function"==typeof e,"listener must be a function.");let t=this.store.getState().dragOffset;return this.store.subscribe((()=>{const r=this.store.getState().dragOffset;r!==t&&(t=r,e())}))}canDragSource(e){if(!e)return!1;const t=this.registry.getSource(e);return(0,l.invariant)(t,`Expected to find a valid source. sourceId=${e}`),!this.isDragging()&&t.canDrag(this,e)}canDropOnTarget(e){if(!e)return!1;const t=this.registry.getTarget(e);if((0,l.invariant)(t,`Expected to find a valid target. targetId=${e}`),!this.isDragging()||this.didDrop())return!1;return D(this.registry.getTargetType(e),this.getItemType())&&t.canDrop(this,e)}isDragging(){return Boolean(this.getItemType())}isDraggingSource(e){if(!e)return!1;const t=this.registry.getSource(e,!0);if((0,l.invariant)(t,`Expected to find a valid source. sourceId=${e}`),!this.isDragging()||!this.isSourcePublic())return!1;return this.registry.getSourceType(e)===this.getItemType()&&t.isDragging(this,e)}isOverTarget(e,t={shallow:!1}){if(!e)return!1;const{shallow:r}=t;if(!this.isDragging())return!1;const n=this.registry.getTargetType(e),o=this.getItemType();if(o&&!D(n,o))return!1;const i=this.getTargetIds();if(!i.length)return!1;const s=i.indexOf(e);return r?s===i.length-1:s>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){
return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return Boolean(this.store.getState().dragOperation.isSourcePublic)}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return function(e){const{clientOffset:t,initialClientOffset:r,initialSourceClientOffset:n}=e;return t&&r&&n?P((i=n,{x:(o=t).x+i.x,y:o.y+i.y}),r):null;var o,i}(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return function(e){const{clientOffset:t,initialClientOffset:r}=e;return t&&r?P(t,r):null}(this.store.getState().dragOffset)}constructor(e,t){this.store=e,this.registry=t}}const R="undefined"!=typeof global?global:self,L=R.MutationObserver||R.WebKitMutationObserver;function _(e){return function(){const t=setTimeout(n,0),r=setInterval(n,50);function n(){clearTimeout(t),clearInterval(r),e()}}}const j="function"==typeof L?function(e){let t=1;const r=new L(e),n=document.createTextNode("");return r.observe(n,{characterData:!0}),function(){t=-t,n.data=t}}:_;class k{call(){try{this.task&&this.task()}catch(e){this.onError(e)}finally{this.task=null,this.release(this)}}constructor(e,t){this.onError=e,this.release=t,this.task=null}}const H=new class{enqueueTask(e){const{queue:t,requestFlush:r}=this;t.length||(r(),this.flushing=!0),t[t.length]=e}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:e}=this;for(;this.index<e.length;){const t=this.index;if(this.index++,e[t].call(),this.index>this.capacity){for(let t=0,r=e.length-this.index;t<r;t++)e[t]=e[t+this.index];e.length-=this.index,this.index=0}}e.length=0,this.index=0,this.flushing=!1},this.registerPendingError=e=>{this.pendingErrors.push(e),this.requestErrorThrow()},this.requestFlush=j(this.flush),this.requestErrorThrow=_((()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()}))}},U=new class{create(e){const t=this.freeTasks,r=t.length?t.pop():new k(this.onError,(e=>t[t.length]=e));return r.task=e,r}constructor(e){this.onError=e,this.freeTasks=[]}}(H.registerPendingError);const F="dnd-core/ADD_SOURCE",$="dnd-core/ADD_TARGET",B="dnd-core/REMOVE_SOURCE",q="dnd-core/REMOVE_TARGET";function K(e,t){t&&Array.isArray(e)?e.forEach((e=>K(e,!1))):(0,l.invariant)("string"==typeof e||"symbol"==typeof e,t?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var G;!function(e){e.SOURCE="SOURCE",e.TARGET="TARGET"}(G||(G={}));let W=0;function Y(e){const t=(W++).toString();switch(e){case G.SOURCE:return`S${t}`;case G.TARGET:return`T${t}`;default:throw new Error(`Unknown Handler Role: ${e}`)}}function z(e){switch(e[0]){case"S":return G.SOURCE;case"T":return G.TARGET;default:
throw new Error(`Cannot parse handler ID: ${e}`)}}function X(e,t){const r=e.entries();let n=!1;do{const{done:e,value:[,o]}=r.next();if(o===t)return!0;n=!!e}while(!n);return!1}class V{addSource(e,t){K(e),function(e){(0,l.invariant)("function"==typeof e.canDrag,"Expected canDrag to be a function."),(0,l.invariant)("function"==typeof e.beginDrag,"Expected beginDrag to be a function."),(0,l.invariant)("function"==typeof e.endDrag,"Expected endDrag to be a function.")}(t);const r=this.addHandler(G.SOURCE,e,t);return this.store.dispatch(function(e){return{type:F,payload:{sourceId:e}}}(r)),r}addTarget(e,t){K(e,!0),function(e){(0,l.invariant)("function"==typeof e.canDrop,"Expected canDrop to be a function."),(0,l.invariant)("function"==typeof e.hover,"Expected hover to be a function."),(0,l.invariant)("function"==typeof e.drop,"Expected beginDrag to be a function.")}(t);const r=this.addHandler(G.TARGET,e,t);return this.store.dispatch(function(e){return{type:$,payload:{targetId:e}}}(r)),r}containsHandler(e){return X(this.dragSources,e)||X(this.dropTargets,e)}getSource(e,t=!1){(0,l.invariant)(this.isSourceId(e),"Expected a valid source ID.");return t&&e===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(e)}getTarget(e){return(0,l.invariant)(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}getSourceType(e){return(0,l.invariant)(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}getTargetType(e){return(0,l.invariant)(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}isSourceId(e){return z(e)===G.SOURCE}isTargetId(e){return z(e)===G.TARGET}removeSource(e){var t;(0,l.invariant)(this.getSource(e),"Expected an existing source."),this.store.dispatch(function(e){return{type:B,payload:{sourceId:e}}}(e)),t=()=>{this.dragSources.delete(e),this.types.delete(e)},H.enqueueTask(U.create(t))}removeTarget(e){(0,l.invariant)(this.getTarget(e),"Expected an existing target."),this.store.dispatch(function(e){return{type:q,payload:{targetId:e}}}(e)),this.dropTargets.delete(e),this.types.delete(e)}pinSource(e){const t=this.getSource(e);(0,l.invariant)(t,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=t}unpinSource(){(0,l.invariant)(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(e,t,r){const n=Y(e);return this.types.set(n,t),e===G.SOURCE?this.dragSources.set(n,r):e===G.TARGET&&this.dropTargets.set(n,r),n}constructor(e){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=e}}const J=(e,t)=>e===t;function Q(e=x,t){switch(t.type){case p:break;case F:case $:case q:case B:return x;default:return M}const{targetIds:r=[],prevTargetIds:n=[]}=t.payload,o=function(e,t){const r=new Map,n=e=>{r.set(e,r.has(e)?r.get(e)+1:1)};e.forEach(n),t.forEach(n);const o=[];return r.forEach(((e,t)=>{1===e&&o.push(t)})),o}(r,n);if(!(o.length>0||!function(e,t,r=J){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!r(e[n],t[n]))return!1
;return!0}(r,n)))return x;const i=n[n.length-1],s=r[r.length-1];return i!==s&&(i&&o.push(i),s&&o.push(s)),o}function Z(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}const ee={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function te(e=ee,t){const{payload:r}=t;switch(t.type){case f:case h:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case p:return n=e.clientOffset,o=r.clientOffset,!n&&!o||n&&o&&n.x===o.x&&n.y===o.y?e:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){Z(e,t,r[t])}))}return e}({},e,{clientOffset:r.clientOffset});case y:case v:return ee;default:return e}var n,o}function re(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ne(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){re(e,t,r[t])}))}return e}const oe={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function ie(e=oe,t){const{payload:r}=t;switch(t.type){case h:return ne({},e,{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case g:return ne({},e,{isSourcePublic:!0});case p:return ne({},e,{targetIds:r.targetIds});case q:return-1===e.targetIds.indexOf(r.targetId)?e:ne({},e,{targetIds:(n=e.targetIds,o=r.targetId,n.filter((e=>e!==o)))});case v:return ne({},e,{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case y:return ne({},e,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}var n,o}function se(e=0,t){switch(t.type){case F:case $:return e+1;case B:case q:return e-1;default:return e}}function ae(e=0){return e+1}function ce(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ue(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter((function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})))),n.forEach((function(t){ce(e,t,r[t])}))}return e}function le(e={},t){return{dirtyHandlerIds:Q(e.dirtyHandlerIds,{type:t.type,payload:ue({},t.payload,{prevTargetIds:(r=e,n="dragOperation.targetIds",o=[],n.split(".").reduce(((e,t)=>e&&e[t]?e[t]:o||null),r))})}),dragOffset:te(e.dragOffset,t),refCount:se(e.refCount,t),dragOperation:ie(e.dragOperation,t),
stateId:ae(e.stateId)};var r,n,o}function de(e,t=void 0,r={},n=!1){const o=function(e){const t="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__;return u(le,e&&t&&t({name:"dnd-core",instanceId:"dnd-core"}))}(n),i=new A(o,new V(o)),s=new N(o,i),a=e(s,t,r);return s.receiveBackend(a),s}var fe=r(50959),he=r(79178);function ge(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}let pe=0;const ve=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var ye=(0,fe.memo)((function(e){var{children:t}=e,r=ge(e,["children"]);const[o,i]=function(e){if("manager"in e){return[{dragDropManager:e.manager},!1]}const t=function(e,t=me(),r,n){const o=t;o[ve]||(o[ve]={dragDropManager:de(e,t,r,n)});return o[ve]}(e.backend,e.context,e.options,e.debugMode),r=!e.context;return[t,r]}(r);return(0,fe.useEffect)((()=>{if(i){const e=me();return++pe,()=>{0==--pe&&(e[ve]=null)}}}),[]),(0,n.jsx)(he.DndContext.Provider,{value:o,children:t})}));function me(){return"undefined"!=typeof global?global:window}},50038:(e,t,r)=>{"use strict";r.d(t,{useCollectedProps:()=>i});var n=r(71690),o=r(52541);function i(e,t,r){return function(e,t,r){const[i,s]=(0,n.useCollector)(e,t,r);return(0,o.useIsomorphicLayoutEffect)((function(){const t=e.getHandlerId();if(null!=t)return e.subscribeToStateChange(s,{handlerIds:[t]})}),[e,s]),i}(t,e||(()=>({})),(()=>r.reconnect()))}},71690:(e,t,r)=>{"use strict";r.d(t,{useCollector:()=>s});var n=r(3341),o=r(50959),i=r(52541);function s(e,t,r){const[s,a]=(0,o.useState)((()=>t(e))),c=(0,o.useCallback)((()=>{const o=t(e);n(s,o)||(a(o),r&&r())}),[s,e,r]);return(0,i.useIsomorphicLayoutEffect)(c),[s,c]}},16812:(e,t,r)=>{"use strict";r.d(t,{useDrag:()=>O});var n=r(6346),o=r(50038),i=r(38088),s=r(50959);function a(e){return(0,s.useMemo)((()=>e.hooks.dragSource()),[e])}function c(e){return(0,s.useMemo)((()=>e.hooks.dragPreview()),[e])}var u=r(98789),l=r(15299),d=r(3632);class f{receiveHandlerId(e){this.handlerId!==e&&(this.handlerId=e,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(e){this.dragSourceOptionsInternal=e}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(e){this.dragPreviewOptionsInternal=e}reconnect(){const e=this.reconnectDragSource();this.reconnectDragPreview(e)}reconnectDragSource(){const e=this.dragSource,t=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();return t&&this.disconnectDragSource(),this.handlerId?e?(t&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=e,this.lastConnectedDragSourceOptions=this.dragSourceOptions,
this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,e,this.dragSourceOptions)),t):(this.lastConnectedDragSource=e,t):t}reconnectDragPreview(e=!1){const t=this.dragPreview,r=e||this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();r&&this.disconnectDragPreview(),this.handlerId&&(t?r&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=t,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,t,this.dragPreviewOptions)):this.lastConnectedDragPreview=t)}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!(0,u.shallowEqual)(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!(0,u.shallowEqual)(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}clearDragSource(){this.dragSourceNode=null,this.dragSourceRef=null}clearDragPreview(){this.dragPreviewNode=null,this.dragPreviewRef=null}constructor(e){this.hooks=(0,d.wrapConnectorHooks)({dragSource:(e,t)=>{this.clearDragSource(),this.dragSourceOptions=t||null,(0,l.isRef)(e)?this.dragSourceRef=e:this.dragSourceNode=e,this.reconnectDragSource()},dragPreview:(e,t)=>{this.clearDragPreview(),this.dragPreviewOptions=t||null,(0,l.isRef)(e)?this.dragPreviewRef=e:this.dragPreviewNode=e,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null,this.backend=e}}var h=r(68788),g=r(52541);let p=!1,v=!1;class y{receiveHandlerId(e){this.sourceId=e}getHandlerId(){return this.sourceId}canDrag(){(0,n.invariant)(!p,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return p=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{p=!1}}isDragging(){if(!this.sourceId)return!1;(0,n.invariant)(!v,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return v=!0,
this.internalMonitor.isDraggingSource(this.sourceId)}finally{v=!1}}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}isDraggingSource(e){return this.internalMonitor.isDraggingSource(e)}isOverTarget(e,t){return this.internalMonitor.isOverTarget(e,t)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(e){return this.internalMonitor.subscribeToOffsetChange(e)}canDragSource(e){return this.internalMonitor.canDragSource(e)}canDropOnTarget(e){return this.internalMonitor.canDropOnTarget(e)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.sourceId=null,this.internalMonitor=e.getMonitor()}}var m=r(95053);class S{beginDrag(){const e=this.spec,t=this.monitor;let r=null;return r="object"==typeof e.item?e.item:"function"==typeof e.item?e.item(t):{},null!=r?r:null}canDrag(){const e=this.spec,t=this.monitor;return"boolean"==typeof e.canDrag?e.canDrag:"function"!=typeof e.canDrag||e.canDrag(t)}isDragging(e,t){const r=this.spec,n=this.monitor,{isDragging:o}=r;return o?o(n):t===e.getSourceId()}endDrag(){const e=this.spec,t=this.monitor,r=this.connector,{end:n}=e;n&&n(t.getItem(),t),r.reconnect()}constructor(e,t,r){this.spec=e,this.monitor=t,this.connector=r}}function b(e,t,r){const o=(0,h.useDragDropManager)(),i=function(e,t,r){const n=(0,s.useMemo)((()=>new S(e,t,r)),[t,r]);return(0,s.useEffect)((()=>{n.spec=e}),[e]),n}(e,t,r),a=function(e){return(0,s.useMemo)((()=>{const t=e.type;return(0,n.invariant)(null!=t,"spec.type must be defined"),t}),[e])}(e);(0,g.useIsomorphicLayoutEffect)((function(){if(null!=a){const[e,n]=(0,m.registerSource)(a,i,o);return t.receiveHandlerId(e),r.receiveHandlerId(e),n}}),[o,t,r,i,a])}function O(e,t){const r=(0,i.useOptionalFactory)(e,t);(0,n.invariant)(!r.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");const u=function(){const e=(0,h.useDragDropManager)();return(0,s.useMemo)((()=>new y(e)),[e])}(),l=function(e,t){const r=(0,h.useDragDropManager)(),n=(0,s.useMemo)((()=>new f(r.getBackend())),[r]);return(0,g.useIsomorphicLayoutEffect)((()=>(n.dragSourceOptions=e||null,n.reconnect(),()=>n.disconnectDragSource())),[n,e]),(0,g.useIsomorphicLayoutEffect)((()=>(n.dragPreviewOptions=t||null,n.reconnect(),()=>n.disconnectDragPreview())),[n,t]),n
}(r.options,r.previewOptions);return b(r,u,l),[(0,o.useCollectedProps)(r.collect,u,l),a(l),c(l)]}},68788:(e,t,r)=>{"use strict";r.d(t,{useDragDropManager:()=>s});var n=r(6346),o=r(50959),i=r(79178);function s(){const{dragDropManager:e}=(0,o.useContext)(i.DndContext);return(0,n.invariant)(null!=e,"Expected drag drop context"),e}},65982:(e,t,r)=>{"use strict";r.d(t,{useDragLayer:()=>s});var n=r(50959),o=r(71690),i=r(68788);function s(e){const t=(0,i.useDragDropManager)().getMonitor(),[r,s]=(0,o.useCollector)(t,e);return(0,n.useEffect)((()=>t.subscribeToOffsetChange(s))),(0,n.useEffect)((()=>t.subscribeToStateChange(s))),r}},69690:(e,t,r)=>{"use strict";r.d(t,{useDrop:()=>S});var n=r(50038),o=r(38088),i=r(50959);function s(e){return(0,i.useMemo)((()=>e.hooks.dropTarget()),[e])}var a=r(98789),c=r(15299),u=r(3632);class l{get connectTarget(){return this.dropTarget}reconnect(){const e=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();e&&this.disconnectDropTarget();const t=this.dropTarget;this.handlerId&&(t?e&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=t,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,t,this.dropTargetOptions)):this.lastConnectedDropTarget=t)}receiveHandlerId(e){e!==this.handlerId&&(this.handlerId=e,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(e){this.dropTargetOptionsInternal=e}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!(0,a.shallowEqual)(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}clearDropTarget(){this.dropTargetRef=null,this.dropTargetNode=null}constructor(e){this.hooks=(0,u.wrapConnectorHooks)({dropTarget:(e,t)=>{this.clearDropTarget(),this.dropTargetOptions=t,(0,c.isRef)(e)?this.dropTargetRef=e:this.dropTargetNode=e,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null,this.backend=e}}var d=r(68788),f=r(52541);var h=r(6346);let g=!1;class p{receiveHandlerId(e){this.targetId=e}getHandlerId(){return this.targetId}subscribeToStateChange(e,t){return this.internalMonitor.subscribeToStateChange(e,t)}canDrop(){if(!this.targetId)return!1;(0,h.invariant)(!g,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return g=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{g=!1}}isOver(e){return!!this.targetId&&this.internalMonitor.isOverTarget(this.targetId,e)}getItemType(){
return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(e){this.targetId=null,this.internalMonitor=e.getMonitor()}}var v=r(95053);class y{canDrop(){const e=this.spec,t=this.monitor;return!e.canDrop||e.canDrop(t.getItem(),t)}hover(){const e=this.spec,t=this.monitor;e.hover&&e.hover(t.getItem(),t)}drop(){const e=this.spec,t=this.monitor;if(e.drop)return e.drop(t.getItem(),t)}constructor(e,t){this.spec=e,this.monitor=t}}function m(e,t,r){const n=(0,d.useDragDropManager)(),o=function(e,t){const r=(0,i.useMemo)((()=>new y(e,t)),[t]);return(0,i.useEffect)((()=>{r.spec=e}),[e]),r}(e,t),s=function(e){const{accept:t}=e;return(0,i.useMemo)((()=>((0,h.invariant)(null!=e.accept,"accept must be defined"),Array.isArray(t)?t:[t])),[t])}(e);(0,f.useIsomorphicLayoutEffect)((function(){const[e,i]=(0,v.registerTarget)(s,o,n);return t.receiveHandlerId(e),r.receiveHandlerId(e),i}),[n,t,o,r,s.map((e=>e.toString())).join("|")])}function S(e,t){const r=(0,o.useOptionalFactory)(e,t),a=function(){const e=(0,d.useDragDropManager)();return(0,i.useMemo)((()=>new p(e)),[e])}(),c=function(e){const t=(0,d.useDragDropManager)(),r=(0,i.useMemo)((()=>new l(t.getBackend())),[t]);return(0,f.useIsomorphicLayoutEffect)((()=>(r.dropTargetOptions=e||null,r.reconnect(),()=>r.disconnectDropTarget())),[e]),r}(r.options);return m(r,a,c),[(0,n.useCollectedProps)(r.collect,a,c),s(c)]}},52541:(e,t,r)=>{"use strict";r.d(t,{useIsomorphicLayoutEffect:()=>o});var n=r(50959);const o="undefined"!=typeof window?n.useLayoutEffect:n.useEffect},38088:(e,t,r)=>{"use strict";r.d(t,{useOptionalFactory:()=>o});var n=r(50959);function o(e,t){const r=[...t||[]];return null==t&&"function"!=typeof e&&r.push(e),(0,n.useMemo)((()=>"function"==typeof e?e():e),r)}},15299:(e,t,r)=>{"use strict";function n(e){return null!==e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}r.d(t,{isRef:()=>n})},95053:(e,t,r)=>{"use strict";function n(e,t,r){const n=r.getRegistry(),o=n.addTarget(e,t);return[o,()=>n.removeTarget(o)]}function o(e,t,r){const n=r.getRegistry(),o=n.addSource(e,t);return[o,()=>n.removeSource(o)]}r.d(t,{registerSource:()=>o,registerTarget:()=>n})},3632:(e,t,r)=>{"use strict";r.d(t,{wrapConnectorHooks:()=>s});var n=r(6346),o=r(50959);function i(e){return(t=null,r=null)=>{if(!(0,o.isValidElement)(t)){const n=t;return e(n,r),n}const i=t;!function(e){if("string"==typeof e.type)return;const t=e.type.displayName||e.type.name||"the component"
;throw new Error(`Only native element nodes can now be passed to React DnD connectors.You can either wrap ${t} into a <div>, or turn it into a drag source or a drop target itself.`)}(i);return function(e,t){const r=e.ref;return(0,n.invariant)("string"!=typeof r,"Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),r?(0,o.cloneElement)(e,{ref:e=>{a(r,e),a(t,e)}}):(0,o.cloneElement)(e,{ref:t})}(i,r?t=>e(t,r):e)}}function s(e){const t={};return Object.keys(e).forEach((r=>{const n=e[r];if(r.endsWith("Ref"))t[r]=e[r];else{const e=i(n);t[r]=()=>e}})),t}function a(e,t){"function"==typeof e?e(t):e.current=t}}}]);