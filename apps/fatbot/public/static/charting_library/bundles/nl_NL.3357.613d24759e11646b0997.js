(self.webpackChunktradingview=self.webpackChunktradingview||[]).push([[3357,4109,9093],{53288:e=>{e.exports={en:["animals & nature"]}},21311:e=>{e.exports={en:["activity"]}},90678:e=>{e.exports={en:["food & drink"]}},6715:e=>{e.exports={en:["flags"]}},98355:e=>{e.exports={en:["objects"]}},43438:e=>{e.exports={en:["smiles & people"]}},40457:e=>{e.exports={en:["symbols"]}},88906:e=>{e.exports={en:["recently used"]}},28562:e=>{e.exports={en:["travel & places"]}},4543:e=>{e.exports={en:["Cancel"],nl_NL:["Annuleren"]}},47742:e=>{e.exports={en:["Close menu"]}},69207:e=>{e.exports={en:["Add to favorites"],nl_NL:["Voeg toe aan favorieten"]}},80439:e=>{e.exports={en:["Do you really want to delete chart layout '{name}' ? This can't be undone. Your drawings and this layout will be gone forever."]}},56495:e=>{e.exports={en:["Do you really want to delete chart layout '{name}' that contains {n_drawings_on_n_symbols}? This can't be undone. Your drawings and this layout will be gone forever."]}},55108:e=>{e.exports={en:["Date modified (oldest first)"]}},75272:e=>{e.exports={en:["Date modified (newest first)"]}},11478:e=>{e.exports={en:["Layout name"]}},21329:e=>{e.exports={en:["Layout name (A to Z)"]}},11324:e=>{e.exports={en:["Layout name (Z to A)"]}},21355:e=>{e.exports={en:["Layouts"]}},63269:e=>{e.exports={en:["No layouts matched your criteria"]}},5191:e=>{e.exports={en:["Sort by layout name, date changed"]}},8573:e=>{e.exports={en:["Search"],nl_NL:["Zoeken"]}},85106:e=>{e.exports={en:["Remove from favorites"],nl_NL:["Verwijder van favorieten"]}},47550:e=>{e.exports={en:["Remove selected emoji"]}},30502:e=>{e.exports={en:["on {amount} symbol","on {amount} symbols"]}}}]);