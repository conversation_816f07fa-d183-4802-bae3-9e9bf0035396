import type { SVGProps } from 'react';

export const EthIcon = (props: SVGProps<SVGSVGElement>) => (
    <svg viewBox="0 0 2500 2503" xmlSpace="preserve" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g>
        <rect fill="none" height="2499.9" width="2499.9" x="0.1" y="1.6" />
        <g>
          <polygon
            points="1248.7,2501.4 1248.7,1874.2 472.8,1420.5"
            style={{
              fill: '#F0CDC2',
              stroke: '#1616B4',
              strokeWidth: 3.1298,
              strokeLinejoin: 'round',
              strokeMiterlimit: 22.9245,
            }}
          />
          <polygon
            points="1251.3,2501.4 1251.3,1874.2 2027.1,1420.5"
            style={{
              fill: '#C9B3F5',
              stroke: '#1616B4',
              strokeWidth: 3.1298,
              strokeLinejoin: 'round',
              strokeMiterlimit: 22.9245,
            }}
          />
          <polygon
            points="1248.7,1718.4 1248.7,917.9 464,1269.3"
            style={{
              fill: '#88AAF1',
              stroke: '#1616B4',
              strokeWidth: 3.1298,
              strokeLinejoin: 'round',
              strokeMiterlimit: 22.9245,
            }}
          />
          <polygon
            points="1251.3,1718.4 1251.3,917.9 2036,1269.3"
            style={{
              fill: '#C9B3F5',
              stroke: '#1616B4',
              strokeWidth: 3.1298,
              strokeLinejoin: 'round',
              strokeMiterlimit: 22.9245,
            }}
          />
          <polygon
            points="464,1269.3 1248.7,1.6 1248.7,917.9"
            style={{
              fill: '#F0CDC2',
              stroke: '#1616B4',
              strokeWidth: 3.1298,
              strokeLinejoin: 'round',
              strokeMiterlimit: 22.9245,
            }}
          />
          <polygon
            points="2036,1269.3 1251.3,1.6 1251.3,917.9"
            style={{
              fill: '#B8FAF6',
              stroke: '#1616B4',
              strokeWidth: 3.1298,
              strokeLinejoin: 'round',
              strokeMiterlimit: 22.9245,
            }}
          />
        </g>
      </g>
    </svg>
  );
