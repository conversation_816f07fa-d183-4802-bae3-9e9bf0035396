'use client';

import { ROUTES } from '@/constants/routes';
import { isClient } from '@/lib/is-client';

export type SessionStorageKeys = 'prevUrl' | 'streak-dialogs-store' | 'token-detail-chart-key';

export interface SessionStorageClient<KeyType extends string> {
  setItem: (key: KeyType, value: string) => void;
  getItem: (key: KeyType) => string | null | undefined;
  removeItem: (key: KeyType) => void;
  clear: () => void;
}

export function getSessionStorageClient<KeyType extends string>(): SessionStorageClient<KeyType> {
  function setItem(key: KeyType, value: string) {
    if (isClient) {
      sessionStorage.setItem(key, value);
    }
  }

  function getItem(key: KeyType) {
    return isClient ? (sessionStorage.getItem(key) as string | null | undefined) : undefined;
  }

  function removeItem(key: KeyType) {
    if (isClient) {
      sessionStorage.removeItem(key);
    }
  }

  function clear() {
    if (isClient) {
      sessionStorage.clear();
    }
  }

  return { setItem, getItem, removeItem, clear };
}

export const sessionStorageClient = getSessionStorageClient<SessionStorageKeys>();

export const getPreviousRoute = (defaultRoute = ROUTES.HOME): string =>
  sessionStorageClient.getItem('prevUrl') ?? defaultRoute;
