import * as Sentry from '@sentry/nextjs';
import Axios, { type AxiosRequestConfig, type AxiosError } from 'axios';
import { redirect } from 'next/navigation';

import { ROUTES } from '@/constants/routes';
import { env } from '@/env/client';
import { getTokens } from '@/lib/firebase/get-tokens';
export const AXIOS_INSTANCE = Axios.create({
  baseURL: env.NEXT_PUBLIC_API_BASE_URL,
});

const HTTP_STATUS_UNAUTHORIZED = 401;

AXIOS_INSTANCE.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    if (error.response?.status === HTTP_STATUS_UNAUTHORIZED) {
      const isServer = typeof window === 'undefined';

      Sentry.captureMessage('Unauthorized API request', {
        level: 'warning',
        extra: {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response.status,
          pathname: isServer ? 'server-side' : window.location.pathname,
        },
      });

      if (!isServer) {
        if (window.location.pathname !== ROUTES.LOGOUT) {
          window.location.href = ROUTES.LOGOUT;
        }
      } else {
        return redirect(ROUTES.LOGOUT);
      }
    }

    return Promise.reject(error);
  },
);

export const customInstance = async <T>(config: AxiosRequestConfig, options?: AxiosRequestConfig): Promise<T> => {
  const getTokensResult = await getTokens();

  const source = Axios.CancelToken.source();
  const promise = AXIOS_INSTANCE<T>({
    ...config,
    ...options,
    cancelToken: source.token,
    paramsSerializer: { indexes: null },
    headers: {
      // eslint-disable-next-line @typescript-eslint/no-misused-spread -- Spread is fine here
      ...options?.headers,
      ...(config.url &&
        !config.url.includes('/public') && {
          Authorization: `Bearer ${getTokensResult?.token}`,
        }),
    },
  }).then((response) => response.data);

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment -- TODO Investigate why this was here in the first place
  // @ts-expect-error
  promise.cancel = () => {
    source.cancel('Query was cancelled');
  };

  return promise;
};
