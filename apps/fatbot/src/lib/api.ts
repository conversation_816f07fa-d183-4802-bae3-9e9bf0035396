/**
 * Generated by orval v7.5.0 🍺
 * Do not edit manually.
 * Fatbot API
 * Fatbot documentation. Last built at: 2025-06-14T11:22:13Z
 * OpenAPI spec version: v4.0.0+6ab6629
 */
import { useInfiniteQuery, useMutation, useQuery } from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseInfiniteQueryResult,
  DefinedUseQueryResult,
  InfiniteData,
  MutationFunction,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';
import { customInstance } from './axios-instance';
export type Chain = (typeof Chain)[keyof typeof Chain];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const Chain = {
  EVM_MAINNET: 'EVM_MAINNET',
  EVM_BASE: 'EVM_BASE',
  EVM_BSC: 'EVM_BSC',
  EVM_ARBITRUM_ONE: 'EVM_ARBITRUM_ONE',
  SOLANA: 'SOLANA',
} as const;

export interface UpdateSelectedChainsRequest {
  selectedChains: Chain[];
}

export interface UpdateTokenImageRequest {
  imageUrl: string;
  chain: Chain;
}

export interface SearchUserTokensTransactionRequestV3 {
  walletId?: string;
  searchString?: string;
  useSelectedChains: boolean;
  allowedStatuses: TransactionStatus[];
}

export type TransactionStatus = (typeof TransactionStatus)[keyof typeof TransactionStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TransactionStatus = {
  PENDING: 'PENDING',
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED',
  NOT_LANDED: 'NOT_LANDED',
} as const;

export interface InfiniteScrollSliceSearchUserTransactionResultUUID {
  content: SearchUserTransactionResult[];
  lastId?: string;
  hasMore: boolean;
}

export interface SearchUserTransactionResult {
  txId: string;
  txHash: string;
  txDetailUrl: string;
  transactionType: TransactionType;
  transactionStatus: TransactionStatus;
  txFailReason?: TransactionFailureReason;
  walletId: string;
  walletCustomName?: string;
  createdAt: string;
  tokenAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
  tokenImageUrl?: string;
  tokenNativeAmount?: string;
  currencyNativeAmount?: string;
  currencyAmountUsd?: string;
  transactionChain: Chain;
}

export type TransactionFailureReason = (typeof TransactionFailureReason)[keyof typeof TransactionFailureReason];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TransactionFailureReason = {
  SLIPPAGE: 'SLIPPAGE',
  RETRY_LIMIT_EXCEEDED: 'RETRY_LIMIT_EXCEEDED',
  UNDEFINED: 'UNDEFINED',
} as const;

export type TransactionType = (typeof TransactionType)[keyof typeof TransactionType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TransactionType = {
  BUY: 'BUY',
  SELL: 'SELL',
  TRANSFER_CURRENCY: 'TRANSFER_CURRENCY',
  TRANSFER_TOKEN: 'TRANSFER_TOKEN',
  CLAIM_REFERRAL_REWARD: 'CLAIM_REFERRAL_REWARD',
  APPROVE: 'APPROVE',
} as const;

export interface SearchUserTokenRequestV3 {
  useSelectedChains: boolean;
  walletId?: string;
  searchString?: string;
}

export interface InfiniteScrollSliceSearchUserMarketPositionResultUUID {
  content: SearchUserMarketPositionResult[];
  lastId?: string;
  hasMore: boolean;
}

export interface SearchUserMarketPositionResult {
  id: string;
  tokenAddress: string;
  tokenDetailUrl: string;
  tokenName: string;
  tokenSymbol: string;
  tokenChain: Chain;
  tokenImageUrl?: string;
  pricePerTokenInUsd: string;
  tokenNativeAmount: string;
  oneHourChangeFraction?: string;
  oneDayChangeFraction?: string;
  currentValueUsd: string;
  currentValueChangeUsd: string;
  currentValueChangeFraction: string;
}

export interface CreateNewUserWalletRequestV2 {
  chain: Chain;
}

export interface CreateNewUserWalletResult {
  walletId: string;
  walletAddress: string;
  privateKey: string;
}

export interface TransferTokenRequestV2 {
  tokenAddress: string;
  destinationWalletAddress: string;
  toTransferNativeAmount: string;
  password: string;
}

export interface TransferTokenResult {
  txHash: string;
}

export type Dex = (typeof Dex)[keyof typeof Dex];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const Dex = {
  UNISWAP_V2: 'UNISWAP_V2',
  PANCAKESWAP_V2: 'PANCAKESWAP_V2',
  UNISWAP_V3: 'UNISWAP_V3',
  PANCAKESWAP_V3: 'PANCAKESWAP_V3',
  PUMP_FUN: 'PUMP_FUN',
  PUMP_SWAP: 'PUMP_SWAP',
  RAYDIUM: 'RAYDIUM',
  METEORA: 'METEORA',
} as const;

export interface DexPairInfoInputV2 {
  pairAddress: string;
  dex: Dex;
  fee?: UniswapV3Fee;
  poolType?: PoolType;
}

export type PoolType = (typeof PoolType)[keyof typeof PoolType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PoolType = {
  AMM: 'AMM',
  CLMM: 'CLMM',
  CPMM: 'CPMM',
} as const;

export interface SellTokenRequestV2 {
  tokenAddress: string;
  toSellNativeAmount: string;
  dexPairInfo: DexPairInfoInputV2;
}

export type UniswapV3Fee = (typeof UniswapV3Fee)[keyof typeof UniswapV3Fee];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const UniswapV3Fee = {
  FEE_100: 'FEE_100',
  FEE_500: 'FEE_500',
  FEE_2500: 'FEE_2500',
  FEE_3000: 'FEE_3000',
  FEE_10000: 'FEE_10000',
} as const;

export interface SellTokenResult {
  txHashes: string[];
}

export interface GetMaxBuyRequestV2 {
  tokenAddress: string;
  dexPairInfo: DexPairInfoInputV2;
}

export interface GetTokenMaxBuyResult {
  maxBuy?: string;
}

export interface BuyTokenRequestV2 {
  tokenAddress: string;
  buyForNativeAmount: string;
  dexPairInfo: DexPairInfoInputV2;
}

export interface BuyTokenResult {
  txHash: string;
}

export interface TransferCurrencyRequestV2 {
  destinationWalletAddress: string;
  toTransferNativeAmount: string;
  password: string;
}

export interface TransferCurrencyResult {
  txHash: string;
}

export interface GetAllUserWalletsRequestV2 {
  chains: Chain[];
}

export interface GetAllUserWalletsResult {
  walletId: string;
  isDefault: boolean;
  walletAddress: string;
  walletDetailUrl: string;
  walletBalance: string;
  walletBalanceUsd: string;
  customName?: string;
  chain: Chain;
  currentPortfolioValueUsd: string;
  currentPortfolioValueChangeUsd: string;
  currentPortfolioValueChangeFraction: string;
}

export interface ImportUserWalletRequestV2 {
  privateKey: string;
  chain: Chain;
}

export interface ImportUserWalletResult {
  walletId: string;
  walletAddress: string;
  privateKey: string;
}

export interface SearchUserTokenTransactionsRequestV2 {
  chain: Chain;
  walletId?: string;
  allowedStatuses: TransactionStatus[];
}

export interface SearchUserTokensTransactionRequestV2 {
  chains: Chain[];
  walletId?: string;
  searchString?: string;
  allowedStatuses: TransactionStatus[];
}

export interface TrackTokenResult {
  result: TrackTokenResult;
}

export interface QuickBuyTokenRequestV2 {
  chain: Chain;
}

export interface QuickBuyTokenResult {
  txHash: string;
}

export interface SearchUserTokenRequestV2 {
  chains: Chain[];
  walletId?: string;
  searchString?: string;
}

export interface GetGasEstimationUsdRequestV2 {
  transactionType: TransactionType;
  walletId: string;
  tokenAddress?: string;
  nativeAmount?: string;
  dexPairInfoInput?: DexPairInfoInputV2;
  recipient?: string;
}

export interface GetGasEstimationUsdResult {
  gasFeeUsd?: string;
}

export interface ExportWalletRequest {
  password: string;
}

export interface ExportUserWalletResult {
  privateKey: string;
}

export interface CreateNewReferralCodeRequest {
  referralCode: string;
}

export interface ClaimReferralRewardsResult {
  claimedAmountNative: string;
  txHash: string;
}

export interface CreateLimitOrderRequest {
  walletId: string;
  tokenAddress: string;
  chain: Chain;
  limitPrice: string;
  initialAmount: string;
  type: LimitOrderType;
}

export type LimitOrderType = (typeof LimitOrderType)[keyof typeof LimitOrderType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const LimitOrderType = {
  BUY: 'BUY',
  SELL: 'SELL',
} as const;

export interface SearchUserFattyCardRequest {
  claimed?: boolean;
  displayed?: boolean;
}

export interface SearchUserFattyCardsResult {
  userFattyCardId: string;
  avatarFileId: string;
  rarity: string;
  probability: string;
  donutReward: string;
}

export interface DisplayUserFattyCardsRequest {
  userFattyCardIds: string[];
}

export interface WithdrawFromBotPortfolioRequest {
  nativeAmount: string;
  percentageOf: string;
  recipientAddress: string;
}

export interface WithdrawFromBotPortfolioResult {
  txHash: string;
}

export interface GetBotWithdrawGasEstimationRequest {
  nativeAmount: string;
  recipientAddress: string;
}

export interface GetBotWithdrawGasEstimationResult {
  gasFeeUsd?: string;
}

export interface SearchUserBotsTransactionsRequest {
  botId: string;
  searchString?: string;
}

export type BotTransactionType = (typeof BotTransactionType)[keyof typeof BotTransactionType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const BotTransactionType = {
  BUY: 'BUY',
  SELL: 'SELL',
  DEPOSIT: 'DEPOSIT',
  PORTFOLIO_WITHDRAW: 'PORTFOLIO_WITHDRAW',
  APPROVE: 'APPROVE',
} as const;

export interface InfiniteScrollSliceSearchUserBotsTransactionsResultUUID {
  content: SearchUserBotsTransactionsResult[];
  lastId?: string;
  hasMore: boolean;
}

export interface SearchUserBotsTransactionsResult {
  botId: string;
  botName: string;
  txId: string;
  txHash: string;
  txDetailUrl: string;
  txType: BotTransactionType;
  txStatus: TransactionStatus;
  txCreatedAt: string;
  txChain: Chain;
  walletId: string;
  tokenAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
  nativeAmount?: string;
  amountUsd?: string;
  percentageOf?: string;
}

export interface SearchUserBotDetailRequest {
  botId: string;
  timeRange: TimeRange;
}

export type TimeRange = (typeof TimeRange)[keyof typeof TimeRange];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TimeRange = {
  HOUR: 'HOUR',
  DAY: 'DAY',
  WEEK: 'WEEK',
  MONTH: 'MONTH',
  YEAR: 'YEAR',
  ALL: 'ALL',
} as const;

export type BotMarketPositionState = (typeof BotMarketPositionState)[keyof typeof BotMarketPositionState];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const BotMarketPositionState = {
  OPENED: 'OPENED',
  PENDING_CLOSED: 'PENDING_CLOSED',
  PENDING_CLOSED_FROM_PROMOTED: 'PENDING_CLOSED_FROM_PROMOTED',
  PENDING_CLOSED_FROM_STALE: 'PENDING_CLOSED_FROM_STALE',
  PENDING_CLOSED_FROM_RED_FLAG: 'PENDING_CLOSED_FROM_RED_FLAG',
  PENDING_CLOSED_FROM_FORCE_SELL: 'PENDING_CLOSED_FROM_FORCE_SELL',
  CONFIRMED_CLOSED: 'CONFIRMED_CLOSED',
  OPEN_NOT_LANDED: 'OPEN_NOT_LANDED',
  OPEN_FAILED: 'OPEN_FAILED',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

export interface BotPortfolioPastValue {
  portfolioValueUsd: string;
  createdAt: string;
}

export type BotStatus = (typeof BotStatus)[keyof typeof BotStatus];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const BotStatus = {
  INSUFFICIENT_BALANCE: 'INSUFFICIENT_BALANCE',
  INSUFFICIENT_BALANCE_FUNDS_IN_OPEN_POSITIONS: 'INSUFFICIENT_BALANCE_FUNDS_IN_OPEN_POSITIONS',
  DEACTIVATED: 'DEACTIVATED',
  BUY_DAILY_LIMIT_REACHED: 'BUY_DAILY_LIMIT_REACHED',
  NO_PURCHASE: 'NO_PURCHASE',
  HAS_PURCHASED: 'HAS_PURCHASED',
} as const;

export interface SearchBotMarketPositionResult {
  id: string;
  state: BotMarketPositionState;
  tokenAddress: string;
  tokenDetailUrl: string;
  tokenName: string;
  tokenSymbol: string;
  tokenChain: Chain;
  tokenImageUrl?: string;
  openValueUsd: string;
  closeValueUsd?: string;
  openTimeStampAt: string;
  closedTimeStampAt?: string;
  pnlAmountUsd: string;
  pnlAmountFraction: string;
  currentValueUsd: string;
}

export interface SearchUserBotDetailResult {
  botId: string;
  botWalletAddress: string;
  botWalletChain: Chain;
  botWalletBalanceUsd: string;
  botName: string;
  isActive: boolean;
  numberOfActiveDays: number;
  botAvatarFileId: string;
  buyFrequency: string;
  remainingBuyFrequency: string;
  buyFrequencyLastResetAt: string;
  botTotalValueAmountUsd: string;
  botTotalPnlAmountUsd: string;
  botTotalPnlAmountFraction: string;
  botPortfolioLastValues: BotPortfolioPastValue[];
  transactionsCount: number;
  buyTransactionsCount: number;
  profitableTransactionsCount: number;
  lossTransactionsCount: number;
  transactionsVolumeSum: string;
  activeBotMarketPositions: SearchBotMarketPositionResult[];
  closedBotMarketPositions: SearchBotMarketPositionResult[];
  botTransactions: SearchUserBotsTransactionsResult[];
  botStatus: BotStatus;
}

export interface SearchUserBotMarketPositionsRequest {
  botId?: string;
  searchString?: string;
}

export interface InfiniteScrollSliceSearchBotMarketPositionResultUUID {
  content: SearchBotMarketPositionResult[];
  lastId?: string;
  hasMore: boolean;
}

export interface GetTokenTaxRequestV2 {
  chain: Chain;
  dexPairInfo: DexPairInfoInputV2;
}

export interface GetTokenTaxResult {
  buyTax?: string;
  sellTax?: string;
  isHoneypot: boolean;
}

export interface GetPredictedEthAmountOnSellRequestV2 {
  toSellTokenNativeAmount: string;
  chain: Chain;
  dexPairInfo: DexPairInfoInputV2;
}

export interface GetPredictedNativeAmountOnSellResult {
  predictedCurrencyNativeAmount: string;
}

export interface GetPredictedTokenAmountOnBuyRequestV2 {
  chain: Chain;
  buyForCurrencyNativeAmount: string;
  dexPairInfo: DexPairInfoInputV2;
}

export interface GetPredictedTokenAmountOnBuyResult {
  predictedTokenNativeAmount: string;
}

export type CandleChartTimeRange = (typeof CandleChartTimeRange)[keyof typeof CandleChartTimeRange];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CandleChartTimeRange = {
  ONE_SECOND: 'ONE_SECOND',
  THREE_SECONDS: 'THREE_SECONDS',
  FIVE_SECONDS: 'FIVE_SECONDS',
  FIFTEEN_SECONDS: 'FIFTEEN_SECONDS',
  THIRTY_SECONDS: 'THIRTY_SECONDS',
  ONE_MINUTE: 'ONE_MINUTE',
  THREE_MINUTES: 'THREE_MINUTES',
  FIVE_MINUTES: 'FIVE_MINUTES',
  FIFTEEN_MINUTES: 'FIFTEEN_MINUTES',
  THIRTY_MINUTES: 'THIRTY_MINUTES',
  FORTY_FIVE_MINUTES: 'FORTY_FIVE_MINUTES',
  ONE_HOUR: 'ONE_HOUR',
  TWO_HOURS: 'TWO_HOURS',
  THREE_HOURS: 'THREE_HOURS',
  FOUR_HOURS: 'FOUR_HOURS',
  ONE_DAY: 'ONE_DAY',
} as const;

export interface GetTokenHistoricalCandleChartRequest {
  timeRange: CandleChartTimeRange;
  chain: Chain;
  before?: string;
  after?: string;
}

export interface TokenOHLCTimeIntervalItem {
  timestamp: string;
  openUsd: string;
  openNative: string;
  highUsd: string;
  lowUsd: string;
  closeUsd: string;
  closeNative: string;
  volumeUsd: string;
}

export interface GetTokenPriceChartRequestV2 {
  timeRange: TimeRange;
  chain: Chain;
}

export interface GetTokenPriceChartResult {
  data: TokenPriceTimeIntervalItem[];
}

export interface TokenPriceTimeIntervalItem {
  timestamp: string;
  close: string;
  closeNative: string;
  volume: string;
}

export interface GetContinuousTokenPriceChartResult {
  data: TokenPriceTimeIntervalItem[];
}

export type BotSortParameter = (typeof BotSortParameter)[keyof typeof BotSortParameter];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const BotSortParameter = {
  PROFIT_PERCENTAGE: 'PROFIT_PERCENTAGE',
  PROFIT_USD: 'PROFIT_USD',
  WALLET_BALANCE: 'WALLET_BALANCE',
} as const;

export interface BotsLeaderboardSearchRequest {
  searchString?: string;
  allBots: boolean;
  timeRange: TimeRange;
  sortParameter: BotSortParameter;
}

export interface BotsLeaderboardSearchResult {
  botId: string;
  botName: string;
  botAvatarFileId: string;
  profitUsd: string;
  profitPercentage: string;
  walletBalance: string;
  daysActive: number;
  buyTransactionsCount: number;
  sellTransactionsCount: number;
}

export interface InfiniteScrollSliceBotsLeaderboardSearchResultBigDecimal {
  content: BotsLeaderboardSearchResult[];
  lastId?: string;
  hasMore: boolean;
}

export interface NewState {
  tgeBlockSlot: number;
  tokenAddress: string;
  creator: string;
  name: string;
  symbol: string;
  infoUrl?: string;
  marketCapUsd: string;
  liquidityUsd: string;
  volumeUsd: string;
  numOfAccountHolders: number;
  buyVolume: string;
  sellVolume: string;
  tokenReserves: string;
  solanaReserves: string;
  fractionOfSellTransactions: string;
  creatorGraduationSuccessRateFraction: string;
  numOfGraduatedTokensByCreator: string;
  numOfLaunchedTokensByCreator: string;
  isRedFlagTokenTickerCopy: boolean;
  isRedFlagCreatorHighBuy: boolean;
  isRedFlagBundledBuysDetected: boolean;
  isRedFlagSuspiciousWalletsDetected: boolean;
  isRedFlagSingleHighBuy: boolean;
}

export interface TokenStateChangesRequest {
  blockSlot: number;
  states: NewState[];
}

export type SolanaTokenRedFlag = (typeof SolanaTokenRedFlag)[keyof typeof SolanaTokenRedFlag];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SolanaTokenRedFlag = {
  TICKER_COPY: 'TICKER_COPY',
  CREATOR_HIGH_BUY: 'CREATOR_HIGH_BUY',
  BUNDLE_DETECTED: 'BUNDLE_DETECTED',
  SUSPICIOUS_WALLETS_DETECTED: 'SUSPICIOUS_WALLETS_DETECTED',
  SINGLE_HIGH_BUY: 'SINGLE_HIGH_BUY',
} as const;

export interface TokenRedFlaggedRequest {
  blockSlot: number;
  tokenAddress: string;
  creator: string;
  type: SolanaTokenRedFlag;
  marketCapUsd: string;
  liquidityUsd: string;
  volumeUsd: string;
  numOfAccountHolders: number;
  buyVolume: string;
  sellVolume: string;
  fractionOfSellTransactions: string;
}

export interface TokenPromotedToPumpswapRequest {
  blockSlot: number;
  tokenAddress: string;
  creator: string;
  pairAddress: string;
}

export interface Result {
  botId: string;
}

export interface SearchBotMarketPositionRequest {
  timeRange: TimeRange;
  searchString?: string;
  isBotMarketPositionActive: boolean;
}

export interface ResetBotBuyFrequencyRequest {
  botId: string;
}

export interface BotsOverviewRequest {
  timeRange: TimeRange;
}

export interface BotsOverviewResult {
  userId: string;
  botsTotalValueAmountUsd: string;
  botsTotalPnlAmountUsd: string;
  botsTotalPnlAmountFraction: string;
  botsOneDayChangeAmountUsd: string;
  botsOneDayChangeFraction: string;
  botsPortfolioValueSumPastValues: BotPortfolioPastValue[];
  botsTransactionsCount: number;
  botsBuyTransactionsCount: number;
  botsProfitableTransactionsCount: number;
  botsLossTransactionsCount: number;
  botsTransactionsVolumeSum: string;
}

export interface CompareBotsRequest {
  botIds: string[];
  timeRange: TimeRange;
}

export interface CompareBotsResult {
  botId: string;
  botName: string;
  isActive: boolean;
  numberOfActiveDays: number;
  botAvatarFileId: string;
  buyFrequency: string;
  remainingBuyFrequency: string;
  buyFrequencyLastResetAt: string;
  botTotalValueAmountUsd: string;
  botTotalPnlAmountUsd: string;
  botTotalPnlAmountFraction: string;
  botPortfolioLastValues: BotPortfolioPastValue[];
  transactionsCount: number;
  buyTransactionsCount: number;
  profitableTransactionsCount: number;
  lossTransactionsCount: number;
  transactionsVolumeSum: string;
  botStatus: BotStatus;
  activeBotMarketPositions: SearchBotMarketPositionResult[];
  closedBotMarketPositions: SearchBotMarketPositionResult[];
  botTransactions: SearchUserBotsTransactionsResult[];
}

export interface CreateBotDraftResult {
  botDraftId: string;
}

export interface PatchUserWalletRequest {
  customName?: string;
  buyAntiMevProtection?: boolean;
  sellAntiMevProtection?: boolean;
}

export interface PatchBotSettingsRequest {
  /** Non-Nullable - `null` indicates no update */
  name?: string;
  /** Non-Nullable - `null` indicates no update */
  avatarFileId?: string;
  tradeAmount?: string;
  buyFrequency?: string;
  profitTargetFraction?: string;
  stopLossFraction?: string;
  /** Non-Nullable - `null` indicates no update */
  tokenTickerCopyIsChecked?: boolean;
  /** Non-Nullable - `null` indicates no update */
  creatorHighBuyIsChecked?: boolean;
  /** Non-Nullable - `null` indicates no update */
  bundledBuysDetectedIsChecked?: boolean;
  /** Non-Nullable - `null` indicates no update */
  suspiciousWalletsDetectedIsChecked?: boolean;
  /** Non-Nullable - `null` indicates no update */
  singleHighBuyIsChecked?: boolean;
  /** Non-Nullable - `null` indicates no update */
  shouldWaitBeforeBuying?: boolean;
  /** Non-Nullable - `null` indicates no update */
  shouldAutoSellAfterHoldTime?: boolean;
  /** @nullable */
  marketCapFromUsd?: string | null;
  /** @nullable */
  marketCapToUsd?: string | null;
  /** @nullable */
  liquidityFromUsd?: string | null;
  /** @nullable */
  liquidityToUsd?: string | null;
  /** @nullable */
  dailyVolumeFromUsd?: string | null;
  /** @nullable */
  dailyVolumeToUsd?: string | null;
  /** @nullable */
  numberOfHoldersFrom?: string | null;
  /** @nullable */
  numberOfHoldersTo?: string | null;
  /** @nullable */
  buyVolume?: string | null;
  /** @nullable */
  sellVolume?: string | null;
  /** @nullable */
  sellTransactionFraction?: string | null;
  /** @nullable */
  buyTransactionFraction?: string | null;
}

export interface UserMarketPositionOverviewResult {
  totalValueAmountUsd: string;
  totalPnlAmountUsd: string;
  totalPnlAmountFraction: string;
  oneDayChangeAmountUsd: string;
  oneDayChangeFraction: string;
}

export interface GetTokenBalanceResult {
  platformBalance: string;
  onChainBalance: string;
}

export interface Detail {
  isOnDex: boolean;
}

export type DexDetailAllOf = {
  tokenDecimals?: string;
  dexInfo?: DexInfo;
};

export type DexDetail = Detail &
  DexDetailAllOf &
  Required<Pick<Detail & DexDetailAllOf, 'dexInfo' | 'isOnDex' | 'tokenDecimals'>>;

export interface DexInfo {
  chainId: string;
  url: string;
  dexPairInfo: GetDexResult;
  baseToken: Token;
  quoteToken: Token;
  priceUsd: string;
  priceNative: string;
  volume: TimeIntervals;
  priceChange: TimeIntervals;
  fullyDilutedValue?: string;
  marketCap?: string;
  liquidity?: Liquidity;
  buys: TimeIntervals;
  sells: TimeIntervals;
  pairCreatedAt: string;
  info?: Info;
}

export interface GetDexResult {
  pairAddress: string;
  dex: Dex;
}

export type GetTokenDetailResultTokenInfo = DexDetail | NonDexDetail;

export interface GetTokenDetailResult {
  tokenInfo: GetTokenDetailResultTokenInfo;
  userWalletMarketPositionsOnThisToken: WalletMarketPosition[];
  lastUserTransactionsOnThisToken: InfiniteScrollSliceSearchUserTransactionResultUUID;
  tokenAudit?: TokenAuditDto;
}

export interface Info {
  imageUrl: string;
  websites: Website[];
  socials: Social[];
}

export type IssueSeverity = (typeof IssueSeverity)[keyof typeof IssueSeverity];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const IssueSeverity = {
  HIGH: 'HIGH',
  MEDIUM: 'MEDIUM',
  LOW: 'LOW',
} as const;

export interface Liquidity {
  usd: string;
  base: string;
  quote: string;
}

export type NonDexDetailAllOf = {
  nonDexInfo?: NonDexInfo;
};

export type NonDexDetail = Detail &
  NonDexDetailAllOf &
  Required<Pick<Detail & NonDexDetailAllOf, 'isOnDex' | 'nonDexInfo'>>;

export interface NonDexInfo {
  name: string;
  symbol: string;
}

export type RiskFactor = (typeof RiskFactor)[keyof typeof RiskFactor];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const RiskFactor = {
  RED: 'RED',
  ORANGE: 'ORANGE',
  GREEN: 'GREEN',
} as const;

export interface Social {
  type: string;
  url: string;
}

export interface TimeIntervals {
  hours24?: string;
  hours06?: string;
  hours01?: string;
  minutes05?: string;
}

export interface Token {
  address: string;
  name: string;
  symbol: string;
}

export interface TokenAuditDto {
  result?: TokenAuditResult;
  state: TokenAuditState;
}

export interface TokenAuditIssue {
  summary: string;
  detail: string;
  severity: IssueSeverity;
}

export interface TokenAuditResult {
  issues: TokenAuditIssue[];
  riskFactor: RiskFactor;
  riskFactorReason: string;
}

export type TokenAuditState = (typeof TokenAuditState)[keyof typeof TokenAuditState];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TokenAuditState = {
  CREATED: 'CREATED',
  PROCESSING: 'PROCESSING',
  FINISHED: 'FINISHED',
} as const;

export interface WalletMarketPosition {
  walletId: string;
  walletDetailUrl: string;
  walletCustomName?: string;
  walletChain: Chain;
  tokenName: string;
  tokenSymbol: string;
  tokenImageUrl?: string;
  tokenAmount: string;
  tokenChain: Chain;
  currentValueUsd: string;
  currentValueChangeUsd: string;
  currentValueChangeFraction: string;
}

export interface Website {
  label: string;
  url: string;
}

export interface GetHotTokensResult {
  id: string;
  tokenAddress: string;
  tokenDetailUrl: string;
  tokenName: string;
  tokenSymbol: string;
  tokenChain: Chain;
  tokenImageUrl?: string;
  change24h?: string;
  volume24hUsd: string;
  priceUsd: string;
}

export interface InfiniteScrollSliceGetHotTokensResultBigDecimal {
  content: GetHotTokensResult[];
  lastId?: string;
  hasMore: boolean;
}

export interface GetUserResult {
  userId: string;
  email: string;
  referralCode?: string;
  quickBuyAmountUsd: string;
  isReferred: boolean;
  selectedChains: Chain[];
}

export interface GetUserWalletResult {
  id: string;
  isDefault: boolean;
  customName?: string;
  walletAddress: string;
  walletDetailUri: string;
  walletBalance: string;
  walletBalanceUsd: string;
  chain: Chain;
  buyAntiMevProtection: boolean;
  sellAntiMevProtection: boolean;
  currentPortfolioValueUsd: string;
  currentPortfolioValueChangeUsd: string;
  currentPortfolioValueChangeFraction: string;
  portfolioOneDayChangeAmountUsd: string;
  portfolioOneDayChangeFraction: string;
}

/**
 * Note: all unspecified units default to the native unit, i.e. ETH for EVM, SOL for Solana
 */
export interface SearchUserWalletPositionResult {
  chain: Chain;
  currentValueNative: string;
  currentValueUsd: string;
  currentPriceUsd: string;
  oneHourChangeFraction?: string;
  oneDayChangeFraction?: string;
  pnlUsd: string;
  pnlFraction: string;
}

export interface GetUserLeaderboardInfoResult {
  userId: string;
  donutsGained: string;
  donutsNeededForNextThreshold: string;
  volumeNeededForNextThreshold: string;
  volume: string;
  rank?: number;
  multiplier: string;
}

export interface InfiniteScrollSliceSearchUserLeaderboardResultInteger {
  content: SearchUserLeaderboardResult[];
  lastId?: number;
  hasMore: boolean;
}

export interface SearchUserLeaderboardResult {
  userId: string;
  rank: number;
  multiplier: string;
  donutsGained: string;
  email: string;
}

export interface GetUserLeaderboardGeneralInfoResult {
  rank: number;
  multiplier: string;
}

export interface GetLifetimeTradeVolumeResult {
  lifetimeTradeVolumeUsd: string;
}

export interface GetMultipliersThresholdResult {
  daysInStreak: number;
  multiplier: string;
  completed: boolean;
}

export interface GetStreakDayResult {
  date: string;
  state: StreakDayState;
}

export interface GetUserStreakResult {
  userId: string;
  daysInStreak: number;
  currentMultiplier: string;
  daysToNextStreak?: number;
  nextMultiplier?: string;
  streakExpiresAt: string;
  isThresholdDay: boolean;
  thresholds: GetMultipliersThresholdResult[];
  streakDates: GetStreakDayResult[];
}

export type StreakDayState = (typeof StreakDayState)[keyof typeof StreakDayState];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const StreakDayState = {
  NOT_STARTED: 'NOT_STARTED',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  NEXT: 'NEXT',
} as const;

export interface GetUserStreakGeneralInfoResult {
  daysInStreak: number;
  multiplier: string;
}

export interface GetReferralRewardsSummaryResult {
  numberOfReferrals: number;
  referralsOnChains: GetReferralRewardsSummaryResultChainData[];
}

export interface GetReferralRewardsSummaryResultChainData {
  chain: Chain;
  totalAmountNative: string;
  totalAmountUsd: string;
  unclaimedAmountNative: string;
  unclaimedAmountUsd: string;
}

export interface GetPortfolioValuesResult {
  totalValueAmountUsd: string;
  totalPnlAmountUsd: string;
  totalPnlAmountFraction: string;
  oneDayChangeAmountUsd: string;
  oneDayChangeFraction: string;
  pastValues: PortfolioPastValue[];
  pieChartValues: PieChartValue[];
}

export interface PieChartValue {
  chain: Chain;
  portfolioValueUsd: string;
  portfolioPnlFraction: string;
  type: PieChartValueType;
}

export type PieChartValueType = (typeof PieChartValueType)[keyof typeof PieChartValueType];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const PieChartValueType = {
  MANUAL: 'MANUAL',
  BOT: 'BOT',
} as const;

export interface PortfolioPastValue {
  portfolioValueUsd: string;
  date: string;
}

export interface GetAllUserLimitOrdersResult {
  id: string;
  walletId: string;
  type: LimitOrderType;
  tokenAddress: string;
  chain: Chain;
  limitPrice: string;
  remainingAmount: string;
  filledAmount: string;
  isLocked: boolean;
}

export interface GetLimitOrderDetailResult {
  id: string;
  walletId: string;
  type: LimitOrderType;
  tokenAddress: string;
  chain: Chain;
  limitPrice: string;
  remainingAmount: string;
  filledAmount: string;
  isLocked: boolean;
}

export interface GetUserFattyLeagueSeasonsResult {
  userFattyLeagueSeasons: UserFattyLeagueSeasonResult[];
  totalClaimedTokens: string;
  totalClaimableTokens: string;
}

export interface UserFattyLeagueSeasonResult {
  id: string;
  seasonName: string;
  earnedTokens: string;
  claimableTokens: string;
  claimedTokens: string;
  expiredTokens: string;
  claimableTokensNow: string;
  nextClaimableAt?: string;
  areAllTokensClaimed: boolean;
  tokensExpireAt?: string;
  isExpired: boolean;
}

export interface GetUserFattyCardsOverviewResult {
  userId: string;
  amountOfFattyCardsEarnedToday: number;
  amountNeededToNextFattyCard: string;
}

export interface GetUserFattyCardResult {
  userFattyCardId: string;
  avatarFileId: string;
  rarity: string;
  probability: string;
  donutReward: string;
}

export interface GetMyBotsResult {
  id: string;
  isActive: boolean;
  numberOfActiveDays: number;
  botStatus: BotStatus;
  name?: string;
  avatarFileUrl?: string;
  avatarFileId?: string;
  botWalletAddress?: string;
  balanceUsd?: string;
  timeRangeChangeUsd?: string;
  timeRangeChangeFraction?: string;
  buyCount?: string;
  sellCount?: string;
  draftCompleteness?: string;
}

export interface GetClaimingLimitsResult {
  chain: Chain;
  minimalClaimNativeAmount: string;
}

export interface CheckReferralCodeResult {
  isUnique: boolean;
}

export interface SearchTokenResult {
  chain: Chain;
  imageUrl?: string;
  token: Token;
  priceUsd: string;
  priceChangeHours24?: string;
  oneHourVolumeUsd?: string;
}

export interface GetIsTokenContractVerifiedResult {
  isVerified: boolean;
}

export interface GetAllZeroFeeTokensResult {
  chain: Chain;
  imageUrl?: string;
  tokenAddress: string;
  tokenName: string;
  tokenSymbol: string;
  tokenDetailUrl: string;
}

export interface GetEnabledChainsResult {
  enabledChains: Chain[];
}

export interface GetBotResult {
  id: string;
  botWalletAddress: string;
  botWalletChain: Chain;
  botWalletBalanceNativeAmount: string;
  botWalletBalanceUsd: string;
  createdAt: string;
  userReadableId: string;
  name: string;
  avatarFileId: string;
  tradeAmount: string;
  buyFrequency: string;
  remainingBuyFrequency: string;
  buyFrequencyLastResetAt: string;
  profitTargetFraction: string;
  stopLossFraction: string;
  hasSufficientBalanceForTrade: boolean;
  marketCapFromUsd?: string;
  marketCapToUsd?: string;
  liquidityFromUsd?: string;
  liquidityToUsd?: string;
  dailyVolumeFromUsd?: string;
  dailyVolumeToUsd?: string;
  numberOfHoldersFrom?: string;
  numberOfHoldersTo?: string;
  buyVolume?: string;
  sellVolume?: string;
  sellTransactionFraction?: string;
  buyTransactionFraction?: string;
  tokenTickerCopyIsChecked: boolean;
  creatorHighBuyIsChecked: boolean;
  bundledBuysDetectedIsChecked: boolean;
  suspiciousWalletsDetectedIsChecked: boolean;
  singleHighBuyIsChecked: boolean;
  shouldWaitBeforeBuying: boolean;
  shouldAutoSellAfterHoldTime: boolean;
}

export type BotMarketPositionSellReason =
  (typeof BotMarketPositionSellReason)[keyof typeof BotMarketPositionSellReason];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const BotMarketPositionSellReason = {
  STOP_LOSS_HIT: 'STOP_LOSS_HIT',
  TAKE_PROFIT_HIT: 'TAKE_PROFIT_HIT',
  MANUAL_SELL: 'MANUAL_SELL',
  PROTECTION_RULE_TRIGGER: 'PROTECTION_RULE_TRIGGER',
} as const;

export interface BotTradeTransaction {
  txId: string;
  txHash: string;
  txDetailUrl: string;
  txType: BotTransactionType;
  txStatus: TransactionStatus;
  txCreatedAt: string;
  txChain: Chain;
  botWalletId: string;
  amountUsd: string;
  marketCapUsd: string;
  liquidityUsd: string;
  volumeUsd: string;
  numOfHolders: number;
  buyVolume: string;
  sellVolume: string;
  fractionOfSellTransactions: string;
}

export interface GetBotMarketPositionResult {
  id: string;
  botId: string;
  tokenAddress: string;
  tokenDetailUrl: string;
  tokenName: string;
  tokenSymbol: string;
  tokenChain: Chain;
  tokenImageUrl?: string;
  openValueUsd: string;
  closeValueUsd?: string;
  openTimeStampAt: string;
  closedTimeStampAt?: string;
  pnlAmountUsd: string;
  pnlAmountFraction: string;
  currentValueUsd: string;
  buyTradeTransaction: BotTradeTransaction;
  sellTradeTransaction?: BotTradeTransaction;
  sellReason?: BotMarketPositionSellReason;
}

export interface GetBotSettingsStatisticsResult {
  tradeAmount: ValueBucketBigDecimal[];
  buyFrequency: ValueBucketBigInteger[];
  profitTarget: ValueBucketBigDecimal[];
  stopLoss: ValueBucketBigDecimal[];
  buyVolume: ValueBucketBigDecimal[];
  sellVolume: ValueBucketBigDecimal[];
  numberOfHolders: ValueBucketBigInteger[];
  marketCapUsd: ValueBucketBigDecimal[];
  liquidityUsd: ValueBucketBigDecimal[];
  dailyVolumeUsd: ValueBucketBigDecimal[];
  sellTransactionFractionThreshold: ValueBucketBigDecimal[];
}

export interface ValueBucketBigDecimal {
  from: string;
  to: string;
  count: number;
}

export interface ValueBucketBigInteger {
  from: string;
  to: string;
  count: number;
}

export interface GetAllBotsResult {
  id: string;
  createdAt: string;
  userReadableId: string;
  name: string;
  avatarFileId: string;
  tradeAmount: string;
  buyFrequency: string;
  remainingBuyFrequency: string;
  buyFrequencyLastResetAt: string;
  profitTargetFraction: string;
  stopLossFraction: string;
  marketCapFromUsd?: string;
  marketCapToUsd?: string;
  liquidityFromUsd?: string;
  liquidityToUsd?: string;
  dailyVolumeFromUsd?: string;
  dailyVolumeToUsd?: string;
  numberOfHoldersFrom?: string;
  numberOfHoldersTo?: string;
  buyVolume?: string;
  sellVolume?: string;
  sellTransactionFraction?: string;
  buyTransactionFraction?: string;
  tokenTickerCopyIsChecked: boolean;
  creatorHighBuyIsChecked: boolean;
  bundledBuysDetectedIsChecked: boolean;
  suspiciousWalletsDetectedIsChecked: boolean;
  singleHighBuyIsChecked: boolean;
  shouldWaitBeforeBuying: boolean;
  shouldAutoSellAfterHoldTime: boolean;
}

export interface GetAllBotDraftsResult {
  id: string;
  createdAt: string;
  name?: string;
  avatarFileId?: string;
  tradeAmount?: string;
  buyFrequency?: string;
  profitTargetFraction?: string;
  stopLossFraction?: string;
  marketCapFromUsd?: string;
  marketCapToUsd?: string;
  liquidityFromUsd?: string;
  liquidityToUsd?: string;
  dailyVolumeFromUsd?: string;
  dailyVolumeToUsd?: string;
  numberOfHoldersFrom?: string;
  numberOfHoldersTo?: string;
  buyVolume?: string;
  sellVolume?: string;
  sellTransactionFraction?: string;
  buyTransactionFraction?: string;
  tokenTickerCopyIsChecked: boolean;
  creatorHighBuyIsChecked: boolean;
  bundledBuysDetectedIsChecked: boolean;
  suspiciousWalletsDetectedIsChecked: boolean;
  singleHighBuyIsChecked: boolean;
  shouldWaitBeforeBuying: boolean;
  shouldAutoSellAfterHoldTime: boolean;
}

export type SearchUserTransactionOfAllTokenV3Params = {
  size?: number;
  lastId?: string;
};

export type SearchUserMarketPositionV3Params = {
  size?: number;
  lastId?: string;
};

export type SearchUserTransactionOfSpecificTokenV2Params = {
  size?: number;
  lastId?: string;
};

export type SearchUserTransactionOfAllTokenV2Params = {
  size?: number;
  lastId?: string;
};

export type TrackTokenV2Params = {
  walletId: string;
};

export type SearchUserMarketPositionV2Params = {
  size?: number;
  lastId?: string;
};

export type SignUpUserParams = {
  referralCode?: string;
};

export type SearchUserBotsTransactionsParams = {
  size?: number;
  lastId?: string;
};

export type SearchUserBotMarketPositionsParams = {
  size?: number;
  lastId?: string;
};

export type BotsLeaderboardParams = {
  size?: number;
  lastId?: string;
};

export type CreateBotParams = {
  botDraftId: string;
};

export type SearchBotMarketPositionsParams = {
  size?: number;
  lastId?: string;
};

export type BotsLeaderboard1Params = {
  size?: number;
  lastId?: string;
};

export type SetQuickBuyAmountParams = {
  quickBuyAmountUsd: string;
};

export type UpdateBotStateParams = {
  active: boolean;
};

export type GetAllUserWalletsV3Params = {
  useSelectedChains: boolean;
};

export type MarketPositionOverviewV3Params = {
  useSelectedChains: boolean;
};

export type GetTokenBalanceV2Params = {
  walletId: string;
};

export type GetHotTokensV2Params = {
  size?: number;
  lastId?: string;
  useSelectedChains: boolean;
};

export type MarketPositionOverviewV2Params = {
  chains: Chain[];
};

export type SearchUserWalletCurrencyPositionsParams = {
  useSelectedChains: boolean;
  walletId?: string;
};

export type SearchUserLeaderboardsParams = {
  size?: number;
  lastId?: number;
};

export type GetMyBotsParams = {
  timeRange: TimeRange;
};

export type CheckReferralCodeParams = {
  code: string;
};

export type GetIsTokenContractVerifiedParams = {
  chain: Chain;
};

export type GetHotTokensV2PublicParams = {
  chains?: Chain[];
  size?: number;
  lastId?: string;
};

type SecondParameter<T extends (...args: any) => any> = Parameters<T>[1];

export const selectChains = (
  updateSelectedChainsRequest: UpdateSelectedChainsRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/users/me/selected-chains`,
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      data: updateSelectedChainsRequest,
    },
    options,
  );
};

export const getSelectChainsMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof selectChains>>,
    TError,
    { data: UpdateSelectedChainsRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof selectChains>>,
  TError,
  { data: UpdateSelectedChainsRequest },
  TContext
> => {
  const mutationKey = ['selectChains'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof selectChains>>,
    { data: UpdateSelectedChainsRequest }
  > = (props) => {
    const { data } = props ?? {};

    return selectChains(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SelectChainsMutationResult = NonNullable<Awaited<ReturnType<typeof selectChains>>>;
export type SelectChainsMutationBody = UpdateSelectedChainsRequest;
export type SelectChainsMutationError = unknown;

export const useSelectChains = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof selectChains>>,
    TError,
    { data: UpdateSelectedChainsRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof selectChains>>,
  TError,
  { data: UpdateSelectedChainsRequest },
  TContext
> => {
  const mutationOptions = getSelectChainsMutationOptions(options);

  return useMutation(mutationOptions);
};

export const updateTokenImage = (
  tokenAddress: string,
  updateTokenImageRequest: UpdateTokenImageRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/internal/tokens/${tokenAddress}/image`,
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      data: updateTokenImageRequest,
    },
    options,
  );
};

export const getUpdateTokenImageMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateTokenImage>>,
    TError,
    { tokenAddress: string; data: UpdateTokenImageRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateTokenImage>>,
  TError,
  { tokenAddress: string; data: UpdateTokenImageRequest },
  TContext
> => {
  const mutationKey = ['updateTokenImage'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateTokenImage>>,
    { tokenAddress: string; data: UpdateTokenImageRequest }
  > = (props) => {
    const { tokenAddress, data } = props ?? {};

    return updateTokenImage(tokenAddress, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateTokenImageMutationResult = NonNullable<Awaited<ReturnType<typeof updateTokenImage>>>;
export type UpdateTokenImageMutationBody = UpdateTokenImageRequest;
export type UpdateTokenImageMutationError = unknown;

export const useUpdateTokenImage = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateTokenImage>>,
    TError,
    { tokenAddress: string; data: UpdateTokenImageRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof updateTokenImage>>,
  TError,
  { tokenAddress: string; data: UpdateTokenImageRequest },
  TContext
> => {
  const mutationOptions = getUpdateTokenImageMutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
		Search all transactions of user buying or selling any EVM tokens.
		Default size for infinite scroll is 20. When requesting new slice of data, send `lastId` from previous slice.
		Order is newest first, where content[0] is always the newest transaction.
	
 */
export const searchUserTransactionOfAllTokenV3 = (
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params?: SearchUserTransactionOfAllTokenV3Params,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceSearchUserTransactionResultUUID>(
    {
      url: `/v3/users/me/transactions/tokens/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: searchUserTokensTransactionRequestV3,
      params,
    },
    options,
  );
};

export const getSearchUserTransactionOfAllTokenV3QueryKey = (
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params?: SearchUserTransactionOfAllTokenV3Params,
) => {
  return [
    `/v3/users/me/transactions/tokens/search`,
    ...(params ? [params] : []),
    searchUserTokensTransactionRequestV3,
  ] as const;
};

export const getSearchUserTransactionOfAllTokenV3InfiniteQueryOptions = <
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
    SearchUserTransactionOfAllTokenV3Params['lastId']
  >,
  TError = unknown,
>(
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params?: SearchUserTransactionOfAllTokenV3Params,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
        QueryKey,
        SearchUserTransactionOfAllTokenV3Params['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getSearchUserTransactionOfAllTokenV3QueryKey(searchUserTokensTransactionRequestV3, params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
    QueryKey,
    SearchUserTransactionOfAllTokenV3Params['lastId']
  > = ({ pageParam }) =>
    searchUserTransactionOfAllTokenV3(
      searchUserTokensTransactionRequestV3,
      { ...params, lastId: pageParam || params?.['lastId'] },
      requestOptions,
    );

  return { queryKey, queryFn, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
    TError,
    TData,
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
    QueryKey,
    SearchUserTransactionOfAllTokenV3Params['lastId']
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchUserTransactionOfAllTokenV3InfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>
>;
export type SearchUserTransactionOfAllTokenV3InfiniteQueryError = unknown;

export function useSearchUserTransactionOfAllTokenV3Infinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
    SearchUserTransactionOfAllTokenV3Params['lastId']
  >,
  TError = unknown,
>(
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params: undefined | SearchUserTransactionOfAllTokenV3Params,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
        QueryKey,
        SearchUserTransactionOfAllTokenV3Params['lastId']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
          TError,
          Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserTransactionOfAllTokenV3Infinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
    SearchUserTransactionOfAllTokenV3Params['lastId']
  >,
  TError = unknown,
>(
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params?: SearchUserTransactionOfAllTokenV3Params,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
        QueryKey,
        SearchUserTransactionOfAllTokenV3Params['lastId']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
          TError,
          Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserTransactionOfAllTokenV3Infinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
    SearchUserTransactionOfAllTokenV3Params['lastId']
  >,
  TError = unknown,
>(
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params?: SearchUserTransactionOfAllTokenV3Params,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
        QueryKey,
        SearchUserTransactionOfAllTokenV3Params['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchUserTransactionOfAllTokenV3Infinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
    SearchUserTransactionOfAllTokenV3Params['lastId']
  >,
  TError = unknown,
>(
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params?: SearchUserTransactionOfAllTokenV3Params,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
        QueryKey,
        SearchUserTransactionOfAllTokenV3Params['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchUserTransactionOfAllTokenV3InfiniteQueryOptions(
    searchUserTokensTransactionRequestV3,
    params,
    options,
  );

  const query = useInfiniteQuery(queryOptions) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getSearchUserTransactionOfAllTokenV3QueryOptions = <
  TData = Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
  TError = unknown,
>(
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params?: SearchUserTransactionOfAllTokenV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getSearchUserTransactionOfAllTokenV3QueryKey(searchUserTokensTransactionRequestV3, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>> = () =>
    searchUserTransactionOfAllTokenV3(searchUserTokensTransactionRequestV3, params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchUserTransactionOfAllTokenV3QueryResult = NonNullable<
  Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>
>;
export type SearchUserTransactionOfAllTokenV3QueryError = unknown;

export function useSearchUserTransactionOfAllTokenV3<
  TData = Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
  TError = unknown,
>(
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params: undefined | SearchUserTransactionOfAllTokenV3Params,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
          TError,
          Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserTransactionOfAllTokenV3<
  TData = Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
  TError = unknown,
>(
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params?: SearchUserTransactionOfAllTokenV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
          TError,
          Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserTransactionOfAllTokenV3<
  TData = Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
  TError = unknown,
>(
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params?: SearchUserTransactionOfAllTokenV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchUserTransactionOfAllTokenV3<
  TData = Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>,
  TError = unknown,
>(
  searchUserTokensTransactionRequestV3: SearchUserTokensTransactionRequestV3,
  params?: SearchUserTransactionOfAllTokenV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchUserTransactionOfAllTokenV3QueryOptions(
    searchUserTokensTransactionRequestV3,
    params,
    options,
  );

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
		Get User market position (how much tokens they hold) summed for all Wallets on specified chainId.
		When walletId is null, all Wallets are considered.
		Search string search for token name, token symbol (ignoring case) or token address (only if valid EVM address is provided)
	
 */
export const searchUserMarketPositionV3 = (
  searchUserTokenRequestV3: SearchUserTokenRequestV3,
  params?: SearchUserMarketPositionV3Params,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceSearchUserMarketPositionResultUUID>(
    {
      url: `/v3/users/me/market-positions/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: searchUserTokenRequestV3,
      params,
    },
    options,
  );
};

export const getSearchUserMarketPositionV3QueryKey = (
  searchUserTokenRequestV3: SearchUserTokenRequestV3,
  params?: SearchUserMarketPositionV3Params,
) => {
  return [`/v3/users/me/market-positions/search`, ...(params ? [params] : []), searchUserTokenRequestV3] as const;
};

export const getSearchUserMarketPositionV3QueryOptions = <
  TData = Awaited<ReturnType<typeof searchUserMarketPositionV3>>,
  TError = unknown,
>(
  searchUserTokenRequestV3: SearchUserTokenRequestV3,
  params?: SearchUserMarketPositionV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserMarketPositionV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getSearchUserMarketPositionV3QueryKey(searchUserTokenRequestV3, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof searchUserMarketPositionV3>>> = () =>
    searchUserMarketPositionV3(searchUserTokenRequestV3, params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof searchUserMarketPositionV3>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchUserMarketPositionV3QueryResult = NonNullable<Awaited<ReturnType<typeof searchUserMarketPositionV3>>>;
export type SearchUserMarketPositionV3QueryError = unknown;

export function useSearchUserMarketPositionV3<
  TData = Awaited<ReturnType<typeof searchUserMarketPositionV3>>,
  TError = unknown,
>(
  searchUserTokenRequestV3: SearchUserTokenRequestV3,
  params: undefined | SearchUserMarketPositionV3Params,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserMarketPositionV3>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserMarketPositionV3>>,
          TError,
          Awaited<ReturnType<typeof searchUserMarketPositionV3>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserMarketPositionV3<
  TData = Awaited<ReturnType<typeof searchUserMarketPositionV3>>,
  TError = unknown,
>(
  searchUserTokenRequestV3: SearchUserTokenRequestV3,
  params?: SearchUserMarketPositionV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserMarketPositionV3>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserMarketPositionV3>>,
          TError,
          Awaited<ReturnType<typeof searchUserMarketPositionV3>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserMarketPositionV3<
  TData = Awaited<ReturnType<typeof searchUserMarketPositionV3>>,
  TError = unknown,
>(
  searchUserTokenRequestV3: SearchUserTokenRequestV3,
  params?: SearchUserMarketPositionV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserMarketPositionV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchUserMarketPositionV3<
  TData = Awaited<ReturnType<typeof searchUserMarketPositionV3>>,
  TError = unknown,
>(
  searchUserTokenRequestV3: SearchUserTokenRequestV3,
  params?: SearchUserMarketPositionV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserMarketPositionV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchUserMarketPositionV3QueryOptions(searchUserTokenRequestV3, params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const createNewUserWalletV2 = (
  createNewUserWalletRequestV2: CreateNewUserWalletRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<CreateNewUserWalletResult>(
    {
      url: `/v2/users/me/wallets`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: createNewUserWalletRequestV2,
    },
    options,
  );
};

export const getCreateNewUserWalletV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createNewUserWalletV2>>,
    TError,
    { data: CreateNewUserWalletRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof createNewUserWalletV2>>,
  TError,
  { data: CreateNewUserWalletRequestV2 },
  TContext
> => {
  const mutationKey = ['createNewUserWalletV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof createNewUserWalletV2>>,
    { data: CreateNewUserWalletRequestV2 }
  > = (props) => {
    const { data } = props ?? {};

    return createNewUserWalletV2(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateNewUserWalletV2MutationResult = NonNullable<Awaited<ReturnType<typeof createNewUserWalletV2>>>;
export type CreateNewUserWalletV2MutationBody = CreateNewUserWalletRequestV2;
export type CreateNewUserWalletV2MutationError = unknown;

export const useCreateNewUserWalletV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createNewUserWalletV2>>,
    TError,
    { data: CreateNewUserWalletRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof createNewUserWalletV2>>,
  TError,
  { data: CreateNewUserWalletRequestV2 },
  TContext
> => {
  const mutationOptions = getCreateNewUserWalletV2MutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
			Try to transfer amount of given tokens on chainId to an arbitrary address.
			The return value is tx hash of transaction that was sent to RPC.
		
 */
export const transferTokenV2 = (
  walletId: string,
  transferTokenRequestV2: TransferTokenRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<TransferTokenResult>(
    {
      url: `/v2/users/me/wallets/${walletId}/tokens/transfer`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: transferTokenRequestV2,
    },
    options,
  );
};

export const getTransferTokenV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof transferTokenV2>>,
    TError,
    { walletId: string; data: TransferTokenRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof transferTokenV2>>,
  TError,
  { walletId: string; data: TransferTokenRequestV2 },
  TContext
> => {
  const mutationKey = ['transferTokenV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof transferTokenV2>>,
    { walletId: string; data: TransferTokenRequestV2 }
  > = (props) => {
    const { walletId, data } = props ?? {};

    return transferTokenV2(walletId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type TransferTokenV2MutationResult = NonNullable<Awaited<ReturnType<typeof transferTokenV2>>>;
export type TransferTokenV2MutationBody = TransferTokenRequestV2;
export type TransferTokenV2MutationError = unknown;

export const useTransferTokenV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof transferTokenV2>>,
    TError,
    { walletId: string; data: TransferTokenRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof transferTokenV2>>,
  TError,
  { walletId: string; data: TransferTokenRequestV2 },
  TContext
> => {
  const mutationOptions = getTransferTokenV2MutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
			Try to sell an EVM token on chainId selling given amount of tokens (this number is in decimals of given token!).
			The amount of Wei received will be known only after transaction succeeds.
			The return value is array of tx hashes (for sell, approval tx might be needed before sell tx can happen)
		
 */
export const sellTokenV2 = (
  walletId: string,
  sellTokenRequestV2: SellTokenRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<SellTokenResult>(
    {
      url: `/v2/users/me/wallets/${walletId}/tokens/sell`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: sellTokenRequestV2,
    },
    options,
  );
};

export const getSellTokenV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof sellTokenV2>>,
    TError,
    { walletId: string; data: SellTokenRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof sellTokenV2>>,
  TError,
  { walletId: string; data: SellTokenRequestV2 },
  TContext
> => {
  const mutationKey = ['sellTokenV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof sellTokenV2>>,
    { walletId: string; data: SellTokenRequestV2 }
  > = (props) => {
    const { walletId, data } = props ?? {};

    return sellTokenV2(walletId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SellTokenV2MutationResult = NonNullable<Awaited<ReturnType<typeof sellTokenV2>>>;
export type SellTokenV2MutationBody = SellTokenRequestV2;
export type SellTokenV2MutationError = unknown;

export const useSellTokenV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof sellTokenV2>>,
    TError,
    { walletId: string; data: SellTokenRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof sellTokenV2>>,
  TError,
  { walletId: string; data: SellTokenRequestV2 },
  TContext
> => {
  const mutationOptions = getSellTokenV2MutationOptions(options);

  return useMutation(mutationOptions);
};

export const getMaxBuyV2 = (
  walletId: string,
  getMaxBuyRequestV2: GetMaxBuyRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetTokenMaxBuyResult>(
    {
      url: `/v2/users/me/wallets/${walletId}/tokens/max-buy`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: getMaxBuyRequestV2,
    },
    options,
  );
};

export const getGetMaxBuyV2QueryKey = (walletId: string, getMaxBuyRequestV2: GetMaxBuyRequestV2) => {
  return [`/v2/users/me/wallets/${walletId}/tokens/max-buy`, getMaxBuyRequestV2] as const;
};

export const getGetMaxBuyV2QueryOptions = <TData = Awaited<ReturnType<typeof getMaxBuyV2>>, TError = unknown>(
  walletId: string,
  getMaxBuyRequestV2: GetMaxBuyRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMaxBuyV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetMaxBuyV2QueryKey(walletId, getMaxBuyRequestV2);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getMaxBuyV2>>> = () =>
    getMaxBuyV2(walletId, getMaxBuyRequestV2, requestOptions);

  return { queryKey, queryFn, enabled: !!walletId, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getMaxBuyV2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetMaxBuyV2QueryResult = NonNullable<Awaited<ReturnType<typeof getMaxBuyV2>>>;
export type GetMaxBuyV2QueryError = unknown;

export function useGetMaxBuyV2<TData = Awaited<ReturnType<typeof getMaxBuyV2>>, TError = unknown>(
  walletId: string,
  getMaxBuyRequestV2: GetMaxBuyRequestV2,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMaxBuyV2>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getMaxBuyV2>>,
          TError,
          Awaited<ReturnType<typeof getMaxBuyV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetMaxBuyV2<TData = Awaited<ReturnType<typeof getMaxBuyV2>>, TError = unknown>(
  walletId: string,
  getMaxBuyRequestV2: GetMaxBuyRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMaxBuyV2>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getMaxBuyV2>>,
          TError,
          Awaited<ReturnType<typeof getMaxBuyV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetMaxBuyV2<TData = Awaited<ReturnType<typeof getMaxBuyV2>>, TError = unknown>(
  walletId: string,
  getMaxBuyRequestV2: GetMaxBuyRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMaxBuyV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetMaxBuyV2<TData = Awaited<ReturnType<typeof getMaxBuyV2>>, TError = unknown>(
  walletId: string,
  getMaxBuyRequestV2: GetMaxBuyRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMaxBuyV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetMaxBuyV2QueryOptions(walletId, getMaxBuyRequestV2, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
			Try to buy a token on chain buying for native amount on that chain
			e.g. nativeAmount = 1 on solana is buying for 1 SOL, whereas nativeAmount = 1 on ethereum is 1 ETH

			The amount of tokens received will be known only after transaction succeeds.
			The return value is tx hash / signature of transaction that was sent to RPC.
		
 */
export const buyTokenV2 = (
  walletId: string,
  buyTokenRequestV2: BuyTokenRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<BuyTokenResult>(
    {
      url: `/v2/users/me/wallets/${walletId}/tokens/buy`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: buyTokenRequestV2,
    },
    options,
  );
};

export const getBuyTokenV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof buyTokenV2>>,
    TError,
    { walletId: string; data: BuyTokenRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof buyTokenV2>>,
  TError,
  { walletId: string; data: BuyTokenRequestV2 },
  TContext
> => {
  const mutationKey = ['buyTokenV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof buyTokenV2>>,
    { walletId: string; data: BuyTokenRequestV2 }
  > = (props) => {
    const { walletId, data } = props ?? {};

    return buyTokenV2(walletId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type BuyTokenV2MutationResult = NonNullable<Awaited<ReturnType<typeof buyTokenV2>>>;
export type BuyTokenV2MutationBody = BuyTokenRequestV2;
export type BuyTokenV2MutationError = unknown;

export const useBuyTokenV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof buyTokenV2>>,
    TError,
    { walletId: string; data: BuyTokenRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof buyTokenV2>>,
  TError,
  { walletId: string; data: BuyTokenRequestV2 },
  TContext
> => {
  const mutationOptions = getBuyTokenV2MutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
			Try to transfer given amount of native currency to an arbitrary address.
			The return value is tx hash / signature of transaction that was sent to RPC.

			Note:
		
 * @deprecated
 */
export const transferEthV2 = (
  walletId: string,
  transferCurrencyRequestV2: TransferCurrencyRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<TransferCurrencyResult>(
    {
      url: `/v2/users/me/wallets/${walletId}/eth/transfer`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: transferCurrencyRequestV2,
    },
    options,
  );
};

export const getTransferEthV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof transferEthV2>>,
    TError,
    { walletId: string; data: TransferCurrencyRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof transferEthV2>>,
  TError,
  { walletId: string; data: TransferCurrencyRequestV2 },
  TContext
> => {
  const mutationKey = ['transferEthV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof transferEthV2>>,
    { walletId: string; data: TransferCurrencyRequestV2 }
  > = (props) => {
    const { walletId, data } = props ?? {};

    return transferEthV2(walletId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type TransferEthV2MutationResult = NonNullable<Awaited<ReturnType<typeof transferEthV2>>>;
export type TransferEthV2MutationBody = TransferCurrencyRequestV2;
export type TransferEthV2MutationError = unknown;

/**
 * @deprecated
 */
export const useTransferEthV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof transferEthV2>>,
    TError,
    { walletId: string; data: TransferCurrencyRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof transferEthV2>>,
  TError,
  { walletId: string; data: TransferCurrencyRequestV2 },
  TContext
> => {
  const mutationOptions = getTransferEthV2MutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
			Try to transfer given amount of native currency to an arbitrary address.
			The return value is tx hash / signature of transaction that was sent to RPC.

			Note:
		
 */
export const transferCurrencyV2 = (
  walletId: string,
  transferCurrencyRequestV2: TransferCurrencyRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<TransferCurrencyResult>(
    {
      url: `/v2/users/me/wallets/${walletId}/currency/transfer`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: transferCurrencyRequestV2,
    },
    options,
  );
};

export const getTransferCurrencyV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof transferCurrencyV2>>,
    TError,
    { walletId: string; data: TransferCurrencyRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof transferCurrencyV2>>,
  TError,
  { walletId: string; data: TransferCurrencyRequestV2 },
  TContext
> => {
  const mutationKey = ['transferCurrencyV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof transferCurrencyV2>>,
    { walletId: string; data: TransferCurrencyRequestV2 }
  > = (props) => {
    const { walletId, data } = props ?? {};

    return transferCurrencyV2(walletId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type TransferCurrencyV2MutationResult = NonNullable<Awaited<ReturnType<typeof transferCurrencyV2>>>;
export type TransferCurrencyV2MutationBody = TransferCurrencyRequestV2;
export type TransferCurrencyV2MutationError = unknown;

export const useTransferCurrencyV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof transferCurrencyV2>>,
    TError,
    { walletId: string; data: TransferCurrencyRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof transferCurrencyV2>>,
  TError,
  { walletId: string; data: TransferCurrencyRequestV2 },
  TContext
> => {
  const mutationOptions = getTransferCurrencyV2MutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * @deprecated
 */
export const getAllUserWalletsV2 = (
  getAllUserWalletsRequestV2: GetAllUserWalletsRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetAllUserWalletsResult[]>(
    {
      url: `/v2/users/me/wallets/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: getAllUserWalletsRequestV2,
    },
    options,
  );
};

export const getGetAllUserWalletsV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getAllUserWalletsV2>>,
    TError,
    { data: GetAllUserWalletsRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof getAllUserWalletsV2>>,
  TError,
  { data: GetAllUserWalletsRequestV2 },
  TContext
> => {
  const mutationKey = ['getAllUserWalletsV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof getAllUserWalletsV2>>,
    { data: GetAllUserWalletsRequestV2 }
  > = (props) => {
    const { data } = props ?? {};

    return getAllUserWalletsV2(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type GetAllUserWalletsV2MutationResult = NonNullable<Awaited<ReturnType<typeof getAllUserWalletsV2>>>;
export type GetAllUserWalletsV2MutationBody = GetAllUserWalletsRequestV2;
export type GetAllUserWalletsV2MutationError = unknown;

/**
 * @deprecated
 */
export const useGetAllUserWalletsV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof getAllUserWalletsV2>>,
    TError,
    { data: GetAllUserWalletsRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof getAllUserWalletsV2>>,
  TError,
  { data: GetAllUserWalletsRequestV2 },
  TContext
> => {
  const mutationOptions = getGetAllUserWalletsV2MutationOptions(options);

  return useMutation(mutationOptions);
};

export const importUserWalletV2 = (
  importUserWalletRequestV2: ImportUserWalletRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<ImportUserWalletResult>(
    {
      url: `/v2/users/me/wallets/import`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: importUserWalletRequestV2,
    },
    options,
  );
};

export const getImportUserWalletV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof importUserWalletV2>>,
    TError,
    { data: ImportUserWalletRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof importUserWalletV2>>,
  TError,
  { data: ImportUserWalletRequestV2 },
  TContext
> => {
  const mutationKey = ['importUserWalletV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof importUserWalletV2>>,
    { data: ImportUserWalletRequestV2 }
  > = (props) => {
    const { data } = props ?? {};

    return importUserWalletV2(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ImportUserWalletV2MutationResult = NonNullable<Awaited<ReturnType<typeof importUserWalletV2>>>;
export type ImportUserWalletV2MutationBody = ImportUserWalletRequestV2;
export type ImportUserWalletV2MutationError = unknown;

export const useImportUserWalletV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof importUserWalletV2>>,
    TError,
    { data: ImportUserWalletRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof importUserWalletV2>>,
  TError,
  { data: ImportUserWalletRequestV2 },
  TContext
> => {
  const mutationOptions = getImportUserWalletV2MutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
		Search all transactions of user buying or selling only specified EVM token.
		Default size for infinite scroll is 20. When requesting new slice of data, send `lastId` from previous slice.
		Order is newest first, where content[0] is always the newest transaction.
	
 */
export const searchUserTransactionOfSpecificTokenV2 = (
  tokenAddress: string,
  searchUserTokenTransactionsRequestV2: SearchUserTokenTransactionsRequestV2,
  params?: SearchUserTransactionOfSpecificTokenV2Params,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceSearchUserTransactionResultUUID>(
    {
      url: `/v2/users/me/transactions/tokens/${tokenAddress}/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: searchUserTokenTransactionsRequestV2,
      params,
    },
    options,
  );
};

export const getSearchUserTransactionOfSpecificTokenV2MutationOptions = <
  TError = unknown,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchUserTransactionOfSpecificTokenV2>>,
    TError,
    {
      tokenAddress: string;
      data: SearchUserTokenTransactionsRequestV2;
      params?: SearchUserTransactionOfSpecificTokenV2Params;
    },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof searchUserTransactionOfSpecificTokenV2>>,
  TError,
  {
    tokenAddress: string;
    data: SearchUserTokenTransactionsRequestV2;
    params?: SearchUserTransactionOfSpecificTokenV2Params;
  },
  TContext
> => {
  const mutationKey = ['searchUserTransactionOfSpecificTokenV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof searchUserTransactionOfSpecificTokenV2>>,
    {
      tokenAddress: string;
      data: SearchUserTokenTransactionsRequestV2;
      params?: SearchUserTransactionOfSpecificTokenV2Params;
    }
  > = (props) => {
    const { tokenAddress, data, params } = props ?? {};

    return searchUserTransactionOfSpecificTokenV2(tokenAddress, data, params, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SearchUserTransactionOfSpecificTokenV2MutationResult = NonNullable<
  Awaited<ReturnType<typeof searchUserTransactionOfSpecificTokenV2>>
>;
export type SearchUserTransactionOfSpecificTokenV2MutationBody = SearchUserTokenTransactionsRequestV2;
export type SearchUserTransactionOfSpecificTokenV2MutationError = unknown;

export const useSearchUserTransactionOfSpecificTokenV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchUserTransactionOfSpecificTokenV2>>,
    TError,
    {
      tokenAddress: string;
      data: SearchUserTokenTransactionsRequestV2;
      params?: SearchUserTransactionOfSpecificTokenV2Params;
    },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof searchUserTransactionOfSpecificTokenV2>>,
  TError,
  {
    tokenAddress: string;
    data: SearchUserTokenTransactionsRequestV2;
    params?: SearchUserTransactionOfSpecificTokenV2Params;
  },
  TContext
> => {
  const mutationOptions = getSearchUserTransactionOfSpecificTokenV2MutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
		Search all transactions of user buying or selling any EVM tokens.
		Default size for infinite scroll is 20. When requesting new slice of data, send `lastId` from previous slice.
		Order is newest first, where content[0] is always the newest transaction.
	
 * @deprecated
 */
export const searchUserTransactionOfAllTokenV2 = (
  searchUserTokensTransactionRequestV2: SearchUserTokensTransactionRequestV2,
  params?: SearchUserTransactionOfAllTokenV2Params,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceSearchUserTransactionResultUUID>(
    {
      url: `/v2/users/me/transactions/tokens/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: searchUserTokensTransactionRequestV2,
      params,
    },
    options,
  );
};

export const getSearchUserTransactionOfAllTokenV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV2>>,
    TError,
    { data: SearchUserTokensTransactionRequestV2; params?: SearchUserTransactionOfAllTokenV2Params },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV2>>,
  TError,
  { data: SearchUserTokensTransactionRequestV2; params?: SearchUserTransactionOfAllTokenV2Params },
  TContext
> => {
  const mutationKey = ['searchUserTransactionOfAllTokenV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV2>>,
    { data: SearchUserTokensTransactionRequestV2; params?: SearchUserTransactionOfAllTokenV2Params }
  > = (props) => {
    const { data, params } = props ?? {};

    return searchUserTransactionOfAllTokenV2(data, params, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SearchUserTransactionOfAllTokenV2MutationResult = NonNullable<
  Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV2>>
>;
export type SearchUserTransactionOfAllTokenV2MutationBody = SearchUserTokensTransactionRequestV2;
export type SearchUserTransactionOfAllTokenV2MutationError = unknown;

/**
 * @deprecated
 */
export const useSearchUserTransactionOfAllTokenV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV2>>,
    TError,
    { data: SearchUserTokensTransactionRequestV2; params?: SearchUserTransactionOfAllTokenV2Params },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof searchUserTransactionOfAllTokenV2>>,
  TError,
  { data: SearchUserTokensTransactionRequestV2; params?: SearchUserTransactionOfAllTokenV2Params },
  TContext
> => {
  const mutationOptions = getSearchUserTransactionOfAllTokenV2MutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
			Track EVM token. If the on chain balance is higher, new transaction is created and the balances are evened.
		
 */
export const trackTokenV2 = (
  tokenAddress: string,
  params: TrackTokenV2Params,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<TrackTokenResult>(
    { url: `/v2/users/me/tokens/${tokenAddress}/track`, method: 'POST', params },
    options,
  );
};

export const getTrackTokenV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof trackTokenV2>>,
    TError,
    { tokenAddress: string; params: TrackTokenV2Params },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof trackTokenV2>>,
  TError,
  { tokenAddress: string; params: TrackTokenV2Params },
  TContext
> => {
  const mutationKey = ['trackTokenV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof trackTokenV2>>,
    { tokenAddress: string; params: TrackTokenV2Params }
  > = (props) => {
    const { tokenAddress, params } = props ?? {};

    return trackTokenV2(tokenAddress, params, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type TrackTokenV2MutationResult = NonNullable<Awaited<ReturnType<typeof trackTokenV2>>>;

export type TrackTokenV2MutationError = unknown;

export const useTrackTokenV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof trackTokenV2>>,
    TError,
    { tokenAddress: string; params: TrackTokenV2Params },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof trackTokenV2>>,
  TError,
  { tokenAddress: string; params: TrackTokenV2Params },
  TContext
> => {
  const mutationOptions = getTrackTokenV2MutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
			Try to buy a token on chain for the quick buy amount associated with the user. See setQuickBuyAmount endpoint.
			The amount of tokens received will be known only after transaction succeeds.
			The return value is tx hash of transaction that was sent to RPC.

			This endpoint always buys the token with default user wallet on that chain.
		
 */
export const quickBuyTokenV2 = (
  tokenAddress: string,
  quickBuyTokenRequestV2: QuickBuyTokenRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<QuickBuyTokenResult>(
    {
      url: `/v2/users/me/tokens/${tokenAddress}/quick-buy`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: quickBuyTokenRequestV2,
    },
    options,
  );
};

export const getQuickBuyTokenV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof quickBuyTokenV2>>,
    TError,
    { tokenAddress: string; data: QuickBuyTokenRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof quickBuyTokenV2>>,
  TError,
  { tokenAddress: string; data: QuickBuyTokenRequestV2 },
  TContext
> => {
  const mutationKey = ['quickBuyTokenV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof quickBuyTokenV2>>,
    { tokenAddress: string; data: QuickBuyTokenRequestV2 }
  > = (props) => {
    const { tokenAddress, data } = props ?? {};

    return quickBuyTokenV2(tokenAddress, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type QuickBuyTokenV2MutationResult = NonNullable<Awaited<ReturnType<typeof quickBuyTokenV2>>>;
export type QuickBuyTokenV2MutationBody = QuickBuyTokenRequestV2;
export type QuickBuyTokenV2MutationError = unknown;

export const useQuickBuyTokenV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof quickBuyTokenV2>>,
    TError,
    { tokenAddress: string; data: QuickBuyTokenRequestV2 },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof quickBuyTokenV2>>,
  TError,
  { tokenAddress: string; data: QuickBuyTokenRequestV2 },
  TContext
> => {
  const mutationOptions = getQuickBuyTokenV2MutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
		Get User market position (how much tokens they hold) summed for all Wallets on specified chainId.
		When walletId is null, all Wallets are considered.
		Search string search for token name, token symbol (ignoring case) or token address (only if valid EVM address is provided)
	
 * @deprecated
 */
export const searchUserMarketPositionV2 = (
  searchUserTokenRequestV2: SearchUserTokenRequestV2,
  params?: SearchUserMarketPositionV2Params,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceSearchUserMarketPositionResultUUID>(
    {
      url: `/v2/users/me/market-positions/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: searchUserTokenRequestV2,
      params,
    },
    options,
  );
};

export const getSearchUserMarketPositionV2MutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchUserMarketPositionV2>>,
    TError,
    { data: SearchUserTokenRequestV2; params?: SearchUserMarketPositionV2Params },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof searchUserMarketPositionV2>>,
  TError,
  { data: SearchUserTokenRequestV2; params?: SearchUserMarketPositionV2Params },
  TContext
> => {
  const mutationKey = ['searchUserMarketPositionV2'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof searchUserMarketPositionV2>>,
    { data: SearchUserTokenRequestV2; params?: SearchUserMarketPositionV2Params }
  > = (props) => {
    const { data, params } = props ?? {};

    return searchUserMarketPositionV2(data, params, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SearchUserMarketPositionV2MutationResult = NonNullable<
  Awaited<ReturnType<typeof searchUserMarketPositionV2>>
>;
export type SearchUserMarketPositionV2MutationBody = SearchUserTokenRequestV2;
export type SearchUserMarketPositionV2MutationError = unknown;

/**
 * @deprecated
 */
export const useSearchUserMarketPositionV2 = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof searchUserMarketPositionV2>>,
    TError,
    { data: SearchUserTokenRequestV2; params?: SearchUserMarketPositionV2Params },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof searchUserMarketPositionV2>>,
  TError,
  { data: SearchUserTokenRequestV2; params?: SearchUserMarketPositionV2Params },
  TContext
> => {
  const mutationOptions = getSearchUserMarketPositionV2MutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
			Get gas fees estimation.
			NativeAmount is passed in chain's native coin amount i.e 0.00001 ETH or 21.0001 SOL
		
 */
export const getGasEstimationUsdV2 = (
  getGasEstimationUsdRequestV2: GetGasEstimationUsdRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetGasEstimationUsdResult>(
    {
      url: `/v2/users/me/gas-estimation/usd`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: getGasEstimationUsdRequestV2,
    },
    options,
  );
};

export const getGetGasEstimationUsdV2QueryKey = (getGasEstimationUsdRequestV2: GetGasEstimationUsdRequestV2) => {
  return [`/v2/users/me/gas-estimation/usd`, getGasEstimationUsdRequestV2] as const;
};

export const getGetGasEstimationUsdV2QueryOptions = <
  TData = Awaited<ReturnType<typeof getGasEstimationUsdV2>>,
  TError = unknown,
>(
  getGasEstimationUsdRequestV2: GetGasEstimationUsdRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGasEstimationUsdV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetGasEstimationUsdV2QueryKey(getGasEstimationUsdRequestV2);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getGasEstimationUsdV2>>> = () =>
    getGasEstimationUsdV2(getGasEstimationUsdRequestV2, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getGasEstimationUsdV2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetGasEstimationUsdV2QueryResult = NonNullable<Awaited<ReturnType<typeof getGasEstimationUsdV2>>>;
export type GetGasEstimationUsdV2QueryError = unknown;

export function useGetGasEstimationUsdV2<TData = Awaited<ReturnType<typeof getGasEstimationUsdV2>>, TError = unknown>(
  getGasEstimationUsdRequestV2: GetGasEstimationUsdRequestV2,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGasEstimationUsdV2>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getGasEstimationUsdV2>>,
          TError,
          Awaited<ReturnType<typeof getGasEstimationUsdV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetGasEstimationUsdV2<TData = Awaited<ReturnType<typeof getGasEstimationUsdV2>>, TError = unknown>(
  getGasEstimationUsdRequestV2: GetGasEstimationUsdRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGasEstimationUsdV2>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getGasEstimationUsdV2>>,
          TError,
          Awaited<ReturnType<typeof getGasEstimationUsdV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetGasEstimationUsdV2<TData = Awaited<ReturnType<typeof getGasEstimationUsdV2>>, TError = unknown>(
  getGasEstimationUsdRequestV2: GetGasEstimationUsdRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGasEstimationUsdV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetGasEstimationUsdV2<TData = Awaited<ReturnType<typeof getGasEstimationUsdV2>>, TError = unknown>(
  getGasEstimationUsdRequestV2: GetGasEstimationUsdRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGasEstimationUsdV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetGasEstimationUsdV2QueryOptions(getGasEstimationUsdRequestV2, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
			Sign new user with email into database.
			Then after calling sign-up, it's possible to call all other endpoints by providing Firebase JWT token.
			Multiple invocations are possible, and do not have side effects.
		
 */
export const signUpUser = (params?: SignUpUserParams, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<void>({ url: `/users/sign-up`, method: 'POST', params }, options);
};

export const getSignUpUserMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof signUpUser>>,
    TError,
    { params?: SignUpUserParams },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<Awaited<ReturnType<typeof signUpUser>>, TError, { params?: SignUpUserParams }, TContext> => {
  const mutationKey = ['signUpUser'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof signUpUser>>, { params?: SignUpUserParams }> = (
    props,
  ) => {
    const { params } = props ?? {};

    return signUpUser(params, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SignUpUserMutationResult = NonNullable<Awaited<ReturnType<typeof signUpUser>>>;

export type SignUpUserMutationError = unknown;

export const useSignUpUser = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof signUpUser>>,
    TError,
    { params?: SignUpUserParams },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<Awaited<ReturnType<typeof signUpUser>>, TError, { params?: SignUpUserParams }, TContext> => {
  const mutationOptions = getSignUpUserMutationOptions(options);

  return useMutation(mutationOptions);
};

export const markWalletAsDefault = (walletId: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<void>({ url: `/users/me/wallets/${walletId}/mark-as-default`, method: 'POST' }, options);
};

export const getMarkWalletAsDefaultMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof markWalletAsDefault>>,
    TError,
    { walletId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<Awaited<ReturnType<typeof markWalletAsDefault>>, TError, { walletId: string }, TContext> => {
  const mutationKey = ['markWalletAsDefault'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof markWalletAsDefault>>, { walletId: string }> = (
    props,
  ) => {
    const { walletId } = props ?? {};

    return markWalletAsDefault(walletId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type MarkWalletAsDefaultMutationResult = NonNullable<Awaited<ReturnType<typeof markWalletAsDefault>>>;

export type MarkWalletAsDefaultMutationError = unknown;

export const useMarkWalletAsDefault = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof markWalletAsDefault>>,
    TError,
    { walletId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<Awaited<ReturnType<typeof markWalletAsDefault>>, TError, { walletId: string }, TContext> => {
  const mutationOptions = getMarkWalletAsDefaultMutationOptions(options);

  return useMutation(mutationOptions);
};

export const exportWallet = (
  walletId: string,
  exportWalletRequest: ExportWalletRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<ExportUserWalletResult>(
    {
      url: `/users/me/wallets/${walletId}/export`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: exportWalletRequest,
    },
    options,
  );
};

export const getExportWalletMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof exportWallet>>,
    TError,
    { walletId: string; data: ExportWalletRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof exportWallet>>,
  TError,
  { walletId: string; data: ExportWalletRequest },
  TContext
> => {
  const mutationKey = ['exportWallet'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof exportWallet>>,
    { walletId: string; data: ExportWalletRequest }
  > = (props) => {
    const { walletId, data } = props ?? {};

    return exportWallet(walletId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ExportWalletMutationResult = NonNullable<Awaited<ReturnType<typeof exportWallet>>>;
export type ExportWalletMutationBody = ExportWalletRequest;
export type ExportWalletMutationError = unknown;

export const useExportWallet = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof exportWallet>>,
    TError,
    { walletId: string; data: ExportWalletRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof exportWallet>>,
  TError,
  { walletId: string; data: ExportWalletRequest },
  TContext
> => {
  const mutationOptions = getExportWalletMutationOptions(options);

  return useMutation(mutationOptions);
};

export const getReferralRewardsSummary = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetReferralRewardsSummaryResult>({ url: `/users/me/referrals`, method: 'GET' }, options);
};

export const getGetReferralRewardsSummaryQueryKey = () => {
  return [`/users/me/referrals`] as const;
};

export const getGetReferralRewardsSummaryQueryOptions = <
  TData = Awaited<ReturnType<typeof getReferralRewardsSummary>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getReferralRewardsSummary>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetReferralRewardsSummaryQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getReferralRewardsSummary>>> = () =>
    getReferralRewardsSummary(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getReferralRewardsSummary>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetReferralRewardsSummaryQueryResult = NonNullable<Awaited<ReturnType<typeof getReferralRewardsSummary>>>;
export type GetReferralRewardsSummaryQueryError = unknown;

export function useGetReferralRewardsSummary<
  TData = Awaited<ReturnType<typeof getReferralRewardsSummary>>,
  TError = unknown,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getReferralRewardsSummary>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getReferralRewardsSummary>>,
        TError,
        Awaited<ReturnType<typeof getReferralRewardsSummary>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetReferralRewardsSummary<
  TData = Awaited<ReturnType<typeof getReferralRewardsSummary>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getReferralRewardsSummary>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getReferralRewardsSummary>>,
        TError,
        Awaited<ReturnType<typeof getReferralRewardsSummary>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetReferralRewardsSummary<
  TData = Awaited<ReturnType<typeof getReferralRewardsSummary>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getReferralRewardsSummary>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetReferralRewardsSummary<
  TData = Awaited<ReturnType<typeof getReferralRewardsSummary>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getReferralRewardsSummary>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetReferralRewardsSummaryQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const createReferralCode = (
  createNewReferralCodeRequest: CreateNewReferralCodeRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/users/me/referrals`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: createNewReferralCodeRequest,
    },
    options,
  );
};

export const getCreateReferralCodeMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createReferralCode>>,
    TError,
    { data: CreateNewReferralCodeRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof createReferralCode>>,
  TError,
  { data: CreateNewReferralCodeRequest },
  TContext
> => {
  const mutationKey = ['createReferralCode'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof createReferralCode>>,
    { data: CreateNewReferralCodeRequest }
  > = (props) => {
    const { data } = props ?? {};

    return createReferralCode(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateReferralCodeMutationResult = NonNullable<Awaited<ReturnType<typeof createReferralCode>>>;
export type CreateReferralCodeMutationBody = CreateNewReferralCodeRequest;
export type CreateReferralCodeMutationError = unknown;

export const useCreateReferralCode = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createReferralCode>>,
    TError,
    { data: CreateNewReferralCodeRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof createReferralCode>>,
  TError,
  { data: CreateNewReferralCodeRequest },
  TContext
> => {
  const mutationOptions = getCreateReferralCodeMutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
		Tries to claim everything on chain from referral program into user default wallet on that chain
	
 */
export const claimRewards = (chain: Chain, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<ClaimReferralRewardsResult>(
    { url: `/users/me/referrals/${chain}/claim-referral-reward`, method: 'POST' },
    options,
  );
};

export const getClaimRewardsMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof claimRewards>>, TError, { chain: Chain }, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<Awaited<ReturnType<typeof claimRewards>>, TError, { chain: Chain }, TContext> => {
  const mutationKey = ['claimRewards'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof claimRewards>>, { chain: Chain }> = (props) => {
    const { chain } = props ?? {};

    return claimRewards(chain, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ClaimRewardsMutationResult = NonNullable<Awaited<ReturnType<typeof claimRewards>>>;

export type ClaimRewardsMutationError = unknown;

export const useClaimRewards = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof claimRewards>>, TError, { chain: Chain }, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<Awaited<ReturnType<typeof claimRewards>>, TError, { chain: Chain }, TContext> => {
  const mutationOptions = getClaimRewardsMutationOptions(options);

  return useMutation(mutationOptions);
};

export const getAllUserLimitOrders = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetAllUserLimitOrdersResult[]>({ url: `/users/me/limit-orders`, method: 'GET' }, options);
};

export const getGetAllUserLimitOrdersQueryKey = () => {
  return [`/users/me/limit-orders`] as const;
};

export const getGetAllUserLimitOrdersQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllUserLimitOrders>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllUserLimitOrders>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllUserLimitOrdersQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllUserLimitOrders>>> = () =>
    getAllUserLimitOrders(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllUserLimitOrders>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetAllUserLimitOrdersQueryResult = NonNullable<Awaited<ReturnType<typeof getAllUserLimitOrders>>>;
export type GetAllUserLimitOrdersQueryError = unknown;

export function useGetAllUserLimitOrders<
  TData = Awaited<ReturnType<typeof getAllUserLimitOrders>>,
  TError = unknown,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllUserLimitOrders>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getAllUserLimitOrders>>,
        TError,
        Awaited<ReturnType<typeof getAllUserLimitOrders>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllUserLimitOrders<
  TData = Awaited<ReturnType<typeof getAllUserLimitOrders>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllUserLimitOrders>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getAllUserLimitOrders>>,
        TError,
        Awaited<ReturnType<typeof getAllUserLimitOrders>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllUserLimitOrders<
  TData = Awaited<ReturnType<typeof getAllUserLimitOrders>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllUserLimitOrders>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetAllUserLimitOrders<
  TData = Awaited<ReturnType<typeof getAllUserLimitOrders>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllUserLimitOrders>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetAllUserLimitOrdersQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const createLimitOrder = (
  createLimitOrderRequest: CreateLimitOrderRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/users/me/limit-orders`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: createLimitOrderRequest,
    },
    options,
  );
};

export const getCreateLimitOrderMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createLimitOrder>>,
    TError,
    { data: CreateLimitOrderRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof createLimitOrder>>,
  TError,
  { data: CreateLimitOrderRequest },
  TContext
> => {
  const mutationKey = ['createLimitOrder'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof createLimitOrder>>,
    { data: CreateLimitOrderRequest }
  > = (props) => {
    const { data } = props ?? {};

    return createLimitOrder(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateLimitOrderMutationResult = NonNullable<Awaited<ReturnType<typeof createLimitOrder>>>;
export type CreateLimitOrderMutationBody = CreateLimitOrderRequest;
export type CreateLimitOrderMutationError = unknown;

export const useCreateLimitOrder = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createLimitOrder>>,
    TError,
    { data: CreateLimitOrderRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof createLimitOrder>>,
  TError,
  { data: CreateLimitOrderRequest },
  TContext
> => {
  const mutationOptions = getCreateLimitOrderMutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * Claim user fatty tokens.
 */
export const claimFattyTokens = (userFattyLeagueId: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<void>(
    { url: `/users/me/fatty-league-seasons/${userFattyLeagueId}/claim`, method: 'POST' },
    options,
  );
};

export const getClaimFattyTokensMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof claimFattyTokens>>,
    TError,
    { userFattyLeagueId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof claimFattyTokens>>,
  TError,
  { userFattyLeagueId: string },
  TContext
> => {
  const mutationKey = ['claimFattyTokens'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof claimFattyTokens>>, { userFattyLeagueId: string }> = (
    props,
  ) => {
    const { userFattyLeagueId } = props ?? {};

    return claimFattyTokens(userFattyLeagueId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ClaimFattyTokensMutationResult = NonNullable<Awaited<ReturnType<typeof claimFattyTokens>>>;

export type ClaimFattyTokensMutationError = unknown;

export const useClaimFattyTokens = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof claimFattyTokens>>,
    TError,
    { userFattyLeagueId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof claimFattyTokens>>,
  TError,
  { userFattyLeagueId: string },
  TContext
> => {
  const mutationOptions = getClaimFattyTokensMutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * Claim rewards for fatty card.
 */
export const claimUserFattyCard = (userFattyCardId: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<void>({ url: `/users/me/fatty-cards/${userFattyCardId}/claim`, method: 'POST' }, options);
};

export const getClaimUserFattyCardMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof claimUserFattyCard>>,
    TError,
    { userFattyCardId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof claimUserFattyCard>>,
  TError,
  { userFattyCardId: string },
  TContext
> => {
  const mutationKey = ['claimUserFattyCard'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof claimUserFattyCard>>, { userFattyCardId: string }> = (
    props,
  ) => {
    const { userFattyCardId } = props ?? {};

    return claimUserFattyCard(userFattyCardId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ClaimUserFattyCardMutationResult = NonNullable<Awaited<ReturnType<typeof claimUserFattyCard>>>;

export type ClaimUserFattyCardMutationError = unknown;

export const useClaimUserFattyCard = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof claimUserFattyCard>>,
    TError,
    { userFattyCardId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof claimUserFattyCard>>,
  TError,
  { userFattyCardId: string },
  TContext
> => {
  const mutationOptions = getClaimUserFattyCardMutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * Search user fatty cards.
 */
export const searchUserFattyCards = (
  searchUserFattyCardRequest: SearchUserFattyCardRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<SearchUserFattyCardsResult[]>(
    {
      url: `/users/me/fatty-cards/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: searchUserFattyCardRequest,
    },
    options,
  );
};

export const getSearchUserFattyCardsQueryKey = (searchUserFattyCardRequest: SearchUserFattyCardRequest) => {
  return [`/users/me/fatty-cards/search`, searchUserFattyCardRequest] as const;
};

export const getSearchUserFattyCardsQueryOptions = <
  TData = Awaited<ReturnType<typeof searchUserFattyCards>>,
  TError = unknown,
>(
  searchUserFattyCardRequest: SearchUserFattyCardRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserFattyCards>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getSearchUserFattyCardsQueryKey(searchUserFattyCardRequest);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof searchUserFattyCards>>> = () =>
    searchUserFattyCards(searchUserFattyCardRequest, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof searchUserFattyCards>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchUserFattyCardsQueryResult = NonNullable<Awaited<ReturnType<typeof searchUserFattyCards>>>;
export type SearchUserFattyCardsQueryError = unknown;

export function useSearchUserFattyCards<TData = Awaited<ReturnType<typeof searchUserFattyCards>>, TError = unknown>(
  searchUserFattyCardRequest: SearchUserFattyCardRequest,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserFattyCards>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserFattyCards>>,
          TError,
          Awaited<ReturnType<typeof searchUserFattyCards>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserFattyCards<TData = Awaited<ReturnType<typeof searchUserFattyCards>>, TError = unknown>(
  searchUserFattyCardRequest: SearchUserFattyCardRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserFattyCards>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserFattyCards>>,
          TError,
          Awaited<ReturnType<typeof searchUserFattyCards>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserFattyCards<TData = Awaited<ReturnType<typeof searchUserFattyCards>>, TError = unknown>(
  searchUserFattyCardRequest: SearchUserFattyCardRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserFattyCards>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchUserFattyCards<TData = Awaited<ReturnType<typeof searchUserFattyCards>>, TError = unknown>(
  searchUserFattyCardRequest: SearchUserFattyCardRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserFattyCards>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchUserFattyCardsQueryOptions(searchUserFattyCardRequest, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Display fatty cards.
 */
export const displayUserFattyCards = (
  displayUserFattyCardsRequest: DisplayUserFattyCardsRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/users/me/fatty-cards/display`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: displayUserFattyCardsRequest,
    },
    options,
  );
};

export const getDisplayUserFattyCardsMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof displayUserFattyCards>>,
    TError,
    { data: DisplayUserFattyCardsRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof displayUserFattyCards>>,
  TError,
  { data: DisplayUserFattyCardsRequest },
  TContext
> => {
  const mutationKey = ['displayUserFattyCards'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof displayUserFattyCards>>,
    { data: DisplayUserFattyCardsRequest }
  > = (props) => {
    const { data } = props ?? {};

    return displayUserFattyCards(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DisplayUserFattyCardsMutationResult = NonNullable<Awaited<ReturnType<typeof displayUserFattyCards>>>;
export type DisplayUserFattyCardsMutationBody = DisplayUserFattyCardsRequest;
export type DisplayUserFattyCardsMutationError = unknown;

export const useDisplayUserFattyCards = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof displayUserFattyCards>>,
    TError,
    { data: DisplayUserFattyCardsRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof displayUserFattyCards>>,
  TError,
  { data: DisplayUserFattyCardsRequest },
  TContext
> => {
  const mutationOptions = getDisplayUserFattyCardsMutationOptions(options);

  return useMutation(mutationOptions);
};

export const withdrawFromBotPortfolio = (
  botId: string,
  withdrawFromBotPortfolioRequest: WithdrawFromBotPortfolioRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<WithdrawFromBotPortfolioResult>(
    {
      url: `/users/me/bots/${botId}/withdraw/portfolio`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: withdrawFromBotPortfolioRequest,
    },
    options,
  );
};

export const getWithdrawFromBotPortfolioMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof withdrawFromBotPortfolio>>,
    TError,
    { botId: string; data: WithdrawFromBotPortfolioRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof withdrawFromBotPortfolio>>,
  TError,
  { botId: string; data: WithdrawFromBotPortfolioRequest },
  TContext
> => {
  const mutationKey = ['withdrawFromBotPortfolio'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof withdrawFromBotPortfolio>>,
    { botId: string; data: WithdrawFromBotPortfolioRequest }
  > = (props) => {
    const { botId, data } = props ?? {};

    return withdrawFromBotPortfolio(botId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type WithdrawFromBotPortfolioMutationResult = NonNullable<Awaited<ReturnType<typeof withdrawFromBotPortfolio>>>;
export type WithdrawFromBotPortfolioMutationBody = WithdrawFromBotPortfolioRequest;
export type WithdrawFromBotPortfolioMutationError = unknown;

export const useWithdrawFromBotPortfolio = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof withdrawFromBotPortfolio>>,
    TError,
    { botId: string; data: WithdrawFromBotPortfolioRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof withdrawFromBotPortfolio>>,
  TError,
  { botId: string; data: WithdrawFromBotPortfolioRequest },
  TContext
> => {
  const mutationOptions = getWithdrawFromBotPortfolioMutationOptions(options);

  return useMutation(mutationOptions);
};

export const getBotWithdrawGasEstimation = (
  botId: string,
  getBotWithdrawGasEstimationRequest: GetBotWithdrawGasEstimationRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetBotWithdrawGasEstimationResult>(
    {
      url: `/users/me/bots/${botId}/gas-estimation/withdraw/usd`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: getBotWithdrawGasEstimationRequest,
    },
    options,
  );
};

export const getGetBotWithdrawGasEstimationQueryKey = (
  botId: string,
  getBotWithdrawGasEstimationRequest: GetBotWithdrawGasEstimationRequest,
) => {
  return [`/users/me/bots/${botId}/gas-estimation/withdraw/usd`, getBotWithdrawGasEstimationRequest] as const;
};

export const getGetBotWithdrawGasEstimationQueryOptions = <
  TData = Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>,
  TError = unknown,
>(
  botId: string,
  getBotWithdrawGasEstimationRequest: GetBotWithdrawGasEstimationRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetBotWithdrawGasEstimationQueryKey(botId, getBotWithdrawGasEstimationRequest);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>> = () =>
    getBotWithdrawGasEstimation(botId, getBotWithdrawGasEstimationRequest, requestOptions);

  return { queryKey, queryFn, enabled: !!botId, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetBotWithdrawGasEstimationQueryResult = NonNullable<
  Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>
>;
export type GetBotWithdrawGasEstimationQueryError = unknown;

export function useGetBotWithdrawGasEstimation<
  TData = Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>,
  TError = unknown,
>(
  botId: string,
  getBotWithdrawGasEstimationRequest: GetBotWithdrawGasEstimationRequest,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>,
          TError,
          Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetBotWithdrawGasEstimation<
  TData = Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>,
  TError = unknown,
>(
  botId: string,
  getBotWithdrawGasEstimationRequest: GetBotWithdrawGasEstimationRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>,
          TError,
          Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetBotWithdrawGasEstimation<
  TData = Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>,
  TError = unknown,
>(
  botId: string,
  getBotWithdrawGasEstimationRequest: GetBotWithdrawGasEstimationRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetBotWithdrawGasEstimation<
  TData = Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>,
  TError = unknown,
>(
  botId: string,
  getBotWithdrawGasEstimationRequest: GetBotWithdrawGasEstimationRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotWithdrawGasEstimation>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetBotWithdrawGasEstimationQueryOptions(botId, getBotWithdrawGasEstimationRequest, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const searchUserBotsTransactions = (
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params?: SearchUserBotsTransactionsParams,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceSearchUserBotsTransactionsResultUUID>(
    {
      url: `/users/me/bots/transactions/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: searchUserBotsTransactionsRequest,
      params,
    },
    options,
  );
};

export const getSearchUserBotsTransactionsQueryKey = (
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params?: SearchUserBotsTransactionsParams,
) => {
  return [
    `/users/me/bots/transactions/search`,
    ...(params ? [params] : []),
    searchUserBotsTransactionsRequest,
  ] as const;
};

export const getSearchUserBotsTransactionsInfiniteQueryOptions = <
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserBotsTransactions>>,
    SearchUserBotsTransactionsParams['lastId']
  >,
  TError = unknown,
>(
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params?: SearchUserBotsTransactionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserBotsTransactions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserBotsTransactions>>,
        QueryKey,
        SearchUserBotsTransactionsParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getSearchUserBotsTransactionsQueryKey(searchUserBotsTransactionsRequest, params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof searchUserBotsTransactions>>,
    QueryKey,
    SearchUserBotsTransactionsParams['lastId']
  > = ({ pageParam }) =>
    searchUserBotsTransactions(
      searchUserBotsTransactionsRequest,
      { ...params, lastId: pageParam || params?.['lastId'] },
      requestOptions,
    );

  return { queryKey, queryFn, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof searchUserBotsTransactions>>,
    TError,
    TData,
    Awaited<ReturnType<typeof searchUserBotsTransactions>>,
    QueryKey,
    SearchUserBotsTransactionsParams['lastId']
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchUserBotsTransactionsInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof searchUserBotsTransactions>>
>;
export type SearchUserBotsTransactionsInfiniteQueryError = unknown;

export function useSearchUserBotsTransactionsInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserBotsTransactions>>,
    SearchUserBotsTransactionsParams['lastId']
  >,
  TError = unknown,
>(
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params: undefined | SearchUserBotsTransactionsParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserBotsTransactions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserBotsTransactions>>,
        QueryKey,
        SearchUserBotsTransactionsParams['lastId']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserBotsTransactions>>,
          TError,
          Awaited<ReturnType<typeof searchUserBotsTransactions>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserBotsTransactionsInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserBotsTransactions>>,
    SearchUserBotsTransactionsParams['lastId']
  >,
  TError = unknown,
>(
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params?: SearchUserBotsTransactionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserBotsTransactions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserBotsTransactions>>,
        QueryKey,
        SearchUserBotsTransactionsParams['lastId']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserBotsTransactions>>,
          TError,
          Awaited<ReturnType<typeof searchUserBotsTransactions>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserBotsTransactionsInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserBotsTransactions>>,
    SearchUserBotsTransactionsParams['lastId']
  >,
  TError = unknown,
>(
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params?: SearchUserBotsTransactionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserBotsTransactions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserBotsTransactions>>,
        QueryKey,
        SearchUserBotsTransactionsParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchUserBotsTransactionsInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserBotsTransactions>>,
    SearchUserBotsTransactionsParams['lastId']
  >,
  TError = unknown,
>(
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params?: SearchUserBotsTransactionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserBotsTransactions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserBotsTransactions>>,
        QueryKey,
        SearchUserBotsTransactionsParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchUserBotsTransactionsInfiniteQueryOptions(
    searchUserBotsTransactionsRequest,
    params,
    options,
  );

  const query = useInfiniteQuery(queryOptions) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getSearchUserBotsTransactionsQueryOptions = <
  TData = Awaited<ReturnType<typeof searchUserBotsTransactions>>,
  TError = unknown,
>(
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params?: SearchUserBotsTransactionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotsTransactions>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getSearchUserBotsTransactionsQueryKey(searchUserBotsTransactionsRequest, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof searchUserBotsTransactions>>> = () =>
    searchUserBotsTransactions(searchUserBotsTransactionsRequest, params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof searchUserBotsTransactions>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchUserBotsTransactionsQueryResult = NonNullable<Awaited<ReturnType<typeof searchUserBotsTransactions>>>;
export type SearchUserBotsTransactionsQueryError = unknown;

export function useSearchUserBotsTransactions<
  TData = Awaited<ReturnType<typeof searchUserBotsTransactions>>,
  TError = unknown,
>(
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params: undefined | SearchUserBotsTransactionsParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotsTransactions>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserBotsTransactions>>,
          TError,
          Awaited<ReturnType<typeof searchUserBotsTransactions>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserBotsTransactions<
  TData = Awaited<ReturnType<typeof searchUserBotsTransactions>>,
  TError = unknown,
>(
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params?: SearchUserBotsTransactionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotsTransactions>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserBotsTransactions>>,
          TError,
          Awaited<ReturnType<typeof searchUserBotsTransactions>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserBotsTransactions<
  TData = Awaited<ReturnType<typeof searchUserBotsTransactions>>,
  TError = unknown,
>(
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params?: SearchUserBotsTransactionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotsTransactions>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchUserBotsTransactions<
  TData = Awaited<ReturnType<typeof searchUserBotsTransactions>>,
  TError = unknown,
>(
  searchUserBotsTransactionsRequest: SearchUserBotsTransactionsRequest,
  params?: SearchUserBotsTransactionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotsTransactions>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchUserBotsTransactionsQueryOptions(searchUserBotsTransactionsRequest, params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const searchUserBotDetail = (
  searchUserBotDetailRequest: SearchUserBotDetailRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<SearchUserBotDetailResult>(
    {
      url: `/users/me/bots/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: searchUserBotDetailRequest,
    },
    options,
  );
};

export const getSearchUserBotDetailQueryKey = (searchUserBotDetailRequest: SearchUserBotDetailRequest) => {
  return [`/users/me/bots/search`, searchUserBotDetailRequest] as const;
};

export const getSearchUserBotDetailQueryOptions = <
  TData = Awaited<ReturnType<typeof searchUserBotDetail>>,
  TError = unknown,
>(
  searchUserBotDetailRequest: SearchUserBotDetailRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotDetail>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getSearchUserBotDetailQueryKey(searchUserBotDetailRequest);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof searchUserBotDetail>>> = () =>
    searchUserBotDetail(searchUserBotDetailRequest, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof searchUserBotDetail>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchUserBotDetailQueryResult = NonNullable<Awaited<ReturnType<typeof searchUserBotDetail>>>;
export type SearchUserBotDetailQueryError = unknown;

export function useSearchUserBotDetail<TData = Awaited<ReturnType<typeof searchUserBotDetail>>, TError = unknown>(
  searchUserBotDetailRequest: SearchUserBotDetailRequest,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotDetail>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserBotDetail>>,
          TError,
          Awaited<ReturnType<typeof searchUserBotDetail>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserBotDetail<TData = Awaited<ReturnType<typeof searchUserBotDetail>>, TError = unknown>(
  searchUserBotDetailRequest: SearchUserBotDetailRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotDetail>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserBotDetail>>,
          TError,
          Awaited<ReturnType<typeof searchUserBotDetail>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserBotDetail<TData = Awaited<ReturnType<typeof searchUserBotDetail>>, TError = unknown>(
  searchUserBotDetailRequest: SearchUserBotDetailRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotDetail>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchUserBotDetail<TData = Awaited<ReturnType<typeof searchUserBotDetail>>, TError = unknown>(
  searchUserBotDetailRequest: SearchUserBotDetailRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotDetail>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchUserBotDetailQueryOptions(searchUserBotDetailRequest, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const searchUserBotMarketPositions = (
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params?: SearchUserBotMarketPositionsParams,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceSearchBotMarketPositionResultUUID>(
    {
      url: `/users/me/bots/market-positions/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: searchUserBotMarketPositionsRequest,
      params,
    },
    options,
  );
};

export const getSearchUserBotMarketPositionsQueryKey = (
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params?: SearchUserBotMarketPositionsParams,
) => {
  return [
    `/users/me/bots/market-positions/search`,
    ...(params ? [params] : []),
    searchUserBotMarketPositionsRequest,
  ] as const;
};

export const getSearchUserBotMarketPositionsInfiniteQueryOptions = <
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
    SearchUserBotMarketPositionsParams['lastId']
  >,
  TError = unknown,
>(
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params?: SearchUserBotMarketPositionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
        QueryKey,
        SearchUserBotMarketPositionsParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getSearchUserBotMarketPositionsQueryKey(searchUserBotMarketPositionsRequest, params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
    QueryKey,
    SearchUserBotMarketPositionsParams['lastId']
  > = ({ pageParam }) =>
    searchUserBotMarketPositions(
      searchUserBotMarketPositionsRequest,
      { ...params, lastId: pageParam || params?.['lastId'] },
      requestOptions,
    );

  return { queryKey, queryFn, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
    TError,
    TData,
    Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
    QueryKey,
    SearchUserBotMarketPositionsParams['lastId']
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchUserBotMarketPositionsInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof searchUserBotMarketPositions>>
>;
export type SearchUserBotMarketPositionsInfiniteQueryError = unknown;

export function useSearchUserBotMarketPositionsInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
    SearchUserBotMarketPositionsParams['lastId']
  >,
  TError = unknown,
>(
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params: undefined | SearchUserBotMarketPositionsParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
        QueryKey,
        SearchUserBotMarketPositionsParams['lastId']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
          TError,
          Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserBotMarketPositionsInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
    SearchUserBotMarketPositionsParams['lastId']
  >,
  TError = unknown,
>(
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params?: SearchUserBotMarketPositionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
        QueryKey,
        SearchUserBotMarketPositionsParams['lastId']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
          TError,
          Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserBotMarketPositionsInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
    SearchUserBotMarketPositionsParams['lastId']
  >,
  TError = unknown,
>(
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params?: SearchUserBotMarketPositionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
        QueryKey,
        SearchUserBotMarketPositionsParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchUserBotMarketPositionsInfinite<
  TData = InfiniteData<
    Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
    SearchUserBotMarketPositionsParams['lastId']
  >,
  TError = unknown,
>(
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params?: SearchUserBotMarketPositionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
        QueryKey,
        SearchUserBotMarketPositionsParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchUserBotMarketPositionsInfiniteQueryOptions(
    searchUserBotMarketPositionsRequest,
    params,
    options,
  );

  const query = useInfiniteQuery(queryOptions) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getSearchUserBotMarketPositionsQueryOptions = <
  TData = Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
  TError = unknown,
>(
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params?: SearchUserBotMarketPositionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotMarketPositions>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getSearchUserBotMarketPositionsQueryKey(searchUserBotMarketPositionsRequest, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof searchUserBotMarketPositions>>> = () =>
    searchUserBotMarketPositions(searchUserBotMarketPositionsRequest, params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchUserBotMarketPositionsQueryResult = NonNullable<
  Awaited<ReturnType<typeof searchUserBotMarketPositions>>
>;
export type SearchUserBotMarketPositionsQueryError = unknown;

export function useSearchUserBotMarketPositions<
  TData = Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
  TError = unknown,
>(
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params: undefined | SearchUserBotMarketPositionsParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotMarketPositions>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
          TError,
          Awaited<ReturnType<typeof searchUserBotMarketPositions>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserBotMarketPositions<
  TData = Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
  TError = unknown,
>(
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params?: SearchUserBotMarketPositionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotMarketPositions>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
          TError,
          Awaited<ReturnType<typeof searchUserBotMarketPositions>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserBotMarketPositions<
  TData = Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
  TError = unknown,
>(
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params?: SearchUserBotMarketPositionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotMarketPositions>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchUserBotMarketPositions<
  TData = Awaited<ReturnType<typeof searchUserBotMarketPositions>>,
  TError = unknown,
>(
  searchUserBotMarketPositionsRequest: SearchUserBotMarketPositionsRequest,
  params?: SearchUserBotMarketPositionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserBotMarketPositions>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchUserBotMarketPositionsQueryOptions(
    searchUserBotMarketPositionsRequest,
    params,
    options,
  );

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getTaxV2 = (
  tokenAddress: string,
  getTokenTaxRequestV2: GetTokenTaxRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetTokenTaxResult>(
    {
      url: `/public/v2/tokens/${tokenAddress}/tax`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: getTokenTaxRequestV2,
    },
    options,
  );
};

export const getGetTaxV2QueryKey = (tokenAddress: string, getTokenTaxRequestV2: GetTokenTaxRequestV2) => {
  return [`/public/v2/tokens/${tokenAddress}/tax`, getTokenTaxRequestV2] as const;
};

export const getGetTaxV2QueryOptions = <TData = Awaited<ReturnType<typeof getTaxV2>>, TError = unknown>(
  tokenAddress: string,
  getTokenTaxRequestV2: GetTokenTaxRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTaxV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetTaxV2QueryKey(tokenAddress, getTokenTaxRequestV2);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getTaxV2>>> = () =>
    getTaxV2(tokenAddress, getTokenTaxRequestV2, requestOptions);

  return { queryKey, queryFn, enabled: !!tokenAddress, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getTaxV2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetTaxV2QueryResult = NonNullable<Awaited<ReturnType<typeof getTaxV2>>>;
export type GetTaxV2QueryError = unknown;

export function useGetTaxV2<TData = Awaited<ReturnType<typeof getTaxV2>>, TError = unknown>(
  tokenAddress: string,
  getTokenTaxRequestV2: GetTokenTaxRequestV2,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTaxV2>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<Awaited<ReturnType<typeof getTaxV2>>, TError, Awaited<ReturnType<typeof getTaxV2>>>,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTaxV2<TData = Awaited<ReturnType<typeof getTaxV2>>, TError = unknown>(
  tokenAddress: string,
  getTokenTaxRequestV2: GetTokenTaxRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTaxV2>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<Awaited<ReturnType<typeof getTaxV2>>, TError, Awaited<ReturnType<typeof getTaxV2>>>,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTaxV2<TData = Awaited<ReturnType<typeof getTaxV2>>, TError = unknown>(
  tokenAddress: string,
  getTokenTaxRequestV2: GetTokenTaxRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTaxV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetTaxV2<TData = Awaited<ReturnType<typeof getTaxV2>>, TError = unknown>(
  tokenAddress: string,
  getTokenTaxRequestV2: GetTokenTaxRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTaxV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetTaxV2QueryOptions(tokenAddress, getTokenTaxRequestV2, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getPredictedEthAmountOnSellV2 = (
  tokenAddress: string,
  getPredictedEthAmountOnSellRequestV2: GetPredictedEthAmountOnSellRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetPredictedNativeAmountOnSellResult>(
    {
      url: `/public/v2/tokens/${tokenAddress}/prediction/sell`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: getPredictedEthAmountOnSellRequestV2,
    },
    options,
  );
};

export const getGetPredictedEthAmountOnSellV2QueryKey = (
  tokenAddress: string,
  getPredictedEthAmountOnSellRequestV2: GetPredictedEthAmountOnSellRequestV2,
) => {
  return [`/public/v2/tokens/${tokenAddress}/prediction/sell`, getPredictedEthAmountOnSellRequestV2] as const;
};

export const getGetPredictedEthAmountOnSellV2QueryOptions = <
  TData = Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>,
  TError = unknown,
>(
  tokenAddress: string,
  getPredictedEthAmountOnSellRequestV2: GetPredictedEthAmountOnSellRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getGetPredictedEthAmountOnSellV2QueryKey(tokenAddress, getPredictedEthAmountOnSellRequestV2);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>> = () =>
    getPredictedEthAmountOnSellV2(tokenAddress, getPredictedEthAmountOnSellRequestV2, requestOptions);

  return { queryKey, queryFn, enabled: !!tokenAddress, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetPredictedEthAmountOnSellV2QueryResult = NonNullable<
  Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>
>;
export type GetPredictedEthAmountOnSellV2QueryError = unknown;

export function useGetPredictedEthAmountOnSellV2<
  TData = Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>,
  TError = unknown,
>(
  tokenAddress: string,
  getPredictedEthAmountOnSellRequestV2: GetPredictedEthAmountOnSellRequestV2,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>,
          TError,
          Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetPredictedEthAmountOnSellV2<
  TData = Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>,
  TError = unknown,
>(
  tokenAddress: string,
  getPredictedEthAmountOnSellRequestV2: GetPredictedEthAmountOnSellRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>,
          TError,
          Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetPredictedEthAmountOnSellV2<
  TData = Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>,
  TError = unknown,
>(
  tokenAddress: string,
  getPredictedEthAmountOnSellRequestV2: GetPredictedEthAmountOnSellRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetPredictedEthAmountOnSellV2<
  TData = Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>,
  TError = unknown,
>(
  tokenAddress: string,
  getPredictedEthAmountOnSellRequestV2: GetPredictedEthAmountOnSellRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPredictedEthAmountOnSellV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetPredictedEthAmountOnSellV2QueryOptions(
    tokenAddress,
    getPredictedEthAmountOnSellRequestV2,
    options,
  );

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getPredictedTokenAmountOnBuyV2 = (
  tokenAddress: string,
  getPredictedTokenAmountOnBuyRequestV2: GetPredictedTokenAmountOnBuyRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetPredictedTokenAmountOnBuyResult>(
    {
      url: `/public/v2/tokens/${tokenAddress}/prediction/buy`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: getPredictedTokenAmountOnBuyRequestV2,
    },
    options,
  );
};

export const getGetPredictedTokenAmountOnBuyV2QueryKey = (
  tokenAddress: string,
  getPredictedTokenAmountOnBuyRequestV2: GetPredictedTokenAmountOnBuyRequestV2,
) => {
  return [`/public/v2/tokens/${tokenAddress}/prediction/buy`, getPredictedTokenAmountOnBuyRequestV2] as const;
};

export const getGetPredictedTokenAmountOnBuyV2QueryOptions = <
  TData = Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>,
  TError = unknown,
>(
  tokenAddress: string,
  getPredictedTokenAmountOnBuyRequestV2: GetPredictedTokenAmountOnBuyRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getGetPredictedTokenAmountOnBuyV2QueryKey(tokenAddress, getPredictedTokenAmountOnBuyRequestV2);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>> = () =>
    getPredictedTokenAmountOnBuyV2(tokenAddress, getPredictedTokenAmountOnBuyRequestV2, requestOptions);

  return { queryKey, queryFn, enabled: !!tokenAddress, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetPredictedTokenAmountOnBuyV2QueryResult = NonNullable<
  Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>
>;
export type GetPredictedTokenAmountOnBuyV2QueryError = unknown;

export function useGetPredictedTokenAmountOnBuyV2<
  TData = Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>,
  TError = unknown,
>(
  tokenAddress: string,
  getPredictedTokenAmountOnBuyRequestV2: GetPredictedTokenAmountOnBuyRequestV2,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>,
          TError,
          Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetPredictedTokenAmountOnBuyV2<
  TData = Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>,
  TError = unknown,
>(
  tokenAddress: string,
  getPredictedTokenAmountOnBuyRequestV2: GetPredictedTokenAmountOnBuyRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>,
          TError,
          Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetPredictedTokenAmountOnBuyV2<
  TData = Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>,
  TError = unknown,
>(
  tokenAddress: string,
  getPredictedTokenAmountOnBuyRequestV2: GetPredictedTokenAmountOnBuyRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetPredictedTokenAmountOnBuyV2<
  TData = Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>,
  TError = unknown,
>(
  tokenAddress: string,
  getPredictedTokenAmountOnBuyRequestV2: GetPredictedTokenAmountOnBuyRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPredictedTokenAmountOnBuyV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetPredictedTokenAmountOnBuyV2QueryOptions(
    tokenAddress,
    getPredictedTokenAmountOnBuyRequestV2,
    options,
  );

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getTokenHistoricalCandleChart = (
  tokenAddress: string,
  getTokenHistoricalCandleChartRequest: GetTokenHistoricalCandleChartRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<TokenOHLCTimeIntervalItem[]>(
    {
      url: `/public/v2/tokens/${tokenAddress}/historical-candle-chart`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: getTokenHistoricalCandleChartRequest,
    },
    options,
  );
};

export const getGetTokenHistoricalCandleChartQueryKey = (
  tokenAddress: string,
  getTokenHistoricalCandleChartRequest: GetTokenHistoricalCandleChartRequest,
) => {
  return [`/public/v2/tokens/${tokenAddress}/historical-candle-chart`, getTokenHistoricalCandleChartRequest] as const;
};

export const getGetTokenHistoricalCandleChartQueryOptions = <
  TData = Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>,
  TError = unknown,
>(
  tokenAddress: string,
  getTokenHistoricalCandleChartRequest: GetTokenHistoricalCandleChartRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getGetTokenHistoricalCandleChartQueryKey(tokenAddress, getTokenHistoricalCandleChartRequest);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>> = () =>
    getTokenHistoricalCandleChart(tokenAddress, getTokenHistoricalCandleChartRequest, requestOptions);

  return { queryKey, queryFn, enabled: !!tokenAddress, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetTokenHistoricalCandleChartQueryResult = NonNullable<
  Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>
>;
export type GetTokenHistoricalCandleChartQueryError = unknown;

export function useGetTokenHistoricalCandleChart<
  TData = Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>,
  TError = unknown,
>(
  tokenAddress: string,
  getTokenHistoricalCandleChartRequest: GetTokenHistoricalCandleChartRequest,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>,
          TError,
          Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTokenHistoricalCandleChart<
  TData = Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>,
  TError = unknown,
>(
  tokenAddress: string,
  getTokenHistoricalCandleChartRequest: GetTokenHistoricalCandleChartRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>,
          TError,
          Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTokenHistoricalCandleChart<
  TData = Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>,
  TError = unknown,
>(
  tokenAddress: string,
  getTokenHistoricalCandleChartRequest: GetTokenHistoricalCandleChartRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetTokenHistoricalCandleChart<
  TData = Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>,
  TError = unknown,
>(
  tokenAddress: string,
  getTokenHistoricalCandleChartRequest: GetTokenHistoricalCandleChartRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenHistoricalCandleChart>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetTokenHistoricalCandleChartQueryOptions(
    tokenAddress,
    getTokenHistoricalCandleChartRequest,
    options,
  );

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getTokenPriceChartV2 = (
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetTokenPriceChartResult>(
    {
      url: `/public/v2/tokens/${pairAddress}/price-chart`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: getTokenPriceChartRequestV2,
    },
    options,
  );
};

export const getGetTokenPriceChartV2QueryKey = (
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
) => {
  return [`/public/v2/tokens/${pairAddress}/price-chart`, getTokenPriceChartRequestV2] as const;
};

export const getGetTokenPriceChartV2QueryOptions = <
  TData = Awaited<ReturnType<typeof getTokenPriceChartV2>>,
  TError = unknown,
>(
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenPriceChartV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetTokenPriceChartV2QueryKey(pairAddress, getTokenPriceChartRequestV2);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getTokenPriceChartV2>>> = () =>
    getTokenPriceChartV2(pairAddress, getTokenPriceChartRequestV2, requestOptions);

  return { queryKey, queryFn, enabled: !!pairAddress, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getTokenPriceChartV2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetTokenPriceChartV2QueryResult = NonNullable<Awaited<ReturnType<typeof getTokenPriceChartV2>>>;
export type GetTokenPriceChartV2QueryError = unknown;

export function useGetTokenPriceChartV2<TData = Awaited<ReturnType<typeof getTokenPriceChartV2>>, TError = unknown>(
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenPriceChartV2>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getTokenPriceChartV2>>,
          TError,
          Awaited<ReturnType<typeof getTokenPriceChartV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTokenPriceChartV2<TData = Awaited<ReturnType<typeof getTokenPriceChartV2>>, TError = unknown>(
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenPriceChartV2>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getTokenPriceChartV2>>,
          TError,
          Awaited<ReturnType<typeof getTokenPriceChartV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTokenPriceChartV2<TData = Awaited<ReturnType<typeof getTokenPriceChartV2>>, TError = unknown>(
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenPriceChartV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetTokenPriceChartV2<TData = Awaited<ReturnType<typeof getTokenPriceChartV2>>, TError = unknown>(
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenPriceChartV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetTokenPriceChartV2QueryOptions(pairAddress, getTokenPriceChartRequestV2, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getContinuousTokenPriceChartV2 = (
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetContinuousTokenPriceChartResult>(
    {
      url: `/public/v2/tokens/${pairAddress}/continuous-price-chart`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: getTokenPriceChartRequestV2,
    },
    options,
  );
};

export const getGetContinuousTokenPriceChartV2QueryKey = (
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
) => {
  return [`/public/v2/tokens/${pairAddress}/continuous-price-chart`, getTokenPriceChartRequestV2] as const;
};

export const getGetContinuousTokenPriceChartV2QueryOptions = <
  TData = Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>,
  TError = unknown,
>(
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetContinuousTokenPriceChartV2QueryKey(pairAddress, getTokenPriceChartRequestV2);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>> = () =>
    getContinuousTokenPriceChartV2(pairAddress, getTokenPriceChartRequestV2, requestOptions);

  return { queryKey, queryFn, enabled: !!pairAddress, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetContinuousTokenPriceChartV2QueryResult = NonNullable<
  Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>
>;
export type GetContinuousTokenPriceChartV2QueryError = unknown;

export function useGetContinuousTokenPriceChartV2<
  TData = Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>,
  TError = unknown,
>(
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>,
          TError,
          Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetContinuousTokenPriceChartV2<
  TData = Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>,
  TError = unknown,
>(
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>,
          TError,
          Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetContinuousTokenPriceChartV2<
  TData = Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>,
  TError = unknown,
>(
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetContinuousTokenPriceChartV2<
  TData = Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>,
  TError = unknown,
>(
  pairAddress: string,
  getTokenPriceChartRequestV2: GetTokenPriceChartRequestV2,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getContinuousTokenPriceChartV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetContinuousTokenPriceChartV2QueryOptions(pairAddress, getTokenPriceChartRequestV2, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const botsLeaderboard = (
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboardParams,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceBotsLeaderboardSearchResultBigDecimal>(
    {
      url: `/public/bots/leaderboard/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: botsLeaderboardSearchRequest,
      params,
    },
    options,
  );
};

export const getBotsLeaderboardQueryKey = (
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboardParams,
) => {
  return [`/public/bots/leaderboard/search`, ...(params ? [params] : []), botsLeaderboardSearchRequest] as const;
};

export const getBotsLeaderboardQueryOptions = <TData = Awaited<ReturnType<typeof botsLeaderboard>>, TError = unknown>(
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboardParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsLeaderboard>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getBotsLeaderboardQueryKey(botsLeaderboardSearchRequest, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof botsLeaderboard>>> = () =>
    botsLeaderboard(botsLeaderboardSearchRequest, params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof botsLeaderboard>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type BotsLeaderboardQueryResult = NonNullable<Awaited<ReturnType<typeof botsLeaderboard>>>;
export type BotsLeaderboardQueryError = unknown;

export function useBotsLeaderboard<TData = Awaited<ReturnType<typeof botsLeaderboard>>, TError = unknown>(
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params: undefined | BotsLeaderboardParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsLeaderboard>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof botsLeaderboard>>,
          TError,
          Awaited<ReturnType<typeof botsLeaderboard>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useBotsLeaderboard<TData = Awaited<ReturnType<typeof botsLeaderboard>>, TError = unknown>(
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboardParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsLeaderboard>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof botsLeaderboard>>,
          TError,
          Awaited<ReturnType<typeof botsLeaderboard>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useBotsLeaderboard<TData = Awaited<ReturnType<typeof botsLeaderboard>>, TError = unknown>(
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboardParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsLeaderboard>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useBotsLeaderboard<TData = Awaited<ReturnType<typeof botsLeaderboard>>, TError = unknown>(
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboardParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsLeaderboard>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getBotsLeaderboardQueryOptions(botsLeaderboardSearchRequest, params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const newTokenStates = (
  tokenStateChangesRequest: TokenStateChangesRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/internal/aggregator/token-state-changes`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: tokenStateChangesRequest,
    },
    options,
  );
};

export const getNewTokenStatesMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof newTokenStates>>,
    TError,
    { data: TokenStateChangesRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof newTokenStates>>,
  TError,
  { data: TokenStateChangesRequest },
  TContext
> => {
  const mutationKey = ['newTokenStates'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof newTokenStates>>, { data: TokenStateChangesRequest }> = (
    props,
  ) => {
    const { data } = props ?? {};

    return newTokenStates(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type NewTokenStatesMutationResult = NonNullable<Awaited<ReturnType<typeof newTokenStates>>>;
export type NewTokenStatesMutationBody = TokenStateChangesRequest;
export type NewTokenStatesMutationError = unknown;

export const useNewTokenStates = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof newTokenStates>>,
    TError,
    { data: TokenStateChangesRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof newTokenStates>>,
  TError,
  { data: TokenStateChangesRequest },
  TContext
> => {
  const mutationOptions = getNewTokenStatesMutationOptions(options);

  return useMutation(mutationOptions);
};

export const tokenRedFlagged = (
  tokenRedFlaggedRequest: TokenRedFlaggedRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/internal/aggregator/token-red-flagged`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: tokenRedFlaggedRequest,
    },
    options,
  );
};

export const getTokenRedFlaggedMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof tokenRedFlagged>>,
    TError,
    { data: TokenRedFlaggedRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof tokenRedFlagged>>,
  TError,
  { data: TokenRedFlaggedRequest },
  TContext
> => {
  const mutationKey = ['tokenRedFlagged'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof tokenRedFlagged>>, { data: TokenRedFlaggedRequest }> = (
    props,
  ) => {
    const { data } = props ?? {};

    return tokenRedFlagged(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type TokenRedFlaggedMutationResult = NonNullable<Awaited<ReturnType<typeof tokenRedFlagged>>>;
export type TokenRedFlaggedMutationBody = TokenRedFlaggedRequest;
export type TokenRedFlaggedMutationError = unknown;

export const useTokenRedFlagged = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof tokenRedFlagged>>,
    TError,
    { data: TokenRedFlaggedRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof tokenRedFlagged>>,
  TError,
  { data: TokenRedFlaggedRequest },
  TContext
> => {
  const mutationOptions = getTokenRedFlaggedMutationOptions(options);

  return useMutation(mutationOptions);
};

export const tokenPromotedToPumpswap = (
  tokenPromotedToPumpswapRequest: TokenPromotedToPumpswapRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/internal/aggregator/token-promoted-to-pumpswap`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: tokenPromotedToPumpswapRequest,
    },
    options,
  );
};

export const getTokenPromotedToPumpswapMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof tokenPromotedToPumpswap>>,
    TError,
    { data: TokenPromotedToPumpswapRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof tokenPromotedToPumpswap>>,
  TError,
  { data: TokenPromotedToPumpswapRequest },
  TContext
> => {
  const mutationKey = ['tokenPromotedToPumpswap'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof tokenPromotedToPumpswap>>,
    { data: TokenPromotedToPumpswapRequest }
  > = (props) => {
    const { data } = props ?? {};

    return tokenPromotedToPumpswap(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type TokenPromotedToPumpswapMutationResult = NonNullable<Awaited<ReturnType<typeof tokenPromotedToPumpswap>>>;
export type TokenPromotedToPumpswapMutationBody = TokenPromotedToPumpswapRequest;
export type TokenPromotedToPumpswapMutationError = unknown;

export const useTokenPromotedToPumpswap = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof tokenPromotedToPumpswap>>,
    TError,
    { data: TokenPromotedToPumpswapRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof tokenPromotedToPumpswap>>,
  TError,
  { data: TokenPromotedToPumpswapRequest },
  TContext
> => {
  const mutationOptions = getTokenPromotedToPumpswapMutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
			Creates new Bot from BotDraft for userId and returns its ID.
			Upon successful Bot creation, the BotDraft used is deleted.
	
 */
export const createBot = (params: CreateBotParams, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<Result>({ url: `/bots`, method: 'POST', params }, options);
};

export const getCreateBotMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof createBot>>, TError, { params: CreateBotParams }, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<Awaited<ReturnType<typeof createBot>>, TError, { params: CreateBotParams }, TContext> => {
  const mutationKey = ['createBot'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof createBot>>, { params: CreateBotParams }> = (props) => {
    const { params } = props ?? {};

    return createBot(params, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateBotMutationResult = NonNullable<Awaited<ReturnType<typeof createBot>>>;

export type CreateBotMutationError = unknown;

export const useCreateBot = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof createBot>>, TError, { params: CreateBotParams }, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<Awaited<ReturnType<typeof createBot>>, TError, { params: CreateBotParams }, TContext> => {
  const mutationOptions = getCreateBotMutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
            Forces immediate sell of open bot market position.
        
 */
export const forceSellMarketPositions = (
  botId: string,
  botMarketPositionId: string,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    { url: `/bots/${botId}/market-positions/${botMarketPositionId}/force-sell`, method: 'POST' },
    options,
  );
};

export const getForceSellMarketPositionsMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof forceSellMarketPositions>>,
    TError,
    { botId: string; botMarketPositionId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof forceSellMarketPositions>>,
  TError,
  { botId: string; botMarketPositionId: string },
  TContext
> => {
  const mutationKey = ['forceSellMarketPositions'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof forceSellMarketPositions>>,
    { botId: string; botMarketPositionId: string }
  > = (props) => {
    const { botId, botMarketPositionId } = props ?? {};

    return forceSellMarketPositions(botId, botMarketPositionId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ForceSellMarketPositionsMutationResult = NonNullable<Awaited<ReturnType<typeof forceSellMarketPositions>>>;

export type ForceSellMarketPositionsMutationError = unknown;

export const useForceSellMarketPositions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof forceSellMarketPositions>>,
    TError,
    { botId: string; botMarketPositionId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof forceSellMarketPositions>>,
  TError,
  { botId: string; botMarketPositionId: string },
  TContext
> => {
  const mutationOptions = getForceSellMarketPositionsMutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
            Search for bot market positions with various filters.
            Returns paginated results of bot's market positions with their current values and changes.
        
 */
export const searchBotMarketPositions = (
  botId: string,
  searchBotMarketPositionRequest: SearchBotMarketPositionRequest,
  params?: SearchBotMarketPositionsParams,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceSearchBotMarketPositionResultUUID>(
    {
      url: `/bots/${botId}/market-positions/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: searchBotMarketPositionRequest,
      params,
    },
    options,
  );
};

export const getSearchBotMarketPositionsQueryKey = (
  botId: string,
  searchBotMarketPositionRequest: SearchBotMarketPositionRequest,
  params?: SearchBotMarketPositionsParams,
) => {
  return [
    `/bots/${botId}/market-positions/search`,
    ...(params ? [params] : []),
    searchBotMarketPositionRequest,
  ] as const;
};

export const getSearchBotMarketPositionsInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof searchBotMarketPositions>>, SearchBotMarketPositionsParams['lastId']>,
  TError = unknown,
>(
  botId: string,
  searchBotMarketPositionRequest: SearchBotMarketPositionRequest,
  params?: SearchBotMarketPositionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchBotMarketPositions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchBotMarketPositions>>,
        QueryKey,
        SearchBotMarketPositionsParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getSearchBotMarketPositionsQueryKey(botId, searchBotMarketPositionRequest, params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof searchBotMarketPositions>>,
    QueryKey,
    SearchBotMarketPositionsParams['lastId']
  > = ({ pageParam }) =>
    searchBotMarketPositions(
      botId,
      searchBotMarketPositionRequest,
      { ...params, lastId: pageParam || params?.['lastId'] },
      requestOptions,
    );

  return { queryKey, queryFn, enabled: !!botId, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof searchBotMarketPositions>>,
    TError,
    TData,
    Awaited<ReturnType<typeof searchBotMarketPositions>>,
    QueryKey,
    SearchBotMarketPositionsParams['lastId']
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchBotMarketPositionsInfiniteQueryResult = NonNullable<
  Awaited<ReturnType<typeof searchBotMarketPositions>>
>;
export type SearchBotMarketPositionsInfiniteQueryError = unknown;

export function useSearchBotMarketPositionsInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof searchBotMarketPositions>>, SearchBotMarketPositionsParams['lastId']>,
  TError = unknown,
>(
  botId: string,
  searchBotMarketPositionRequest: SearchBotMarketPositionRequest,
  params: undefined | SearchBotMarketPositionsParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchBotMarketPositions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchBotMarketPositions>>,
        QueryKey,
        SearchBotMarketPositionsParams['lastId']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchBotMarketPositions>>,
          TError,
          Awaited<ReturnType<typeof searchBotMarketPositions>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchBotMarketPositionsInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof searchBotMarketPositions>>, SearchBotMarketPositionsParams['lastId']>,
  TError = unknown,
>(
  botId: string,
  searchBotMarketPositionRequest: SearchBotMarketPositionRequest,
  params?: SearchBotMarketPositionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchBotMarketPositions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchBotMarketPositions>>,
        QueryKey,
        SearchBotMarketPositionsParams['lastId']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchBotMarketPositions>>,
          TError,
          Awaited<ReturnType<typeof searchBotMarketPositions>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchBotMarketPositionsInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof searchBotMarketPositions>>, SearchBotMarketPositionsParams['lastId']>,
  TError = unknown,
>(
  botId: string,
  searchBotMarketPositionRequest: SearchBotMarketPositionRequest,
  params?: SearchBotMarketPositionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchBotMarketPositions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchBotMarketPositions>>,
        QueryKey,
        SearchBotMarketPositionsParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchBotMarketPositionsInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof searchBotMarketPositions>>, SearchBotMarketPositionsParams['lastId']>,
  TError = unknown,
>(
  botId: string,
  searchBotMarketPositionRequest: SearchBotMarketPositionRequest,
  params?: SearchBotMarketPositionsParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof searchBotMarketPositions>>,
        TError,
        TData,
        Awaited<ReturnType<typeof searchBotMarketPositions>>,
        QueryKey,
        SearchBotMarketPositionsParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchBotMarketPositionsInfiniteQueryOptions(
    botId,
    searchBotMarketPositionRequest,
    params,
    options,
  );

  const query = useInfiniteQuery(queryOptions) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const resetBotBuyFrequency = (
  resetBotBuyFrequencyRequest: ResetBotBuyFrequencyRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/bots/reset-buy-frequency`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: resetBotBuyFrequencyRequest,
    },
    options,
  );
};

export const getResetBotBuyFrequencyMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof resetBotBuyFrequency>>,
    TError,
    { data: ResetBotBuyFrequencyRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof resetBotBuyFrequency>>,
  TError,
  { data: ResetBotBuyFrequencyRequest },
  TContext
> => {
  const mutationKey = ['resetBotBuyFrequency'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof resetBotBuyFrequency>>,
    { data: ResetBotBuyFrequencyRequest }
  > = (props) => {
    const { data } = props ?? {};

    return resetBotBuyFrequency(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type ResetBotBuyFrequencyMutationResult = NonNullable<Awaited<ReturnType<typeof resetBotBuyFrequency>>>;
export type ResetBotBuyFrequencyMutationBody = ResetBotBuyFrequencyRequest;
export type ResetBotBuyFrequencyMutationError = unknown;

export const useResetBotBuyFrequency = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof resetBotBuyFrequency>>,
    TError,
    { data: ResetBotBuyFrequencyRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof resetBotBuyFrequency>>,
  TError,
  { data: ResetBotBuyFrequencyRequest },
  TContext
> => {
  const mutationOptions = getResetBotBuyFrequencyMutationOptions(options);

  return useMutation(mutationOptions);
};

export const botsOverview = (
  botsOverviewRequest: BotsOverviewRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<BotsOverviewResult>(
    {
      url: `/bots/overview`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: botsOverviewRequest,
    },
    options,
  );
};

export const getBotsOverviewQueryKey = (botsOverviewRequest: BotsOverviewRequest) => {
  return [`/bots/overview`, botsOverviewRequest] as const;
};

export const getBotsOverviewQueryOptions = <TData = Awaited<ReturnType<typeof botsOverview>>, TError = unknown>(
  botsOverviewRequest: BotsOverviewRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsOverview>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getBotsOverviewQueryKey(botsOverviewRequest);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof botsOverview>>> = () =>
    botsOverview(botsOverviewRequest, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof botsOverview>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type BotsOverviewQueryResult = NonNullable<Awaited<ReturnType<typeof botsOverview>>>;
export type BotsOverviewQueryError = unknown;

export function useBotsOverview<TData = Awaited<ReturnType<typeof botsOverview>>, TError = unknown>(
  botsOverviewRequest: BotsOverviewRequest,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsOverview>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof botsOverview>>,
          TError,
          Awaited<ReturnType<typeof botsOverview>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useBotsOverview<TData = Awaited<ReturnType<typeof botsOverview>>, TError = unknown>(
  botsOverviewRequest: BotsOverviewRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsOverview>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof botsOverview>>,
          TError,
          Awaited<ReturnType<typeof botsOverview>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useBotsOverview<TData = Awaited<ReturnType<typeof botsOverview>>, TError = unknown>(
  botsOverviewRequest: BotsOverviewRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsOverview>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useBotsOverview<TData = Awaited<ReturnType<typeof botsOverview>>, TError = unknown>(
  botsOverviewRequest: BotsOverviewRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsOverview>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getBotsOverviewQueryOptions(botsOverviewRequest, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const botsLeaderboard1 = (
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboard1Params,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceBotsLeaderboardSearchResultBigDecimal>(
    {
      url: `/bots/leaderboard/search`,
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      data: botsLeaderboardSearchRequest,
      params,
    },
    options,
  );
};

export const getBotsLeaderboard1QueryKey = (
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboard1Params,
) => {
  return [`/bots/leaderboard/search`, ...(params ? [params] : []), botsLeaderboardSearchRequest] as const;
};

export const getBotsLeaderboard1QueryOptions = <TData = Awaited<ReturnType<typeof botsLeaderboard1>>, TError = unknown>(
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboard1Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsLeaderboard1>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getBotsLeaderboard1QueryKey(botsLeaderboardSearchRequest, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof botsLeaderboard1>>> = () =>
    botsLeaderboard1(botsLeaderboardSearchRequest, params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof botsLeaderboard1>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type BotsLeaderboard1QueryResult = NonNullable<Awaited<ReturnType<typeof botsLeaderboard1>>>;
export type BotsLeaderboard1QueryError = unknown;

export function useBotsLeaderboard1<TData = Awaited<ReturnType<typeof botsLeaderboard1>>, TError = unknown>(
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params: undefined | BotsLeaderboard1Params,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsLeaderboard1>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof botsLeaderboard1>>,
          TError,
          Awaited<ReturnType<typeof botsLeaderboard1>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useBotsLeaderboard1<TData = Awaited<ReturnType<typeof botsLeaderboard1>>, TError = unknown>(
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboard1Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsLeaderboard1>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof botsLeaderboard1>>,
          TError,
          Awaited<ReturnType<typeof botsLeaderboard1>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useBotsLeaderboard1<TData = Awaited<ReturnType<typeof botsLeaderboard1>>, TError = unknown>(
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboard1Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsLeaderboard1>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useBotsLeaderboard1<TData = Awaited<ReturnType<typeof botsLeaderboard1>>, TError = unknown>(
  botsLeaderboardSearchRequest: BotsLeaderboardSearchRequest,
  params?: BotsLeaderboard1Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof botsLeaderboard1>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getBotsLeaderboard1QueryOptions(botsLeaderboardSearchRequest, params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const compareBots = (
  compareBotsRequest: CompareBotsRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<CompareBotsResult[]>(
    { url: `/bots/compare`, method: 'POST', headers: { 'Content-Type': 'application/json' }, data: compareBotsRequest },
    options,
  );
};

export const getCompareBotsQueryKey = (compareBotsRequest: CompareBotsRequest) => {
  return [`/bots/compare`, compareBotsRequest] as const;
};

export const getCompareBotsQueryOptions = <TData = Awaited<ReturnType<typeof compareBots>>, TError = unknown>(
  compareBotsRequest: CompareBotsRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof compareBots>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getCompareBotsQueryKey(compareBotsRequest);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof compareBots>>> = () =>
    compareBots(compareBotsRequest, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof compareBots>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type CompareBotsQueryResult = NonNullable<Awaited<ReturnType<typeof compareBots>>>;
export type CompareBotsQueryError = unknown;

export function useCompareBots<TData = Awaited<ReturnType<typeof compareBots>>, TError = unknown>(
  compareBotsRequest: CompareBotsRequest,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof compareBots>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof compareBots>>,
          TError,
          Awaited<ReturnType<typeof compareBots>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useCompareBots<TData = Awaited<ReturnType<typeof compareBots>>, TError = unknown>(
  compareBotsRequest: CompareBotsRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof compareBots>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof compareBots>>,
          TError,
          Awaited<ReturnType<typeof compareBots>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useCompareBots<TData = Awaited<ReturnType<typeof compareBots>>, TError = unknown>(
  compareBotsRequest: CompareBotsRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof compareBots>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useCompareBots<TData = Awaited<ReturnType<typeof compareBots>>, TError = unknown>(
  compareBotsRequest: CompareBotsRequest,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof compareBots>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getCompareBotsQueryOptions(compareBotsRequest, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Creates new BotDraft for userId and returns its ID
 */
export const createBotDraft = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<CreateBotDraftResult>({ url: `/bot-drafts`, method: 'POST' }, options);
};

export const getCreateBotDraftMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof createBotDraft>>, TError, void, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<Awaited<ReturnType<typeof createBotDraft>>, TError, void, TContext> => {
  const mutationKey = ['createBotDraft'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof createBotDraft>>, void> = () => {
    return createBotDraft(requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateBotDraftMutationResult = NonNullable<Awaited<ReturnType<typeof createBotDraft>>>;

export type CreateBotDraftMutationError = unknown;

export const useCreateBotDraft = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof createBotDraft>>, TError, void, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<Awaited<ReturnType<typeof createBotDraft>>, TError, void, TContext> => {
  const mutationOptions = getCreateBotDraftMutationOptions(options);

  return useMutation(mutationOptions);
};

export const getUserWallet = (walletId: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetUserWalletResult>({ url: `/users/me/wallets/${walletId}`, method: 'GET' }, options);
};

export const getGetUserWalletQueryKey = (walletId: string) => {
  return [`/users/me/wallets/${walletId}`] as const;
};

export const getGetUserWalletQueryOptions = <TData = Awaited<ReturnType<typeof getUserWallet>>, TError = unknown>(
  walletId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserWalletQueryKey(walletId);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserWallet>>> = () =>
    getUserWallet(walletId, requestOptions);

  return { queryKey, queryFn, enabled: !!walletId, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserWallet>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetUserWalletQueryResult = NonNullable<Awaited<ReturnType<typeof getUserWallet>>>;
export type GetUserWalletQueryError = unknown;

export function useGetUserWallet<TData = Awaited<ReturnType<typeof getUserWallet>>, TError = unknown>(
  walletId: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserWallet>>,
          TError,
          Awaited<ReturnType<typeof getUserWallet>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserWallet<TData = Awaited<ReturnType<typeof getUserWallet>>, TError = unknown>(
  walletId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserWallet>>,
          TError,
          Awaited<ReturnType<typeof getUserWallet>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserWallet<TData = Awaited<ReturnType<typeof getUserWallet>>, TError = unknown>(
  walletId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetUserWallet<TData = Awaited<ReturnType<typeof getUserWallet>>, TError = unknown>(
  walletId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserWallet>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetUserWalletQueryOptions(walletId, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const deleteWallet = (walletId: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<void>({ url: `/users/me/wallets/${walletId}`, method: 'DELETE' }, options);
};

export const getDeleteWalletMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteWallet>>, TError, { walletId: string }, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<Awaited<ReturnType<typeof deleteWallet>>, TError, { walletId: string }, TContext> => {
  const mutationKey = ['deleteWallet'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteWallet>>, { walletId: string }> = (props) => {
    const { walletId } = props ?? {};

    return deleteWallet(walletId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteWalletMutationResult = NonNullable<Awaited<ReturnType<typeof deleteWallet>>>;

export type DeleteWalletMutationError = unknown;

export const useDeleteWallet = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteWallet>>, TError, { walletId: string }, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<Awaited<ReturnType<typeof deleteWallet>>, TError, { walletId: string }, TContext> => {
  const mutationOptions = getDeleteWalletMutationOptions(options);

  return useMutation(mutationOptions);
};

export const patchUserWallet = (
  walletId: string,
  patchUserWalletRequest: PatchUserWalletRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/users/me/wallets/${walletId}`,
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      data: patchUserWalletRequest,
    },
    options,
  );
};

export const getPatchUserWalletMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchUserWallet>>,
    TError,
    { walletId: string; data: PatchUserWalletRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchUserWallet>>,
  TError,
  { walletId: string; data: PatchUserWalletRequest },
  TContext
> => {
  const mutationKey = ['patchUserWallet'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchUserWallet>>,
    { walletId: string; data: PatchUserWalletRequest }
  > = (props) => {
    const { walletId, data } = props ?? {};

    return patchUserWallet(walletId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchUserWalletMutationResult = NonNullable<Awaited<ReturnType<typeof patchUserWallet>>>;
export type PatchUserWalletMutationBody = PatchUserWalletRequest;
export type PatchUserWalletMutationError = unknown;

export const usePatchUserWallet = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchUserWallet>>,
    TError,
    { walletId: string; data: PatchUserWalletRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchUserWallet>>,
  TError,
  { walletId: string; data: PatchUserWalletRequest },
  TContext
> => {
  const mutationOptions = getPatchUserWalletMutationOptions(options);

  return useMutation(mutationOptions);
};

export const setQuickBuyAmount = (
  params: SetQuickBuyAmountParams,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>({ url: `/users/me/settings/quick-buy`, method: 'PATCH', params }, options);
};

export const getSetQuickBuyAmountMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof setQuickBuyAmount>>,
    TError,
    { params: SetQuickBuyAmountParams },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof setQuickBuyAmount>>,
  TError,
  { params: SetQuickBuyAmountParams },
  TContext
> => {
  const mutationKey = ['setQuickBuyAmount'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof setQuickBuyAmount>>,
    { params: SetQuickBuyAmountParams }
  > = (props) => {
    const { params } = props ?? {};

    return setQuickBuyAmount(params, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SetQuickBuyAmountMutationResult = NonNullable<Awaited<ReturnType<typeof setQuickBuyAmount>>>;

export type SetQuickBuyAmountMutationError = unknown;

export const useSetQuickBuyAmount = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof setQuickBuyAmount>>,
    TError,
    { params: SetQuickBuyAmountParams },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof setQuickBuyAmount>>,
  TError,
  { params: SetQuickBuyAmountParams },
  TContext
> => {
  const mutationOptions = getSetQuickBuyAmountMutationOptions(options);

  return useMutation(mutationOptions);
};

export const getBotById = (botId: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetBotResult>({ url: `/bots/${botId}`, method: 'GET' }, options);
};

export const getGetBotByIdQueryKey = (botId: string) => {
  return [`/bots/${botId}`] as const;
};

export const getGetBotByIdQueryOptions = <TData = Awaited<ReturnType<typeof getBotById>>, TError = unknown>(
  botId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotById>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetBotByIdQueryKey(botId);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getBotById>>> = () => getBotById(botId, requestOptions);

  return { queryKey, queryFn, enabled: !!botId, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getBotById>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetBotByIdQueryResult = NonNullable<Awaited<ReturnType<typeof getBotById>>>;
export type GetBotByIdQueryError = unknown;

export function useGetBotById<TData = Awaited<ReturnType<typeof getBotById>>, TError = unknown>(
  botId: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotById>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getBotById>>,
          TError,
          Awaited<ReturnType<typeof getBotById>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetBotById<TData = Awaited<ReturnType<typeof getBotById>>, TError = unknown>(
  botId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotById>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getBotById>>,
          TError,
          Awaited<ReturnType<typeof getBotById>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetBotById<TData = Awaited<ReturnType<typeof getBotById>>, TError = unknown>(
  botId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotById>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetBotById<TData = Awaited<ReturnType<typeof getBotById>>, TError = unknown>(
  botId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotById>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetBotByIdQueryOptions(botId, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const deleteBot = (botId: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<void>({ url: `/bots/${botId}`, method: 'DELETE' }, options);
};

export const getDeleteBotMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteBot>>, TError, { botId: string }, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<Awaited<ReturnType<typeof deleteBot>>, TError, { botId: string }, TContext> => {
  const mutationKey = ['deleteBot'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteBot>>, { botId: string }> = (props) => {
    const { botId } = props ?? {};

    return deleteBot(botId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteBotMutationResult = NonNullable<Awaited<ReturnType<typeof deleteBot>>>;

export type DeleteBotMutationError = unknown;

export const useDeleteBot = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteBot>>, TError, { botId: string }, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<Awaited<ReturnType<typeof deleteBot>>, TError, { botId: string }, TContext> => {
  const mutationOptions = getDeleteBotMutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
			Updates an existing bot with the provided partial data.
			Follows standard PATCH logic.
	
 */
export const patchBot = (
  botId: string,
  patchBotSettingsRequest: PatchBotSettingsRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/bots/${botId}`,
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      data: patchBotSettingsRequest,
    },
    options,
  );
};

export const getPatchBotMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchBot>>,
    TError,
    { botId: string; data: PatchBotSettingsRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchBot>>,
  TError,
  { botId: string; data: PatchBotSettingsRequest },
  TContext
> => {
  const mutationKey = ['patchBot'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchBot>>,
    { botId: string; data: PatchBotSettingsRequest }
  > = (props) => {
    const { botId, data } = props ?? {};

    return patchBot(botId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchBotMutationResult = NonNullable<Awaited<ReturnType<typeof patchBot>>>;
export type PatchBotMutationBody = PatchBotSettingsRequest;
export type PatchBotMutationError = unknown;

export const usePatchBot = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchBot>>,
    TError,
    { botId: string; data: PatchBotSettingsRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchBot>>,
  TError,
  { botId: string; data: PatchBotSettingsRequest },
  TContext
> => {
  const mutationOptions = getPatchBotMutationOptions(options);

  return useMutation(mutationOptions);
};

export const updateBotState = (
  botId: string,
  params: UpdateBotStateParams,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>({ url: `/bots/${botId}/state`, method: 'PATCH', params }, options);
};

export const getUpdateBotStateMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateBotState>>,
    TError,
    { botId: string; params: UpdateBotStateParams },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateBotState>>,
  TError,
  { botId: string; params: UpdateBotStateParams },
  TContext
> => {
  const mutationKey = ['updateBotState'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateBotState>>,
    { botId: string; params: UpdateBotStateParams }
  > = (props) => {
    const { botId, params } = props ?? {};

    return updateBotState(botId, params, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateBotStateMutationResult = NonNullable<Awaited<ReturnType<typeof updateBotState>>>;

export type UpdateBotStateMutationError = unknown;

export const useUpdateBotState = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateBotState>>,
    TError,
    { botId: string; params: UpdateBotStateParams },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof updateBotState>>,
  TError,
  { botId: string; params: UpdateBotStateParams },
  TContext
> => {
  const mutationOptions = getUpdateBotStateMutationOptions(options);

  return useMutation(mutationOptions);
};

export const deleteBotDraft = (botDraftId: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<void>({ url: `/bot-drafts/${botDraftId}`, method: 'DELETE' }, options);
};

export const getDeleteBotDraftMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteBotDraft>>, TError, { botDraftId: string }, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<Awaited<ReturnType<typeof deleteBotDraft>>, TError, { botDraftId: string }, TContext> => {
  const mutationKey = ['deleteBotDraft'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteBotDraft>>, { botDraftId: string }> = (props) => {
    const { botDraftId } = props ?? {};

    return deleteBotDraft(botDraftId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteBotDraftMutationResult = NonNullable<Awaited<ReturnType<typeof deleteBotDraft>>>;

export type DeleteBotDraftMutationError = unknown;

export const useDeleteBotDraft = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteBotDraft>>, TError, { botDraftId: string }, TContext>;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<Awaited<ReturnType<typeof deleteBotDraft>>, TError, { botDraftId: string }, TContext> => {
  const mutationOptions = getDeleteBotDraftMutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * 
			Updates an existing bot draft with the provided partial data.
			Follows standard PATCH logic.
	
 */
export const patchBotDraft = (
  botDraftId: string,
  patchBotSettingsRequest: PatchBotSettingsRequest,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<void>(
    {
      url: `/bot-drafts/${botDraftId}`,
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      data: patchBotSettingsRequest,
    },
    options,
  );
};

export const getPatchBotDraftMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchBotDraft>>,
    TError,
    { botDraftId: string; data: PatchBotSettingsRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof patchBotDraft>>,
  TError,
  { botDraftId: string; data: PatchBotSettingsRequest },
  TContext
> => {
  const mutationKey = ['patchBotDraft'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof patchBotDraft>>,
    { botDraftId: string; data: PatchBotSettingsRequest }
  > = (props) => {
    const { botDraftId, data } = props ?? {};

    return patchBotDraft(botDraftId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PatchBotDraftMutationResult = NonNullable<Awaited<ReturnType<typeof patchBotDraft>>>;
export type PatchBotDraftMutationBody = PatchBotSettingsRequest;
export type PatchBotDraftMutationError = unknown;

export const usePatchBotDraft = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof patchBotDraft>>,
    TError,
    { botDraftId: string; data: PatchBotSettingsRequest },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<
  Awaited<ReturnType<typeof patchBotDraft>>,
  TError,
  { botDraftId: string; data: PatchBotSettingsRequest },
  TContext
> => {
  const mutationOptions = getPatchBotDraftMutationOptions(options);

  return useMutation(mutationOptions);
};

export const getAllUserWalletsV3 = (
  params: GetAllUserWalletsV3Params,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetAllUserWalletsResult[]>({ url: `/v3/users/me/wallets`, method: 'GET', params }, options);
};

export const getGetAllUserWalletsV3QueryKey = (params: GetAllUserWalletsV3Params) => {
  return [`/v3/users/me/wallets`, ...(params ? [params] : [])] as const;
};

export const getGetAllUserWalletsV3QueryOptions = <
  TData = Awaited<ReturnType<typeof getAllUserWalletsV3>>,
  TError = unknown,
>(
  params: GetAllUserWalletsV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllUserWalletsV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllUserWalletsV3QueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllUserWalletsV3>>> = () =>
    getAllUserWalletsV3(params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllUserWalletsV3>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetAllUserWalletsV3QueryResult = NonNullable<Awaited<ReturnType<typeof getAllUserWalletsV3>>>;
export type GetAllUserWalletsV3QueryError = unknown;

export function useGetAllUserWalletsV3<TData = Awaited<ReturnType<typeof getAllUserWalletsV3>>, TError = unknown>(
  params: GetAllUserWalletsV3Params,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllUserWalletsV3>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAllUserWalletsV3>>,
          TError,
          Awaited<ReturnType<typeof getAllUserWalletsV3>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllUserWalletsV3<TData = Awaited<ReturnType<typeof getAllUserWalletsV3>>, TError = unknown>(
  params: GetAllUserWalletsV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllUserWalletsV3>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAllUserWalletsV3>>,
          TError,
          Awaited<ReturnType<typeof getAllUserWalletsV3>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllUserWalletsV3<TData = Awaited<ReturnType<typeof getAllUserWalletsV3>>, TError = unknown>(
  params: GetAllUserWalletsV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllUserWalletsV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetAllUserWalletsV3<TData = Awaited<ReturnType<typeof getAllUserWalletsV3>>, TError = unknown>(
  params: GetAllUserWalletsV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllUserWalletsV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetAllUserWalletsV3QueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
		Get aggregated user market position for all tokens on given chains
	
 */
export const marketPositionOverviewV3 = (
  params: MarketPositionOverviewV3Params,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<UserMarketPositionOverviewResult>(
    { url: `/v3/users/me/market-positions/overview`, method: 'GET', params },
    options,
  );
};

export const getMarketPositionOverviewV3QueryKey = (params: MarketPositionOverviewV3Params) => {
  return [`/v3/users/me/market-positions/overview`, ...(params ? [params] : [])] as const;
};

export const getMarketPositionOverviewV3QueryOptions = <
  TData = Awaited<ReturnType<typeof marketPositionOverviewV3>>,
  TError = unknown,
>(
  params: MarketPositionOverviewV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof marketPositionOverviewV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getMarketPositionOverviewV3QueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof marketPositionOverviewV3>>> = () =>
    marketPositionOverviewV3(params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof marketPositionOverviewV3>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type MarketPositionOverviewV3QueryResult = NonNullable<Awaited<ReturnType<typeof marketPositionOverviewV3>>>;
export type MarketPositionOverviewV3QueryError = unknown;

export function useMarketPositionOverviewV3<
  TData = Awaited<ReturnType<typeof marketPositionOverviewV3>>,
  TError = unknown,
>(
  params: MarketPositionOverviewV3Params,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof marketPositionOverviewV3>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof marketPositionOverviewV3>>,
          TError,
          Awaited<ReturnType<typeof marketPositionOverviewV3>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useMarketPositionOverviewV3<
  TData = Awaited<ReturnType<typeof marketPositionOverviewV3>>,
  TError = unknown,
>(
  params: MarketPositionOverviewV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof marketPositionOverviewV3>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof marketPositionOverviewV3>>,
          TError,
          Awaited<ReturnType<typeof marketPositionOverviewV3>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useMarketPositionOverviewV3<
  TData = Awaited<ReturnType<typeof marketPositionOverviewV3>>,
  TError = unknown,
>(
  params: MarketPositionOverviewV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof marketPositionOverviewV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useMarketPositionOverviewV3<
  TData = Awaited<ReturnType<typeof marketPositionOverviewV3>>,
  TError = unknown,
>(
  params: MarketPositionOverviewV3Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof marketPositionOverviewV3>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getMarketPositionOverviewV3QueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
			Get EVM token balance.
		
 */
export const getTokenBalanceV2 = (
  tokenAddress: string,
  params: GetTokenBalanceV2Params,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetTokenBalanceResult>(
    { url: `/v2/users/me/tokens/${tokenAddress}/balance`, method: 'GET', params },
    options,
  );
};

export const getGetTokenBalanceV2QueryKey = (tokenAddress: string, params: GetTokenBalanceV2Params) => {
  return [`/v2/users/me/tokens/${tokenAddress}/balance`, ...(params ? [params] : [])] as const;
};

export const getGetTokenBalanceV2QueryOptions = <
  TData = Awaited<ReturnType<typeof getTokenBalanceV2>>,
  TError = unknown,
>(
  tokenAddress: string,
  params: GetTokenBalanceV2Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenBalanceV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetTokenBalanceV2QueryKey(tokenAddress, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getTokenBalanceV2>>> = () =>
    getTokenBalanceV2(tokenAddress, params, requestOptions);

  return { queryKey, queryFn, enabled: !!tokenAddress, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getTokenBalanceV2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetTokenBalanceV2QueryResult = NonNullable<Awaited<ReturnType<typeof getTokenBalanceV2>>>;
export type GetTokenBalanceV2QueryError = unknown;

export function useGetTokenBalanceV2<TData = Awaited<ReturnType<typeof getTokenBalanceV2>>, TError = unknown>(
  tokenAddress: string,
  params: GetTokenBalanceV2Params,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenBalanceV2>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getTokenBalanceV2>>,
          TError,
          Awaited<ReturnType<typeof getTokenBalanceV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTokenBalanceV2<TData = Awaited<ReturnType<typeof getTokenBalanceV2>>, TError = unknown>(
  tokenAddress: string,
  params: GetTokenBalanceV2Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenBalanceV2>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getTokenBalanceV2>>,
          TError,
          Awaited<ReturnType<typeof getTokenBalanceV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTokenBalanceV2<TData = Awaited<ReturnType<typeof getTokenBalanceV2>>, TError = unknown>(
  tokenAddress: string,
  params: GetTokenBalanceV2Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenBalanceV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetTokenBalanceV2<TData = Awaited<ReturnType<typeof getTokenBalanceV2>>, TError = unknown>(
  tokenAddress: string,
  params: GetTokenBalanceV2Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenBalanceV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetTokenBalanceV2QueryOptions(tokenAddress, params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
			Search specific token on chain
		
 */
export const getTokenDetailAsUserV2 = (
  chain: Chain,
  tokenAddress: string,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetTokenDetailResult>(
    { url: `/v2/users/me/tokens/${chain}/${tokenAddress}`, method: 'GET' },
    options,
  );
};

export const getGetTokenDetailAsUserV2QueryKey = (chain: Chain, tokenAddress: string) => {
  return [`/v2/users/me/tokens/${chain}/${tokenAddress}`] as const;
};

export const getGetTokenDetailAsUserV2QueryOptions = <
  TData = Awaited<ReturnType<typeof getTokenDetailAsUserV2>>,
  TError = unknown,
>(
  chain: Chain,
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenDetailAsUserV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetTokenDetailAsUserV2QueryKey(chain, tokenAddress);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getTokenDetailAsUserV2>>> = () =>
    getTokenDetailAsUserV2(chain, tokenAddress, requestOptions);

  return { queryKey, queryFn, enabled: !!(chain && tokenAddress), ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getTokenDetailAsUserV2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetTokenDetailAsUserV2QueryResult = NonNullable<Awaited<ReturnType<typeof getTokenDetailAsUserV2>>>;
export type GetTokenDetailAsUserV2QueryError = unknown;

export function useGetTokenDetailAsUserV2<TData = Awaited<ReturnType<typeof getTokenDetailAsUserV2>>, TError = unknown>(
  chain: Chain,
  tokenAddress: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenDetailAsUserV2>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getTokenDetailAsUserV2>>,
          TError,
          Awaited<ReturnType<typeof getTokenDetailAsUserV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTokenDetailAsUserV2<TData = Awaited<ReturnType<typeof getTokenDetailAsUserV2>>, TError = unknown>(
  chain: Chain,
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenDetailAsUserV2>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getTokenDetailAsUserV2>>,
          TError,
          Awaited<ReturnType<typeof getTokenDetailAsUserV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTokenDetailAsUserV2<TData = Awaited<ReturnType<typeof getTokenDetailAsUserV2>>, TError = unknown>(
  chain: Chain,
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenDetailAsUserV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetTokenDetailAsUserV2<TData = Awaited<ReturnType<typeof getTokenDetailAsUserV2>>, TError = unknown>(
  chain: Chain,
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenDetailAsUserV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetTokenDetailAsUserV2QueryOptions(chain, tokenAddress, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
			If `useSelectedChains == true`: Gets hot tokens in respect to User `selectedChains` settings.
			If `useSelectedChains == false`: Gets hot tokens in respect to all enabled chains.
		
 */
export const getHotTokensV2 = (params: GetHotTokensV2Params, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<InfiniteScrollSliceGetHotTokensResultBigDecimal>(
    { url: `/v2/users/me/tokens/hot`, method: 'GET', params },
    options,
  );
};

export const getGetHotTokensV2QueryKey = (params: GetHotTokensV2Params) => {
  return [`/v2/users/me/tokens/hot`, ...(params ? [params] : [])] as const;
};

export const getGetHotTokensV2InfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof getHotTokensV2>>, GetHotTokensV2Params['lastId']>,
  TError = unknown,
>(
  params: GetHotTokensV2Params,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof getHotTokensV2>>,
        TError,
        TData,
        Awaited<ReturnType<typeof getHotTokensV2>>,
        QueryKey,
        GetHotTokensV2Params['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetHotTokensV2QueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getHotTokensV2>>,
    QueryKey,
    GetHotTokensV2Params['lastId']
  > = ({ pageParam }) => getHotTokensV2({ ...params, lastId: pageParam || params?.['lastId'] }, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof getHotTokensV2>>,
    TError,
    TData,
    Awaited<ReturnType<typeof getHotTokensV2>>,
    QueryKey,
    GetHotTokensV2Params['lastId']
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetHotTokensV2InfiniteQueryResult = NonNullable<Awaited<ReturnType<typeof getHotTokensV2>>>;
export type GetHotTokensV2InfiniteQueryError = unknown;

export function useGetHotTokensV2Infinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getHotTokensV2>>, GetHotTokensV2Params['lastId']>,
  TError = unknown,
>(
  params: GetHotTokensV2Params,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof getHotTokensV2>>,
        TError,
        TData,
        Awaited<ReturnType<typeof getHotTokensV2>>,
        QueryKey,
        GetHotTokensV2Params['lastId']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getHotTokensV2>>,
          TError,
          Awaited<ReturnType<typeof getHotTokensV2>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetHotTokensV2Infinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getHotTokensV2>>, GetHotTokensV2Params['lastId']>,
  TError = unknown,
>(
  params: GetHotTokensV2Params,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof getHotTokensV2>>,
        TError,
        TData,
        Awaited<ReturnType<typeof getHotTokensV2>>,
        QueryKey,
        GetHotTokensV2Params['lastId']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getHotTokensV2>>,
          TError,
          Awaited<ReturnType<typeof getHotTokensV2>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetHotTokensV2Infinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getHotTokensV2>>, GetHotTokensV2Params['lastId']>,
  TError = unknown,
>(
  params: GetHotTokensV2Params,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof getHotTokensV2>>,
        TError,
        TData,
        Awaited<ReturnType<typeof getHotTokensV2>>,
        QueryKey,
        GetHotTokensV2Params['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetHotTokensV2Infinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getHotTokensV2>>, GetHotTokensV2Params['lastId']>,
  TError = unknown,
>(
  params: GetHotTokensV2Params,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof getHotTokensV2>>,
        TError,
        TData,
        Awaited<ReturnType<typeof getHotTokensV2>>,
        QueryKey,
        GetHotTokensV2Params['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetHotTokensV2InfiniteQueryOptions(params, options);

  const query = useInfiniteQuery(queryOptions) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
		Get aggregated user market position for all tokens on given chains
	
 * @deprecated
 */
export const marketPositionOverviewV2 = (
  params: MarketPositionOverviewV2Params,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<UserMarketPositionOverviewResult>(
    { url: `/v2/users/me/market-positions/overview`, method: 'GET', params },
    options,
  );
};

export const getMarketPositionOverviewV2QueryKey = (params: MarketPositionOverviewV2Params) => {
  return [`/v2/users/me/market-positions/overview`, ...(params ? [params] : [])] as const;
};

export const getMarketPositionOverviewV2QueryOptions = <
  TData = Awaited<ReturnType<typeof marketPositionOverviewV2>>,
  TError = unknown,
>(
  params: MarketPositionOverviewV2Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof marketPositionOverviewV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getMarketPositionOverviewV2QueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof marketPositionOverviewV2>>> = () =>
    marketPositionOverviewV2(params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof marketPositionOverviewV2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type MarketPositionOverviewV2QueryResult = NonNullable<Awaited<ReturnType<typeof marketPositionOverviewV2>>>;
export type MarketPositionOverviewV2QueryError = unknown;

export function useMarketPositionOverviewV2<
  TData = Awaited<ReturnType<typeof marketPositionOverviewV2>>,
  TError = unknown,
>(
  params: MarketPositionOverviewV2Params,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof marketPositionOverviewV2>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof marketPositionOverviewV2>>,
          TError,
          Awaited<ReturnType<typeof marketPositionOverviewV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useMarketPositionOverviewV2<
  TData = Awaited<ReturnType<typeof marketPositionOverviewV2>>,
  TError = unknown,
>(
  params: MarketPositionOverviewV2Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof marketPositionOverviewV2>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof marketPositionOverviewV2>>,
          TError,
          Awaited<ReturnType<typeof marketPositionOverviewV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useMarketPositionOverviewV2<
  TData = Awaited<ReturnType<typeof marketPositionOverviewV2>>,
  TError = unknown,
>(
  params: MarketPositionOverviewV2Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof marketPositionOverviewV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
/**
 * @deprecated
 */

export function useMarketPositionOverviewV2<
  TData = Awaited<ReturnType<typeof marketPositionOverviewV2>>,
  TError = unknown,
>(
  params: MarketPositionOverviewV2Params,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof marketPositionOverviewV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getMarketPositionOverviewV2QueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getUser = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetUserResult>({ url: `/users/me`, method: 'GET' }, options);
};

export const getGetUserQueryKey = () => {
  return [`/users/me`] as const;
};

export const getGetUserQueryOptions = <TData = Awaited<ReturnType<typeof getUser>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUser>>> = () => getUser(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUser>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetUserQueryResult = NonNullable<Awaited<ReturnType<typeof getUser>>>;
export type GetUserQueryError = unknown;

export function useGetUser<TData = Awaited<ReturnType<typeof getUser>>, TError = unknown>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<Awaited<ReturnType<typeof getUser>>, TError, Awaited<ReturnType<typeof getUser>>>,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUser<TData = Awaited<ReturnType<typeof getUser>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<Awaited<ReturnType<typeof getUser>>, TError, Awaited<ReturnType<typeof getUser>>>,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUser<TData = Awaited<ReturnType<typeof getUser>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetUser<TData = Awaited<ReturnType<typeof getUser>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUser>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetUserQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
		Get User wallet currency positions (how much native tokens they hold) aggregated by Chain.
		When walletId is null, all Wallets are considered.
	
 */
export const searchUserWalletCurrencyPositions = (
  params: SearchUserWalletCurrencyPositionsParams,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<SearchUserWalletPositionResult[]>(
    { url: `/users/me/wallets/currency-positions`, method: 'GET', params },
    options,
  );
};

export const getSearchUserWalletCurrencyPositionsQueryKey = (params: SearchUserWalletCurrencyPositionsParams) => {
  return [`/users/me/wallets/currency-positions`, ...(params ? [params] : [])] as const;
};

export const getSearchUserWalletCurrencyPositionsQueryOptions = <
  TData = Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>,
  TError = unknown,
>(
  params: SearchUserWalletCurrencyPositionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getSearchUserWalletCurrencyPositionsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>> = () =>
    searchUserWalletCurrencyPositions(params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchUserWalletCurrencyPositionsQueryResult = NonNullable<
  Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>
>;
export type SearchUserWalletCurrencyPositionsQueryError = unknown;

export function useSearchUserWalletCurrencyPositions<
  TData = Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>,
  TError = unknown,
>(
  params: SearchUserWalletCurrencyPositionsParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>,
          TError,
          Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserWalletCurrencyPositions<
  TData = Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>,
  TError = unknown,
>(
  params: SearchUserWalletCurrencyPositionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>,
          TError,
          Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserWalletCurrencyPositions<
  TData = Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>,
  TError = unknown,
>(
  params: SearchUserWalletCurrencyPositionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchUserWalletCurrencyPositions<
  TData = Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>,
  TError = unknown,
>(
  params: SearchUserWalletCurrencyPositionsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserWalletCurrencyPositions>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchUserWalletCurrencyPositionsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Retrieve user donut leaderboard information.
 * @summary Get User leaderboard info
 */
export const getUserLeaderboardInfo = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetUserLeaderboardInfoResult>({ url: `/users/me/user-leaderboards`, method: 'GET' }, options);
};

export const getGetUserLeaderboardInfoQueryKey = () => {
  return [`/users/me/user-leaderboards`] as const;
};

export const getGetUserLeaderboardInfoQueryOptions = <
  TData = Awaited<ReturnType<typeof getUserLeaderboardInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLeaderboardInfo>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserLeaderboardInfoQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserLeaderboardInfo>>> = () =>
    getUserLeaderboardInfo(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserLeaderboardInfo>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetUserLeaderboardInfoQueryResult = NonNullable<Awaited<ReturnType<typeof getUserLeaderboardInfo>>>;
export type GetUserLeaderboardInfoQueryError = unknown;

export function useGetUserLeaderboardInfo<
  TData = Awaited<ReturnType<typeof getUserLeaderboardInfo>>,
  TError = unknown,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLeaderboardInfo>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserLeaderboardInfo>>,
        TError,
        Awaited<ReturnType<typeof getUserLeaderboardInfo>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserLeaderboardInfo<
  TData = Awaited<ReturnType<typeof getUserLeaderboardInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLeaderboardInfo>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserLeaderboardInfo>>,
        TError,
        Awaited<ReturnType<typeof getUserLeaderboardInfo>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserLeaderboardInfo<
  TData = Awaited<ReturnType<typeof getUserLeaderboardInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLeaderboardInfo>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
/**
 * @summary Get User leaderboard info
 */

export function useGetUserLeaderboardInfo<
  TData = Awaited<ReturnType<typeof getUserLeaderboardInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLeaderboardInfo>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetUserLeaderboardInfoQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Retrieve all users on the leaderboard with their rank, multiplier and donut count
 * @summary Get all user leaderboards
 */
export const searchUserLeaderboards = (
  params?: SearchUserLeaderboardsParams,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceSearchUserLeaderboardResultInteger>(
    { url: `/users/me/user-leaderboards/search`, method: 'GET', params },
    options,
  );
};

export const getSearchUserLeaderboardsQueryKey = (params?: SearchUserLeaderboardsParams) => {
  return [`/users/me/user-leaderboards/search`, ...(params ? [params] : [])] as const;
};

export const getSearchUserLeaderboardsQueryOptions = <
  TData = Awaited<ReturnType<typeof searchUserLeaderboards>>,
  TError = unknown,
>(
  params?: SearchUserLeaderboardsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserLeaderboards>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getSearchUserLeaderboardsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof searchUserLeaderboards>>> = () =>
    searchUserLeaderboards(params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof searchUserLeaderboards>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchUserLeaderboardsQueryResult = NonNullable<Awaited<ReturnType<typeof searchUserLeaderboards>>>;
export type SearchUserLeaderboardsQueryError = unknown;

export function useSearchUserLeaderboards<TData = Awaited<ReturnType<typeof searchUserLeaderboards>>, TError = unknown>(
  params: undefined | SearchUserLeaderboardsParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserLeaderboards>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserLeaderboards>>,
          TError,
          Awaited<ReturnType<typeof searchUserLeaderboards>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserLeaderboards<TData = Awaited<ReturnType<typeof searchUserLeaderboards>>, TError = unknown>(
  params?: SearchUserLeaderboardsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserLeaderboards>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchUserLeaderboards>>,
          TError,
          Awaited<ReturnType<typeof searchUserLeaderboards>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchUserLeaderboards<TData = Awaited<ReturnType<typeof searchUserLeaderboards>>, TError = unknown>(
  params?: SearchUserLeaderboardsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserLeaderboards>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
/**
 * @summary Get all user leaderboards
 */

export function useSearchUserLeaderboards<TData = Awaited<ReturnType<typeof searchUserLeaderboards>>, TError = unknown>(
  params?: SearchUserLeaderboardsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchUserLeaderboards>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchUserLeaderboardsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Retrieve information about leaderboard rank and multiplier association.
 * @summary Get User leaderboard rank and multiplier info
 */
export const getUserLeaderboardGeneralInfo = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetUserLeaderboardGeneralInfoResult[]>(
    { url: `/users/me/user-leaderboards/info`, method: 'GET' },
    options,
  );
};

export const getGetUserLeaderboardGeneralInfoQueryKey = () => {
  return [`/users/me/user-leaderboards/info`] as const;
};

export const getGetUserLeaderboardGeneralInfoQueryOptions = <
  TData = Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserLeaderboardGeneralInfoQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>> = () =>
    getUserLeaderboardGeneralInfo(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetUserLeaderboardGeneralInfoQueryResult = NonNullable<
  Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>
>;
export type GetUserLeaderboardGeneralInfoQueryError = unknown;

export function useGetUserLeaderboardGeneralInfo<
  TData = Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>,
  TError = unknown,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>,
        TError,
        Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserLeaderboardGeneralInfo<
  TData = Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>,
        TError,
        Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserLeaderboardGeneralInfo<
  TData = Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
/**
 * @summary Get User leaderboard rank and multiplier info
 */

export function useGetUserLeaderboardGeneralInfo<
  TData = Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLeaderboardGeneralInfo>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetUserLeaderboardGeneralInfoQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
		Gets lifetime volume of all token trades (buy / sell) made on platform in USD.
	
 */
export const getUserLifetimeTradeVolume = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetLifetimeTradeVolumeResult>(
    { url: `/users/me/transactions/lifetime-trade-volume`, method: 'GET' },
    options,
  );
};

export const getGetUserLifetimeTradeVolumeQueryKey = () => {
  return [`/users/me/transactions/lifetime-trade-volume`] as const;
};

export const getGetUserLifetimeTradeVolumeQueryOptions = <
  TData = Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserLifetimeTradeVolumeQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>> = () =>
    getUserLifetimeTradeVolume(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetUserLifetimeTradeVolumeQueryResult = NonNullable<Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>>;
export type GetUserLifetimeTradeVolumeQueryError = unknown;

export function useGetUserLifetimeTradeVolume<
  TData = Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>,
  TError = unknown,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>,
        TError,
        Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserLifetimeTradeVolume<
  TData = Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>,
        TError,
        Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserLifetimeTradeVolume<
  TData = Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetUserLifetimeTradeVolume<
  TData = Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserLifetimeTradeVolume>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetUserLifetimeTradeVolumeQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getUserStreak = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetUserStreakResult>({ url: `/users/me/statistics/streak`, method: 'GET' }, options);
};

export const getGetUserStreakQueryKey = () => {
  return [`/users/me/statistics/streak`] as const;
};

export const getGetUserStreakQueryOptions = <
  TData = Awaited<ReturnType<typeof getUserStreak>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserStreak>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserStreakQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserStreak>>> = () => getUserStreak(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserStreak>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetUserStreakQueryResult = NonNullable<Awaited<ReturnType<typeof getUserStreak>>>;
export type GetUserStreakQueryError = unknown;

export function useGetUserStreak<TData = Awaited<ReturnType<typeof getUserStreak>>, TError = unknown>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserStreak>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserStreak>>,
        TError,
        Awaited<ReturnType<typeof getUserStreak>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserStreak<TData = Awaited<ReturnType<typeof getUserStreak>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserStreak>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserStreak>>,
        TError,
        Awaited<ReturnType<typeof getUserStreak>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserStreak<TData = Awaited<ReturnType<typeof getUserStreak>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserStreak>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetUserStreak<TData = Awaited<ReturnType<typeof getUserStreak>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserStreak>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetUserStreakQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getUserStreakGeneralInfo = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetUserStreakGeneralInfoResult[]>(
    { url: `/users/me/statistics/streak/info`, method: 'GET' },
    options,
  );
};

export const getGetUserStreakGeneralInfoQueryKey = () => {
  return [`/users/me/statistics/streak/info`] as const;
};

export const getGetUserStreakGeneralInfoQueryOptions = <
  TData = Awaited<ReturnType<typeof getUserStreakGeneralInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserStreakGeneralInfo>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserStreakGeneralInfoQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserStreakGeneralInfo>>> = () =>
    getUserStreakGeneralInfo(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserStreakGeneralInfo>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetUserStreakGeneralInfoQueryResult = NonNullable<Awaited<ReturnType<typeof getUserStreakGeneralInfo>>>;
export type GetUserStreakGeneralInfoQueryError = unknown;

export function useGetUserStreakGeneralInfo<
  TData = Awaited<ReturnType<typeof getUserStreakGeneralInfo>>,
  TError = unknown,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserStreakGeneralInfo>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserStreakGeneralInfo>>,
        TError,
        Awaited<ReturnType<typeof getUserStreakGeneralInfo>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserStreakGeneralInfo<
  TData = Awaited<ReturnType<typeof getUserStreakGeneralInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserStreakGeneralInfo>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserStreakGeneralInfo>>,
        TError,
        Awaited<ReturnType<typeof getUserStreakGeneralInfo>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserStreakGeneralInfo<
  TData = Awaited<ReturnType<typeof getUserStreakGeneralInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserStreakGeneralInfo>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetUserStreakGeneralInfo<
  TData = Awaited<ReturnType<typeof getUserStreakGeneralInfo>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserStreakGeneralInfo>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetUserStreakGeneralInfoQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getPortfolioOverview = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetPortfolioValuesResult>({ url: `/users/me/portfolio/overview`, method: 'GET' }, options);
};

export const getGetPortfolioOverviewQueryKey = () => {
  return [`/users/me/portfolio/overview`] as const;
};

export const getGetPortfolioOverviewQueryOptions = <
  TData = Awaited<ReturnType<typeof getPortfolioOverview>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPortfolioOverview>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetPortfolioOverviewQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getPortfolioOverview>>> = () =>
    getPortfolioOverview(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getPortfolioOverview>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetPortfolioOverviewQueryResult = NonNullable<Awaited<ReturnType<typeof getPortfolioOverview>>>;
export type GetPortfolioOverviewQueryError = unknown;

export function useGetPortfolioOverview<
  TData = Awaited<ReturnType<typeof getPortfolioOverview>>,
  TError = unknown,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPortfolioOverview>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getPortfolioOverview>>,
        TError,
        Awaited<ReturnType<typeof getPortfolioOverview>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetPortfolioOverview<
  TData = Awaited<ReturnType<typeof getPortfolioOverview>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPortfolioOverview>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getPortfolioOverview>>,
        TError,
        Awaited<ReturnType<typeof getPortfolioOverview>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetPortfolioOverview<
  TData = Awaited<ReturnType<typeof getPortfolioOverview>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPortfolioOverview>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetPortfolioOverview<
  TData = Awaited<ReturnType<typeof getPortfolioOverview>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getPortfolioOverview>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetPortfolioOverviewQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getLimitOrderDetail = (limitOrderId: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetLimitOrderDetailResult>(
    { url: `/users/me/limit-orders/${limitOrderId}`, method: 'GET' },
    options,
  );
};

export const getGetLimitOrderDetailQueryKey = (limitOrderId: string) => {
  return [`/users/me/limit-orders/${limitOrderId}`] as const;
};

export const getGetLimitOrderDetailQueryOptions = <
  TData = Awaited<ReturnType<typeof getLimitOrderDetail>>,
  TError = unknown,
>(
  limitOrderId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getLimitOrderDetail>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetLimitOrderDetailQueryKey(limitOrderId);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getLimitOrderDetail>>> = () =>
    getLimitOrderDetail(limitOrderId, requestOptions);

  return { queryKey, queryFn, enabled: !!limitOrderId, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getLimitOrderDetail>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetLimitOrderDetailQueryResult = NonNullable<Awaited<ReturnType<typeof getLimitOrderDetail>>>;
export type GetLimitOrderDetailQueryError = unknown;

export function useGetLimitOrderDetail<TData = Awaited<ReturnType<typeof getLimitOrderDetail>>, TError = unknown>(
  limitOrderId: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getLimitOrderDetail>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getLimitOrderDetail>>,
          TError,
          Awaited<ReturnType<typeof getLimitOrderDetail>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetLimitOrderDetail<TData = Awaited<ReturnType<typeof getLimitOrderDetail>>, TError = unknown>(
  limitOrderId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getLimitOrderDetail>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getLimitOrderDetail>>,
          TError,
          Awaited<ReturnType<typeof getLimitOrderDetail>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetLimitOrderDetail<TData = Awaited<ReturnType<typeof getLimitOrderDetail>>, TError = unknown>(
  limitOrderId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getLimitOrderDetail>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetLimitOrderDetail<TData = Awaited<ReturnType<typeof getLimitOrderDetail>>, TError = unknown>(
  limitOrderId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getLimitOrderDetail>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetLimitOrderDetailQueryOptions(limitOrderId, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const deleteLimitOrder = (limitOrderId: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<void>({ url: `/users/me/limit-orders/${limitOrderId}`, method: 'DELETE' }, options);
};

export const getDeleteLimitOrderMutationOptions = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteLimitOrder>>,
    TError,
    { limitOrderId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<Awaited<ReturnType<typeof deleteLimitOrder>>, TError, { limitOrderId: string }, TContext> => {
  const mutationKey = ['deleteLimitOrder'];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteLimitOrder>>, { limitOrderId: string }> = (
    props,
  ) => {
    const { limitOrderId } = props ?? {};

    return deleteLimitOrder(limitOrderId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteLimitOrderMutationResult = NonNullable<Awaited<ReturnType<typeof deleteLimitOrder>>>;

export type DeleteLimitOrderMutationError = unknown;

export const useDeleteLimitOrder = <TError = unknown, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteLimitOrder>>,
    TError,
    { limitOrderId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationResult<Awaited<ReturnType<typeof deleteLimitOrder>>, TError, { limitOrderId: string }, TContext> => {
  const mutationOptions = getDeleteLimitOrderMutationOptions(options);

  return useMutation(mutationOptions);
};

/**
 * Get all user fatty league seasons.
 */
export const getUserFattyLeagueSeasons = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetUserFattyLeagueSeasonsResult>(
    { url: `/users/me/fatty-league-seasons`, method: 'GET' },
    options,
  );
};

export const getGetUserFattyLeagueSeasonsQueryKey = () => {
  return [`/users/me/fatty-league-seasons`] as const;
};

export const getGetUserFattyLeagueSeasonsQueryOptions = <
  TData = Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserFattyLeagueSeasonsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>> = () =>
    getUserFattyLeagueSeasons(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetUserFattyLeagueSeasonsQueryResult = NonNullable<Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>>;
export type GetUserFattyLeagueSeasonsQueryError = unknown;

export function useGetUserFattyLeagueSeasons<
  TData = Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>,
  TError = unknown,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>,
        TError,
        Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserFattyLeagueSeasons<
  TData = Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>,
        TError,
        Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserFattyLeagueSeasons<
  TData = Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetUserFattyLeagueSeasons<
  TData = Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyLeagueSeasons>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetUserFattyLeagueSeasonsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Get user statistic fatty cards.
 */
export const getUserFattyCardsOverview = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetUserFattyCardsOverviewResult>({ url: `/users/me/fatty-cards`, method: 'GET' }, options);
};

export const getGetUserFattyCardsOverviewQueryKey = () => {
  return [`/users/me/fatty-cards`] as const;
};

export const getGetUserFattyCardsOverviewQueryOptions = <
  TData = Awaited<ReturnType<typeof getUserFattyCardsOverview>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyCardsOverview>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserFattyCardsOverviewQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserFattyCardsOverview>>> = () =>
    getUserFattyCardsOverview(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserFattyCardsOverview>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetUserFattyCardsOverviewQueryResult = NonNullable<Awaited<ReturnType<typeof getUserFattyCardsOverview>>>;
export type GetUserFattyCardsOverviewQueryError = unknown;

export function useGetUserFattyCardsOverview<
  TData = Awaited<ReturnType<typeof getUserFattyCardsOverview>>,
  TError = unknown,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyCardsOverview>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserFattyCardsOverview>>,
        TError,
        Awaited<ReturnType<typeof getUserFattyCardsOverview>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserFattyCardsOverview<
  TData = Awaited<ReturnType<typeof getUserFattyCardsOverview>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyCardsOverview>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getUserFattyCardsOverview>>,
        TError,
        Awaited<ReturnType<typeof getUserFattyCardsOverview>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserFattyCardsOverview<
  TData = Awaited<ReturnType<typeof getUserFattyCardsOverview>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyCardsOverview>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetUserFattyCardsOverview<
  TData = Awaited<ReturnType<typeof getUserFattyCardsOverview>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyCardsOverview>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetUserFattyCardsOverviewQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Get user fatty card.
 */
export const getUserFattyCard = (userFattyCardId: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetUserFattyCardResult>(
    { url: `/users/me/fatty-cards/${userFattyCardId}`, method: 'GET' },
    options,
  );
};

export const getGetUserFattyCardQueryKey = (userFattyCardId: string) => {
  return [`/users/me/fatty-cards/${userFattyCardId}`] as const;
};

export const getGetUserFattyCardQueryOptions = <TData = Awaited<ReturnType<typeof getUserFattyCard>>, TError = unknown>(
  userFattyCardId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyCard>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserFattyCardQueryKey(userFattyCardId);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserFattyCard>>> = () =>
    getUserFattyCard(userFattyCardId, requestOptions);

  return { queryKey, queryFn, enabled: !!userFattyCardId, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserFattyCard>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetUserFattyCardQueryResult = NonNullable<Awaited<ReturnType<typeof getUserFattyCard>>>;
export type GetUserFattyCardQueryError = unknown;

export function useGetUserFattyCard<TData = Awaited<ReturnType<typeof getUserFattyCard>>, TError = unknown>(
  userFattyCardId: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyCard>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserFattyCard>>,
          TError,
          Awaited<ReturnType<typeof getUserFattyCard>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserFattyCard<TData = Awaited<ReturnType<typeof getUserFattyCard>>, TError = unknown>(
  userFattyCardId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyCard>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserFattyCard>>,
          TError,
          Awaited<ReturnType<typeof getUserFattyCard>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetUserFattyCard<TData = Awaited<ReturnType<typeof getUserFattyCard>>, TError = unknown>(
  userFattyCardId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyCard>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetUserFattyCard<TData = Awaited<ReturnType<typeof getUserFattyCard>>, TError = unknown>(
  userFattyCardId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getUserFattyCard>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetUserFattyCardQueryOptions(userFattyCardId, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getMyBots = (params: GetMyBotsParams, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetMyBotsResult[]>({ url: `/users/me/bots/all`, method: 'GET', params }, options);
};

export const getGetMyBotsQueryKey = (params: GetMyBotsParams) => {
  return [`/users/me/bots/all`, ...(params ? [params] : [])] as const;
};

export const getGetMyBotsQueryOptions = <TData = Awaited<ReturnType<typeof getMyBots>>, TError = unknown>(
  params: GetMyBotsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMyBots>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetMyBotsQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getMyBots>>> = () => getMyBots(params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getMyBots>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetMyBotsQueryResult = NonNullable<Awaited<ReturnType<typeof getMyBots>>>;
export type GetMyBotsQueryError = unknown;

export function useGetMyBots<TData = Awaited<ReturnType<typeof getMyBots>>, TError = unknown>(
  params: GetMyBotsParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMyBots>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<Awaited<ReturnType<typeof getMyBots>>, TError, Awaited<ReturnType<typeof getMyBots>>>,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetMyBots<TData = Awaited<ReturnType<typeof getMyBots>>, TError = unknown>(
  params: GetMyBotsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMyBots>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getMyBots>>,
          TError,
          Awaited<ReturnType<typeof getMyBots>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetMyBots<TData = Awaited<ReturnType<typeof getMyBots>>, TError = unknown>(
  params: GetMyBotsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMyBots>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetMyBots<TData = Awaited<ReturnType<typeof getMyBots>>, TError = unknown>(
  params: GetMyBotsParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getMyBots>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetMyBotsQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
		Gets minimal claiming limit for each ENABLED chain in native amount of that chain
	
 */
export const getClaimingLimits = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetClaimingLimitsResult[]>({ url: `/referrals/min-claim-limits`, method: 'GET' }, options);
};

export const getGetClaimingLimitsQueryKey = () => {
  return [`/referrals/min-claim-limits`] as const;
};

export const getGetClaimingLimitsQueryOptions = <
  TData = Awaited<ReturnType<typeof getClaimingLimits>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getClaimingLimits>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetClaimingLimitsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getClaimingLimits>>> = () => getClaimingLimits(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getClaimingLimits>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetClaimingLimitsQueryResult = NonNullable<Awaited<ReturnType<typeof getClaimingLimits>>>;
export type GetClaimingLimitsQueryError = unknown;

export function useGetClaimingLimits<TData = Awaited<ReturnType<typeof getClaimingLimits>>, TError = unknown>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getClaimingLimits>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getClaimingLimits>>,
        TError,
        Awaited<ReturnType<typeof getClaimingLimits>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetClaimingLimits<
  TData = Awaited<ReturnType<typeof getClaimingLimits>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getClaimingLimits>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getClaimingLimits>>,
        TError,
        Awaited<ReturnType<typeof getClaimingLimits>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetClaimingLimits<
  TData = Awaited<ReturnType<typeof getClaimingLimits>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getClaimingLimits>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetClaimingLimits<
  TData = Awaited<ReturnType<typeof getClaimingLimits>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getClaimingLimits>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetClaimingLimitsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const checkReferralCode = (
  params: CheckReferralCodeParams,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<CheckReferralCodeResult>({ url: `/referrals/is-unique`, method: 'GET', params }, options);
};

export const getCheckReferralCodeQueryKey = (params: CheckReferralCodeParams) => {
  return [`/referrals/is-unique`, ...(params ? [params] : [])] as const;
};

export const getCheckReferralCodeQueryOptions = <
  TData = Awaited<ReturnType<typeof checkReferralCode>>,
  TError = unknown,
>(
  params: CheckReferralCodeParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof checkReferralCode>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getCheckReferralCodeQueryKey(params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof checkReferralCode>>> = () =>
    checkReferralCode(params, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof checkReferralCode>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type CheckReferralCodeQueryResult = NonNullable<Awaited<ReturnType<typeof checkReferralCode>>>;
export type CheckReferralCodeQueryError = unknown;

export function useCheckReferralCode<TData = Awaited<ReturnType<typeof checkReferralCode>>, TError = unknown>(
  params: CheckReferralCodeParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof checkReferralCode>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof checkReferralCode>>,
          TError,
          Awaited<ReturnType<typeof checkReferralCode>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useCheckReferralCode<TData = Awaited<ReturnType<typeof checkReferralCode>>, TError = unknown>(
  params: CheckReferralCodeParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof checkReferralCode>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof checkReferralCode>>,
          TError,
          Awaited<ReturnType<typeof checkReferralCode>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useCheckReferralCode<TData = Awaited<ReturnType<typeof checkReferralCode>>, TError = unknown>(
  params: CheckReferralCodeParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof checkReferralCode>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useCheckReferralCode<TData = Awaited<ReturnType<typeof checkReferralCode>>, TError = unknown>(
  params: CheckReferralCodeParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof checkReferralCode>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getCheckReferralCodeQueryOptions(params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
			Search token address across all enabled chains.
		
 */
export const searchToken = (tokenAddress: string, options?: SecondParameter<typeof customInstance>) => {
  return customInstance<SearchTokenResult[]>({ url: `/public/v2/tokens/${tokenAddress}`, method: 'GET' }, options);
};

export const getSearchTokenQueryKey = (tokenAddress: string) => {
  return [`/public/v2/tokens/${tokenAddress}`] as const;
};

export const getSearchTokenQueryOptions = <TData = Awaited<ReturnType<typeof searchToken>>, TError = unknown>(
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchToken>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getSearchTokenQueryKey(tokenAddress);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof searchToken>>> = () =>
    searchToken(tokenAddress, requestOptions);

  return { queryKey, queryFn, enabled: !!tokenAddress, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof searchToken>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type SearchTokenQueryResult = NonNullable<Awaited<ReturnType<typeof searchToken>>>;
export type SearchTokenQueryError = unknown;

export function useSearchToken<TData = Awaited<ReturnType<typeof searchToken>>, TError = unknown>(
  tokenAddress: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchToken>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchToken>>,
          TError,
          Awaited<ReturnType<typeof searchToken>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchToken<TData = Awaited<ReturnType<typeof searchToken>>, TError = unknown>(
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchToken>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof searchToken>>,
          TError,
          Awaited<ReturnType<typeof searchToken>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useSearchToken<TData = Awaited<ReturnType<typeof searchToken>>, TError = unknown>(
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchToken>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useSearchToken<TData = Awaited<ReturnType<typeof searchToken>>, TError = unknown>(
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof searchToken>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getSearchTokenQueryOptions(tokenAddress, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getIsTokenContractVerified = (
  tokenAddress: string,
  params: GetIsTokenContractVerifiedParams,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetIsTokenContractVerifiedResult>(
    { url: `/public/v2/tokens/${tokenAddress}/is-verified`, method: 'GET', params },
    options,
  );
};

export const getGetIsTokenContractVerifiedQueryKey = (
  tokenAddress: string,
  params: GetIsTokenContractVerifiedParams,
) => {
  return [`/public/v2/tokens/${tokenAddress}/is-verified`, ...(params ? [params] : [])] as const;
};

export const getGetIsTokenContractVerifiedQueryOptions = <
  TData = Awaited<ReturnType<typeof getIsTokenContractVerified>>,
  TError = unknown,
>(
  tokenAddress: string,
  params: GetIsTokenContractVerifiedParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getIsTokenContractVerified>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetIsTokenContractVerifiedQueryKey(tokenAddress, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getIsTokenContractVerified>>> = () =>
    getIsTokenContractVerified(tokenAddress, params, requestOptions);

  return { queryKey, queryFn, enabled: !!tokenAddress, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getIsTokenContractVerified>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetIsTokenContractVerifiedQueryResult = NonNullable<Awaited<ReturnType<typeof getIsTokenContractVerified>>>;
export type GetIsTokenContractVerifiedQueryError = unknown;

export function useGetIsTokenContractVerified<
  TData = Awaited<ReturnType<typeof getIsTokenContractVerified>>,
  TError = unknown,
>(
  tokenAddress: string,
  params: GetIsTokenContractVerifiedParams,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getIsTokenContractVerified>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getIsTokenContractVerified>>,
          TError,
          Awaited<ReturnType<typeof getIsTokenContractVerified>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetIsTokenContractVerified<
  TData = Awaited<ReturnType<typeof getIsTokenContractVerified>>,
  TError = unknown,
>(
  tokenAddress: string,
  params: GetIsTokenContractVerifiedParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getIsTokenContractVerified>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getIsTokenContractVerified>>,
          TError,
          Awaited<ReturnType<typeof getIsTokenContractVerified>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetIsTokenContractVerified<
  TData = Awaited<ReturnType<typeof getIsTokenContractVerified>>,
  TError = unknown,
>(
  tokenAddress: string,
  params: GetIsTokenContractVerifiedParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getIsTokenContractVerified>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetIsTokenContractVerified<
  TData = Awaited<ReturnType<typeof getIsTokenContractVerified>>,
  TError = unknown,
>(
  tokenAddress: string,
  params: GetIsTokenContractVerifiedParams,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getIsTokenContractVerified>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetIsTokenContractVerifiedQueryOptions(tokenAddress, params, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
		Search token on chain. Transactions and market positions are always empty.
	
 */
export const getTokenDetailAsAnonymousUserV2 = (
  chain: Chain,
  tokenAddress: string,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetTokenDetailResult>(
    { url: `/public/v2/tokens/${chain}/${tokenAddress}`, method: 'GET' },
    options,
  );
};

export const getGetTokenDetailAsAnonymousUserV2QueryKey = (chain: Chain, tokenAddress: string) => {
  return [`/public/v2/tokens/${chain}/${tokenAddress}`] as const;
};

export const getGetTokenDetailAsAnonymousUserV2QueryOptions = <
  TData = Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>,
  TError = unknown,
>(
  chain: Chain,
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetTokenDetailAsAnonymousUserV2QueryKey(chain, tokenAddress);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>> = () =>
    getTokenDetailAsAnonymousUserV2(chain, tokenAddress, requestOptions);

  return { queryKey, queryFn, enabled: !!(chain && tokenAddress), ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetTokenDetailAsAnonymousUserV2QueryResult = NonNullable<
  Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>
>;
export type GetTokenDetailAsAnonymousUserV2QueryError = unknown;

export function useGetTokenDetailAsAnonymousUserV2<
  TData = Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>,
  TError = unknown,
>(
  chain: Chain,
  tokenAddress: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>,
          TError,
          Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTokenDetailAsAnonymousUserV2<
  TData = Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>,
  TError = unknown,
>(
  chain: Chain,
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>,
          TError,
          Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetTokenDetailAsAnonymousUserV2<
  TData = Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>,
  TError = unknown,
>(
  chain: Chain,
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetTokenDetailAsAnonymousUserV2<
  TData = Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>,
  TError = unknown,
>(
  chain: Chain,
  tokenAddress: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getTokenDetailAsAnonymousUserV2>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetTokenDetailAsAnonymousUserV2QueryOptions(chain, tokenAddress, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * 
			Get all tokens that have 0% Fatbot fee.
		
 */
export const getAllZeroFeeTokens = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetAllZeroFeeTokensResult[]>({ url: `/public/v2/tokens/zero-fee`, method: 'GET' }, options);
};

export const getGetAllZeroFeeTokensQueryKey = () => {
  return [`/public/v2/tokens/zero-fee`] as const;
};

export const getGetAllZeroFeeTokensQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllZeroFeeTokens>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllZeroFeeTokens>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllZeroFeeTokensQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllZeroFeeTokens>>> = () =>
    getAllZeroFeeTokens(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllZeroFeeTokens>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetAllZeroFeeTokensQueryResult = NonNullable<Awaited<ReturnType<typeof getAllZeroFeeTokens>>>;
export type GetAllZeroFeeTokensQueryError = unknown;

export function useGetAllZeroFeeTokens<
  TData = Awaited<ReturnType<typeof getAllZeroFeeTokens>>,
  TError = unknown,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllZeroFeeTokens>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getAllZeroFeeTokens>>,
        TError,
        Awaited<ReturnType<typeof getAllZeroFeeTokens>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllZeroFeeTokens<
  TData = Awaited<ReturnType<typeof getAllZeroFeeTokens>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllZeroFeeTokens>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getAllZeroFeeTokens>>,
        TError,
        Awaited<ReturnType<typeof getAllZeroFeeTokens>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllZeroFeeTokens<
  TData = Awaited<ReturnType<typeof getAllZeroFeeTokens>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllZeroFeeTokens>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetAllZeroFeeTokens<
  TData = Awaited<ReturnType<typeof getAllZeroFeeTokens>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllZeroFeeTokens>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetAllZeroFeeTokensQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getHotTokensV2Public = (
  params?: GetHotTokensV2PublicParams,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<InfiniteScrollSliceGetHotTokensResultBigDecimal>(
    { url: `/public/v2/tokens/hot`, method: 'GET', params },
    options,
  );
};

export const getGetHotTokensV2PublicQueryKey = (params?: GetHotTokensV2PublicParams) => {
  return [`/public/v2/tokens/hot`, ...(params ? [params] : [])] as const;
};

export const getGetHotTokensV2PublicInfiniteQueryOptions = <
  TData = InfiniteData<Awaited<ReturnType<typeof getHotTokensV2Public>>, GetHotTokensV2PublicParams['lastId']>,
  TError = unknown,
>(
  params?: GetHotTokensV2PublicParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof getHotTokensV2Public>>,
        TError,
        TData,
        Awaited<ReturnType<typeof getHotTokensV2Public>>,
        QueryKey,
        GetHotTokensV2PublicParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetHotTokensV2PublicQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getHotTokensV2Public>>,
    QueryKey,
    GetHotTokensV2PublicParams['lastId']
  > = ({ pageParam }) => getHotTokensV2Public({ ...params, lastId: pageParam || params?.['lastId'] }, requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseInfiniteQueryOptions<
    Awaited<ReturnType<typeof getHotTokensV2Public>>,
    TError,
    TData,
    Awaited<ReturnType<typeof getHotTokensV2Public>>,
    QueryKey,
    GetHotTokensV2PublicParams['lastId']
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetHotTokensV2PublicInfiniteQueryResult = NonNullable<Awaited<ReturnType<typeof getHotTokensV2Public>>>;
export type GetHotTokensV2PublicInfiniteQueryError = unknown;

export function useGetHotTokensV2PublicInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getHotTokensV2Public>>, GetHotTokensV2PublicParams['lastId']>,
  TError = unknown,
>(
  params: undefined | GetHotTokensV2PublicParams,
  options: {
    query: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof getHotTokensV2Public>>,
        TError,
        TData,
        Awaited<ReturnType<typeof getHotTokensV2Public>>,
        QueryKey,
        GetHotTokensV2PublicParams['lastId']
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getHotTokensV2Public>>,
          TError,
          Awaited<ReturnType<typeof getHotTokensV2Public>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetHotTokensV2PublicInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getHotTokensV2Public>>, GetHotTokensV2PublicParams['lastId']>,
  TError = unknown,
>(
  params?: GetHotTokensV2PublicParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof getHotTokensV2Public>>,
        TError,
        TData,
        Awaited<ReturnType<typeof getHotTokensV2Public>>,
        QueryKey,
        GetHotTokensV2PublicParams['lastId']
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getHotTokensV2Public>>,
          TError,
          Awaited<ReturnType<typeof getHotTokensV2Public>>,
          QueryKey
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetHotTokensV2PublicInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getHotTokensV2Public>>, GetHotTokensV2PublicParams['lastId']>,
  TError = unknown,
>(
  params?: GetHotTokensV2PublicParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof getHotTokensV2Public>>,
        TError,
        TData,
        Awaited<ReturnType<typeof getHotTokensV2Public>>,
        QueryKey,
        GetHotTokensV2PublicParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetHotTokensV2PublicInfinite<
  TData = InfiniteData<Awaited<ReturnType<typeof getHotTokensV2Public>>, GetHotTokensV2PublicParams['lastId']>,
  TError = unknown,
>(
  params?: GetHotTokensV2PublicParams,
  options?: {
    query?: Partial<
      UseInfiniteQueryOptions<
        Awaited<ReturnType<typeof getHotTokensV2Public>>,
        TError,
        TData,
        Awaited<ReturnType<typeof getHotTokensV2Public>>,
        QueryKey,
        GetHotTokensV2PublicParams['lastId']
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseInfiniteQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetHotTokensV2PublicInfiniteQueryOptions(params, options);

  const query = useInfiniteQuery(queryOptions) as UseInfiniteQueryResult<TData, TError> & {
    queryKey: DataTag<QueryKey, TData>;
  };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getAllChains = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetEnabledChainsResult>({ url: `/public/chains`, method: 'GET' }, options);
};

export const getGetAllChainsQueryKey = () => {
  return [`/public/chains`] as const;
};

export const getGetAllChainsQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllChains>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllChains>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllChainsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllChains>>> = () => getAllChains(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllChains>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetAllChainsQueryResult = NonNullable<Awaited<ReturnType<typeof getAllChains>>>;
export type GetAllChainsQueryError = unknown;

export function useGetAllChains<TData = Awaited<ReturnType<typeof getAllChains>>, TError = unknown>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllChains>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getAllChains>>,
        TError,
        Awaited<ReturnType<typeof getAllChains>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllChains<TData = Awaited<ReturnType<typeof getAllChains>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllChains>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getAllChains>>,
        TError,
        Awaited<ReturnType<typeof getAllChains>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllChains<TData = Awaited<ReturnType<typeof getAllChains>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllChains>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetAllChains<TData = Awaited<ReturnType<typeof getAllChains>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllChains>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetAllChainsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getBotMarketPosition = (
  botId: string,
  botMarketPositionId: string,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<GetBotMarketPositionResult>(
    { url: `/bots/${botId}/market-positions/${botMarketPositionId}`, method: 'GET' },
    options,
  );
};

export const getGetBotMarketPositionQueryKey = (botId: string, botMarketPositionId: string) => {
  return [`/bots/${botId}/market-positions/${botMarketPositionId}`] as const;
};

export const getGetBotMarketPositionQueryOptions = <
  TData = Awaited<ReturnType<typeof getBotMarketPosition>>,
  TError = unknown,
>(
  botId: string,
  botMarketPositionId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotMarketPosition>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetBotMarketPositionQueryKey(botId, botMarketPositionId);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getBotMarketPosition>>> = () =>
    getBotMarketPosition(botId, botMarketPositionId, requestOptions);

  return { queryKey, queryFn, enabled: !!(botId && botMarketPositionId), ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getBotMarketPosition>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetBotMarketPositionQueryResult = NonNullable<Awaited<ReturnType<typeof getBotMarketPosition>>>;
export type GetBotMarketPositionQueryError = unknown;

export function useGetBotMarketPosition<TData = Awaited<ReturnType<typeof getBotMarketPosition>>, TError = unknown>(
  botId: string,
  botMarketPositionId: string,
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotMarketPosition>>, TError, TData>> &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getBotMarketPosition>>,
          TError,
          Awaited<ReturnType<typeof getBotMarketPosition>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetBotMarketPosition<TData = Awaited<ReturnType<typeof getBotMarketPosition>>, TError = unknown>(
  botId: string,
  botMarketPositionId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotMarketPosition>>, TError, TData>> &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getBotMarketPosition>>,
          TError,
          Awaited<ReturnType<typeof getBotMarketPosition>>
        >,
        'initialData'
      >;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetBotMarketPosition<TData = Awaited<ReturnType<typeof getBotMarketPosition>>, TError = unknown>(
  botId: string,
  botMarketPositionId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotMarketPosition>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetBotMarketPosition<TData = Awaited<ReturnType<typeof getBotMarketPosition>>, TError = unknown>(
  botId: string,
  botMarketPositionId: string,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotMarketPosition>>, TError, TData>>;
    request?: SecondParameter<typeof customInstance>;
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetBotMarketPositionQueryOptions(botId, botMarketPositionId, options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getBotSettingsStatistics = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetBotSettingsStatisticsResult>({ url: `/bots/setting-statistics`, method: 'GET' }, options);
};

export const getGetBotSettingsStatisticsQueryKey = () => {
  return [`/bots/setting-statistics`] as const;
};

export const getGetBotSettingsStatisticsQueryOptions = <
  TData = Awaited<ReturnType<typeof getBotSettingsStatistics>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotSettingsStatistics>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetBotSettingsStatisticsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getBotSettingsStatistics>>> = () =>
    getBotSettingsStatistics(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getBotSettingsStatistics>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetBotSettingsStatisticsQueryResult = NonNullable<Awaited<ReturnType<typeof getBotSettingsStatistics>>>;
export type GetBotSettingsStatisticsQueryError = unknown;

export function useGetBotSettingsStatistics<
  TData = Awaited<ReturnType<typeof getBotSettingsStatistics>>,
  TError = unknown,
>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotSettingsStatistics>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getBotSettingsStatistics>>,
        TError,
        Awaited<ReturnType<typeof getBotSettingsStatistics>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetBotSettingsStatistics<
  TData = Awaited<ReturnType<typeof getBotSettingsStatistics>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotSettingsStatistics>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getBotSettingsStatistics>>,
        TError,
        Awaited<ReturnType<typeof getBotSettingsStatistics>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetBotSettingsStatistics<
  TData = Awaited<ReturnType<typeof getBotSettingsStatistics>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotSettingsStatistics>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetBotSettingsStatistics<
  TData = Awaited<ReturnType<typeof getBotSettingsStatistics>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getBotSettingsStatistics>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetBotSettingsStatisticsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getAllBots = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetAllBotsResult[]>({ url: `/bots/all`, method: 'GET' }, options);
};

export const getGetAllBotsQueryKey = () => {
  return [`/bots/all`] as const;
};

export const getGetAllBotsQueryOptions = <TData = Awaited<ReturnType<typeof getAllBots>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllBots>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllBotsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllBots>>> = () => getAllBots(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllBots>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetAllBotsQueryResult = NonNullable<Awaited<ReturnType<typeof getAllBots>>>;
export type GetAllBotsQueryError = unknown;

export function useGetAllBots<TData = Awaited<ReturnType<typeof getAllBots>>, TError = unknown>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllBots>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<Awaited<ReturnType<typeof getAllBots>>, TError, Awaited<ReturnType<typeof getAllBots>>>,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllBots<TData = Awaited<ReturnType<typeof getAllBots>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllBots>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getAllBots>>,
        TError,
        Awaited<ReturnType<typeof getAllBots>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllBots<TData = Awaited<ReturnType<typeof getAllBots>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllBots>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetAllBots<TData = Awaited<ReturnType<typeof getAllBots>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllBots>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetAllBotsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

export const getAllBotDrafts = (options?: SecondParameter<typeof customInstance>) => {
  return customInstance<GetAllBotDraftsResult[]>({ url: `/bot-drafts/all`, method: 'GET' }, options);
};

export const getGetAllBotDraftsQueryKey = () => {
  return [`/bot-drafts/all`] as const;
};

export const getGetAllBotDraftsQueryOptions = <
  TData = Awaited<ReturnType<typeof getAllBotDrafts>>,
  TError = unknown,
>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllBotDrafts>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllBotDraftsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllBotDrafts>>> = () => getAllBotDrafts(requestOptions);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAllBotDrafts>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData> };
};

export type GetAllBotDraftsQueryResult = NonNullable<Awaited<ReturnType<typeof getAllBotDrafts>>>;
export type GetAllBotDraftsQueryError = unknown;

export function useGetAllBotDrafts<TData = Awaited<ReturnType<typeof getAllBotDrafts>>, TError = unknown>(options: {
  query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllBotDrafts>>, TError, TData>> &
    Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getAllBotDrafts>>,
        TError,
        Awaited<ReturnType<typeof getAllBotDrafts>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllBotDrafts<TData = Awaited<ReturnType<typeof getAllBotDrafts>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllBotDrafts>>, TError, TData>> &
    Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getAllBotDrafts>>,
        TError,
        Awaited<ReturnType<typeof getAllBotDrafts>>
      >,
      'initialData'
    >;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };
export function useGetAllBotDrafts<TData = Awaited<ReturnType<typeof getAllBotDrafts>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllBotDrafts>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

export function useGetAllBotDrafts<TData = Awaited<ReturnType<typeof getAllBotDrafts>>, TError = unknown>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllBotDrafts>>, TError, TData>>;
  request?: SecondParameter<typeof customInstance>;
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {
  const queryOptions = getGetAllBotDraftsQueryOptions(options);

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
