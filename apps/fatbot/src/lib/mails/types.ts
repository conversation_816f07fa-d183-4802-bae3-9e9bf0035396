export interface Options {
  click_tracking?: boolean;
  open_tracking?: boolean;
}

export interface Attachments {
  type: string;
  name: string;
  content: string;
}

export interface Recipient {
  email: string;
  name?: string;
  cc?: string;
  bcc?: string;
}

export interface TransactionEmailRequest {
  message: {
    template_id: number;
    subject: string;
    from_name: string;
    from_email: string;
    reply_to?: string;
    to: Recipient[];
    global_merge_vars?: Record<string, string>[];
    attachments?: Attachments[];
    options: Options;
  };
}
