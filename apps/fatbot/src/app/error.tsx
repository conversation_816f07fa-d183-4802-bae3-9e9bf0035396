'use client';

import * as Sentry from '@sentry/nextjs';
import { useEffect } from 'react';

import { isLocalDev } from '@/lib/is-local-dev';
import { logger } from '@/lib/logger';
import { MaintenanceContent } from '@/module/maintenance/maintenance-content';

export default function Error({ error }: { error: Error & { digest?: string } }) {
  useEffect(() => {
    if (isLocalDev()) {
      logger.error(`App error: ${error}`);
    }
    Sentry.captureException(error);
  }, [error]);

  return <MaintenanceContent />;
}
