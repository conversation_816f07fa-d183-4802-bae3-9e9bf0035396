import type { ReactNode } from 'react';

import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { DepositToWalletsHeader } from '@/module/deposit-to-wallet/deposit-to-wallet-header';

export default async function ManualTradingLayout({
  children,
}: {
  children: ReactNode;
}) {
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  return (
    <>
      <DepositToWalletsHeader leagueSystemEnabled={leagueSystemEnabled} />
      {children}
    </>
  );
}
