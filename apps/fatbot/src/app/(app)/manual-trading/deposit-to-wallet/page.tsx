import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { getAllUserWalletsV3 } from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { DepositToWalletContent } from '@/module/deposit-to-wallet/deposit-to-wallet-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata.manual-trading');

  return {
    title: t('deposit-to-wallet'),
  };
}

export default async function DepositToWallet() {
  const queryClient = getQueryClient();

  const wallets = await getAllUserWalletsV3({ useSelectedChains: true });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <DepositToWalletContent wallets={wallets} />
    </HydrationBoundary>
  );
}
