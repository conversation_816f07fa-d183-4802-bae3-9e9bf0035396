import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { isTokenDexResult } from '@/api/typeguards/is-token-dexresult';
import {
  type Chain,
  getAllChains,
  getGetAllUserWalletsV3QueryOptions,
  getGetIsTokenContractVerifiedQueryOptions,
  getGetTokenDetailAsAnonymousUserV2QueryOptions,
  getGetTokenDetailAsUserV2QueryOptions,
  getGetUserQueryOptions,
  selectChains,
} from '@/lib/api';
import { getTokens } from '@/lib/firebase/get-tokens';
import { getQueryClient } from '@/lib/query-client';
import { getAdvancedChartsConfig } from '@/module/token-detail/get-advanced-chart-config';
import { PublicTokenDetail } from '@/module/token-detail/public-token-detail';
import { TokenChartProvider } from '@/module/token-detail/token-chart-provider';
import { TokenDetail } from '@/module/token-detail/token-detail';
import { TokenNotTradeable } from '@/module/token-detail/token-not-tradeable';

export async function generateMetadata() {
  const t = await getTranslations('metadata.manual-trading');

  return {
    title: t('token-detail'),
  };
}

interface Props {
  params: Promise<{
    chain: Chain;
    address: string;
  }>;
}

export default async function TokenPage(props: Props) {
  const params = await props.params;
  const queryClient = getQueryClient();

  const tokens = await getTokens();
  const isAdvancedChartsFeatureEnabled = await getAdvancedChartsConfig();

  if (!tokens) {
    const [isTokenContractVerifiedResult] = await Promise.all([
      queryClient.fetchQuery(
        getGetIsTokenContractVerifiedQueryOptions(params.address, {
          chain: params.chain,
        }),
      ),
      queryClient.prefetchQuery(getGetTokenDetailAsAnonymousUserV2QueryOptions(params.chain, params.address)),
    ]);

    const isTokenContractVerified = isTokenContractVerifiedResult.isVerified;

    return (
      <HydrationBoundary state={dehydrate(queryClient)}>
        <PublicTokenDetail
          chain={params.chain}
          isAdvancedChartsFeatureEnabled={isAdvancedChartsFeatureEnabled}
          isTokenContractVerified={isTokenContractVerified}
          tokenAddress={params.address}
        />
      </HydrationBoundary>
    );
  }

  const data = await queryClient.fetchQuery(getGetTokenDetailAsUserV2QueryOptions(params.chain, params.address));

  await queryClient.prefetchQuery(getGetAllUserWalletsV3QueryOptions({ useSelectedChains: false }));

  if (!isTokenDexResult(data.tokenInfo)) {
    return <TokenNotTradeable />;
  }

  const [chains, user, isTokenContractVerifiedResult] = await Promise.all([
    getAllChains(),
    queryClient.fetchQuery(getGetUserQueryOptions()),
    queryClient.fetchQuery(
      getGetIsTokenContractVerifiedQueryOptions(params.address, {
        chain: params.chain,
      }),
    ),
  ]);

  const isTokenContractVerified = isTokenContractVerifiedResult.isVerified;

  await selectChains({
    selectedChains: [...user.selectedChains, params.chain],
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <TokenChartProvider chain={params.chain} enabledChains={chains.enabledChains} tokenAddress={params.address}>
        <TokenDetail
          chain={params.chain}
          isAdvancedChartsFeatureEnabled={isAdvancedChartsFeatureEnabled}
          isTokenContractVerified={isTokenContractVerified}
          tokenAddress={params.address}
        />
      </TokenChartProvider>
    </HydrationBoundary>
  );
}
