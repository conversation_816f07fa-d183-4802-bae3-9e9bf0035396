import type { ReactNode } from 'react';

import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import type { Chain } from '@/lib/api';
import { getTokens } from '@/lib/firebase/get-tokens';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { PublicTokenDetailHeader } from '@/module/token-detail/public-token-detail-header';
import { TokenDetailHeader } from '@/module/token-detail/token-detail-header';

interface Props {
  params: Promise<{
    chain: Chain;
    address: string;
  }>;
  children: ReactNode;
}

export default async function TokenDetailLayout({ params, children }: Props) {
  const tokens = await getTokens();
  const { chain, address } = await params;
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  return (
    <>
      {tokens ? (
        <TokenDetailHeader
          address={address}
          chain={chain}
          leagueSystemEnabled={leagueSystemEnabled}
        />
      ) : (
        <PublicTokenDetailHeader
          address={address}
          chain={chain}
          leagueSystemEnabled={leagueSystemEnabled}
        />
      )}
      {children}
    </>
  );
}
