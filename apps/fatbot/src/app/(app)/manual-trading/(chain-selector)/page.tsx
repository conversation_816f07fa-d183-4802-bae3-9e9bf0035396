import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { DEFAULT_PAGINATION_SIZE_SMALL } from '@/api/constants';
import { Card } from '@/components/ui/card';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { ROUTES } from '@/constants/routes';
import {
  getGetAllChainsQueryOptions,
  getGetAllUserWalletsV3QueryOptions,
  getGetHotTokensV2InfiniteQueryOptions,
  getGetHotTokensV2PublicInfiniteQueryOptions,
  getGetPortfolioOverviewQueryOptions,
  getMarketPositionOverviewV3QueryOptions,
  getSearchUserMarketPositionV3QueryOptions,
  getSearchUserTransactionOfAllTokenV3QueryOptions,
  getSearchUserWalletCurrencyPositionsQueryOptions,
} from '@/lib/api';
import { getTokens } from '@/lib/firebase/get-tokens';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { getQueryClient } from '@/lib/query-client';
import { ManualTradingHeader } from '@/module/manual-trading/components/manual-trading-header';
import { PublicHotTokens } from '@/module/manual-trading/hot-tokens/public-hot-tokens';
import { lastTransactionsRequestParams } from '@/module/manual-trading/last-transactions/last-transactions-request-params';
import { ManualTradingContent } from '@/module/manual-trading/manual-trading-content';
import { PublicMyAssets } from '@/module/manual-trading/my-assets/public-my-assets';
import { PublicPortfolioOverviewCard } from '@/module/manual-trading/portfolio-overview/public-portfolio-overview-card';
import { PublicSearchToken } from '@/module/manual-trading/search-token/public-search-token';
import { OnboardingHeader } from '@/module/onboarding/onboarding-header';

export async function generateMetadata() {
  const t = await getTranslations('metadata.manual-trading');

  return {
    title: t('root'),
  };
}

export default async function ManualTrading() {
  const tokens = await getTokens();
  const queryClient = getQueryClient();
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  if (!tokens) {
    await queryClient.prefetchInfiniteQuery(
      getGetHotTokensV2PublicInfiniteQueryOptions({
        size: DEFAULT_PAGINATION_SIZE_SMALL,
      })
    );

    return (
      <HydrationBoundary state={dehydrate(queryClient)}>
        <OnboardingHeader leagueSystemEnabled={leagueSystemEnabled} />
        <div className="grid w-full grid-cols-12 space-y-5 sm:gap-2 sm:space-y-0">
          <div className="col-span-12 grid grid-cols-1 gap-5 sm:col-span-12 sm:h-fit sm:grid-cols-2 sm:gap-2 xl:col-span-4 xl:grid-cols-1">
            <PublicPortfolioOverviewCard />
            <PublicSearchToken />
          </div>

          <div className="col-span-12 grid grid-cols-1 gap-5 sm:col-span-12 sm:gap-2 xl:col-span-8">
            <Card className="bg-transparent p-0 sm:bg-surface-area sm:p-3">
              <PublicMyAssets />
            </Card>
          </div>

          <div className="hidden xl:col-span-4 xl:grid" />

          <div className="col-span-12 grid grid-cols-1 gap-5 sm:gap-2 xl:col-span-8">
            <Card className="bg-transparent p-0 sm:bg-surface-area sm:p-3">
              <PublicHotTokens link={ROUTES.HOT_TOKENS.ROOT} />
            </Card>
          </div>
        </div>
      </HydrationBoundary>
    );
  }

  const lastTransactionsOptions =
    getSearchUserTransactionOfAllTokenV3QueryOptions(
      lastTransactionsRequestParams({ useSelectedChains: true }),
      { size: DEFAULT_PAGINATION_SIZE_SMALL }
    );

  const hotTokensOptions = getGetHotTokensV2InfiniteQueryOptions({
    size: DEFAULT_PAGINATION_SIZE_SMALL,
    useSelectedChains: true,
  });

  const marketPositionOption = getSearchUserMarketPositionV3QueryOptions(
    { useSelectedChains: true },
    { size: DEFAULT_PAGINATION_SIZE_SMALL }
  );

  const userWalletsOptions = getGetAllUserWalletsV3QueryOptions({
    useSelectedChains: true,
  });

  const portfolioOverviewOptions = getGetPortfolioOverviewQueryOptions();

  const searchUserWalletOptions =
    getSearchUserWalletCurrencyPositionsQueryOptions({
      useSelectedChains: false,
    });

  await Promise.all([
    queryClient.prefetchInfiniteQuery(hotTokensOptions),
    queryClient.prefetchQuery(marketPositionOption),
    queryClient.prefetchQuery(lastTransactionsOptions),
    queryClient.prefetchQuery(userWalletsOptions),
    queryClient.prefetchQuery(searchUserWalletOptions),
    queryClient.prefetchQuery(portfolioOverviewOptions),
    queryClient.prefetchQuery(getGetAllChainsQueryOptions()),
    queryClient.prefetchQuery(
      getMarketPositionOverviewV3QueryOptions({ useSelectedChains: true })
    ),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ManualTradingHeader leagueSystemEnabled={leagueSystemEnabled} />
      <ManualTradingContent />
    </HydrationBoundary>
  );
}
