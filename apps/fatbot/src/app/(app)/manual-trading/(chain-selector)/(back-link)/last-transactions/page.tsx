import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { DEFAULT_PAGINATION_SIZE } from '@/api/constants';
import {
  getGetAllUserWalletsV3QueryOptions,
  getSearchUserTransactionOfAllTokenV3InfiniteQueryOptions,
} from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { lastTransactionsRequestParams } from '@/module/manual-trading/last-transactions/last-transactions-request-params';
import { LastTransactionsContent } from '@/module/manual-trading/last-transactions-detail/last-transactions-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata.manual-trading');

  return {
    title: t('transactions'),
  };
}

interface PageSearchParams {
  searchString: string;
  walletId: string;
}

export default async function LastTransactions(props: {
  searchParams: Promise<PageSearchParams>;
}) {
  const searchParams = await props.searchParams;
  const { searchString, walletId } = searchParams;

  const queryClient = getQueryClient();

  const wallets = await queryClient.fetchQuery(
    getGetAllUserWalletsV3QueryOptions({ useSelectedChains: true })
  );

  const options = getSearchUserTransactionOfAllTokenV3InfiniteQueryOptions(
    lastTransactionsRequestParams({
      walletId,
      searchString,
      useSelectedChains: true,
    }),
    { size: DEFAULT_PAGINATION_SIZE }
  );
  await queryClient.prefetchInfiniteQuery({
    ...options,
    initialPageParam: undefined,
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <LastTransactionsContent wallets={wallets} />
    </HydrationBoundary>
  );
}
