import type { ReactNode } from 'react';

import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getTokens } from '@/lib/firebase/get-tokens';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { ManualTradingInnerHeader } from '@/module/manual-trading/components/maual-trading-inner-header';
import { OnboardingHeader } from '@/module/onboarding/onboarding-header';

interface Props {
  children: ReactNode;
}

export default async function ManualTradingLayout({ children }: Props) {
  const tokens = await getTokens();
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  return (
    <>
      {tokens ? (
        <ManualTradingInnerHeader leagueSystemEnabled={leagueSystemEnabled} />
      ) : (
        <OnboardingHeader leagueSystemEnabled={leagueSystemEnabled} />
      )}
      {children}
    </>
  );
}
