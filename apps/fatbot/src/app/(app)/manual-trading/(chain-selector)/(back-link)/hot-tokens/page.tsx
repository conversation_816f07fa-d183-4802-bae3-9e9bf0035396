import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { DEFAULT_PAGINATION_SIZE } from '@/api/constants';
import { getGetHotTokensV2InfiniteQueryOptions, getGetHotTokensV2PublicInfiniteQueryOptions } from '@/lib/api';
import { getTokens } from '@/lib/firebase/get-tokens';
import { getQueryClient } from '@/lib/query-client';
import { HotTokensDetailContent } from '@/module/manual-trading/hot-tokens-detail/hot-tokens-detail-content';
import { PublicHotTokensDetailContent } from '@/module/manual-trading/hot-tokens-detail/public-hot-tokens-detail-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata.manual-trading');

  return {
    title: t('transactions'),
  };
}

export default async function HotTokensPage() {
  const tokens = await getTokens();
  const queryClient = getQueryClient();

  const params = {
    size: DEFAULT_PAGINATION_SIZE,
    useSelectedChains: !!tokens,
  };

  if (!tokens) {
    const hotTokensOptions = getGetHotTokensV2PublicInfiniteQueryOptions(params);
    await queryClient.prefetchInfiniteQuery(hotTokensOptions);

    return (
      <HydrationBoundary state={dehydrate(queryClient)}>
        <PublicHotTokensDetailContent />
      </HydrationBoundary>
    );
  }

  const hotTokensOptions = getGetHotTokensV2InfiniteQueryOptions(params);
  await queryClient.prefetchInfiniteQuery(hotTokensOptions);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <HotTokensDetailContent />
    </HydrationBoundary>
  );
}
