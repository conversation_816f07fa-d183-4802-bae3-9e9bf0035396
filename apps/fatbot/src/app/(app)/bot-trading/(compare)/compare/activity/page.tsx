import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { getCompareBotsQueryOptions, getGetMyBotsQueryOptions, type TimeRange } from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { BotCompareContent } from '@/module/bot-trading/bot-compare/bot-compare-content';
import { MAX_BOTS_TO_COMPARE } from '@/module/bot-trading/bot-compare/constants';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';

export async function generateMetadata() {
  const t = await getTranslations('metadata.bot-trading');

  return {
    title: t('compare'),
  };
}

export default async function BotComparePage(props: { searchParams: Promise<{ timeRange?: TimeRange }> }) {
  const searchParams = await props.searchParams;
  const queryClient = getQueryClient();
  const bots = await queryClient.fetchQuery(
    getGetMyBotsQueryOptions({
      timeRange: searchParams.timeRange ?? BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
    }),
  );

  await Promise.all([
    queryClient.prefetchQuery(
      getCompareBotsQueryOptions({
        botIds: bots.map((bot) => bot.id).slice(0, MAX_BOTS_TO_COMPARE),
        timeRange: searchParams.timeRange ?? BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
      }),
    ),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <BotCompareContent />
    </HydrationBoundary>
  );
}
