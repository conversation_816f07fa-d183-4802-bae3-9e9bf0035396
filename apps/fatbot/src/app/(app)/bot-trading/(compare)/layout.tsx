import { getTranslations } from 'next-intl/server';

import PageWrapper from '@/components/page-wrapper';
import { BotTradingInnerHeader } from '@/module/bot-trading/components/bot-trading-inner-header';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('default'),
  };
}
interface Props {
  children: React.ReactNode;
}

export default function BotTradingLayout({ children }: Props) {
  return (
    <PageWrapper>
      <BotTradingInnerHeader />
      {children}
    </PageWrapper>
  );
}
