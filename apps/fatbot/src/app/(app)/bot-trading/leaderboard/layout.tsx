import PageWrapper from '@/components/page-wrapper';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { BotLeaderboardHeader } from '@/module/bot-trading/components/leaderboard/bot-leaderboard-header';

interface Props {
  children: React.ReactNode;
}

export default async function BotLeaderboardLayout({ children }: Props) {
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  return (
    <PageWrapper className="flex h-dvh w-full flex-col py-0!">
      <BotLeaderboardHeader leagueSystemEnabled={leagueSystemEnabled} />
      {children}
    </PageWrapper>
  );
}
