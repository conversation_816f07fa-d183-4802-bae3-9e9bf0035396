import { getTranslations } from 'next-intl/server';

import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getBotById } from '@/lib/api';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { BotWithdrawContent } from '@/module/bot-trading/bot-withdraw/bot-withdraw-content';
import { BotWithdrawHeader } from '@/module/bot-trading/bot-withdraw/bot-withdraw-header-inner';

export async function generateMetadata() {
  const t = await getTranslations('metadata.bot-trading');

  return {
    title: t('portfolio'),
  };
}

export default async function BotWithdrawPage({ params }: { params: Promise<{ botId: string }> }) {
  const { botId } = await params;
  const bot = await getBotById(botId);
  const leagueSystemEnabled = await getRemoteConfigValue(REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED);

  return (
    <>
      <BotWithdrawHeader botId={botId} leagueSystemEnabled={leagueSystemEnabled} />
      <BotWithdrawContent botId={botId} chain={bot.botWalletChain} />
    </>
  );
}
