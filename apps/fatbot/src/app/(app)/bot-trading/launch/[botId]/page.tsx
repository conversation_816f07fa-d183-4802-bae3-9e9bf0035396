import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';

import { LaunchingBotDialog } from '@/module/bot-trading/bot-wizard/components/launching-bot-dialog/launching-bot-dialog';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('manual-trading.bot-launch'),
  };
}
export default async function BotTradingLaunchPage({ params }: { params: Promise<{ botId: string }> }) {
  const { botId } = await params;

  if (!botId) {
    return notFound();
  }

  return <LaunchingBotDialog botId={botId} />;
}
