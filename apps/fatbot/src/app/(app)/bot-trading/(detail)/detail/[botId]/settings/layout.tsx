import { ROUTES } from '@/constants/routes';
import { BotDetailHeader } from '@/module/bot-trading/bot-detail/bot-detail-header';

interface Props {
  children: React.ReactNode;
  params: Promise<{ botId: string }>;
}

export default async function BotTradingDetailLayout({
  children,
  params,
}: Props) {
  const { botId } = await params;
  return (
    <>
      <BotDetailHeader backUrl={ROUTES.BOT_TRADING.DETAIL(botId)} />
      {children}
    </>
  );
}
