import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { getGetBotByIdQueryOptions } from '@/lib/api';
import { BotDetailSettingContent } from '@/module/bot-trading/bot-detail/bot-detail-setting-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata.bot-trading');

  return {
    title: t('bot-settings'),
  };
}

export default async function BotDetailSettingsPage(props: {
  params: Promise<{ botId: string }>;
}) {
  const { botId } = await props.params;

  const queryClient = new QueryClient();
  await queryClient.prefetchQuery(getGetBotByIdQueryOptions(botId));

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <BotDetailSettingContent />
    </HydrationBoundary>
  );
}
