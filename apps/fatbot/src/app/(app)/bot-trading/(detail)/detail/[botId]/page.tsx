import { dehydrate, HydrationBoundary, QueryClient } from '@tanstack/react-query';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';

import { ROUTES } from '@/constants/routes';
import type { TimeRange } from '@/lib/api';
import { getSearchUserBotDetailQueryOptions } from '@/lib/api';
import { BotDetailContent } from '@/module/bot-trading/bot-detail/bot-detail-content';
import { BotDetailHeader } from '@/module/bot-trading/bot-detail/bot-detail-header';
import { BotTransactionUpdateProvider } from '@/module/bot-trading/bot-detail/providers/bot-transaction-update-provider';

export async function generateMetadata() {
  const t = await getTranslations('metadata.bot-trading');

  return {
    title: t('detail'),
  };
}

export default async function BotDetailPage(props: {
  params: Promise<{ botId: string }>;
  searchParams: Promise<{
    timeRange: TimeRange;
  }>;
}) {
  const searchParams = await props.searchParams;
  const { botId } = await props.params;

  if (!botId) {
    return notFound();
  }
  const queryClient = new QueryClient();

  await Promise.all([
    queryClient.prefetchQuery(
      getSearchUserBotDetailQueryOptions({
        botId,
        timeRange: searchParams.timeRange,
      }),
    ),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <BotDetailHeader backUrl={ROUTES.BOT_TRADING.ROOT} />
      <BotTransactionUpdateProvider botId={botId}>
        <BotDetailContent botId={botId} />
      </BotTransactionUpdateProvider>
    </HydrationBoundary>
  );
}
