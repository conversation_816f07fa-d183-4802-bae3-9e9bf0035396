import PageWrapper from '@/components/page-wrapper';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { BotTradingComingSoon } from '@/module/bot-trading/coming-soon/coming-soon';
import { BotTradingHeader } from '@/module/bot-trading/components/bot-trading-header';

interface Props {
  children: React.ReactNode;
}

export default async function RootBotTradingLayout({ children }: Props) {
  const botTradingEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.BOT_TRADING_ENABLED
  );
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  if (!botTradingEnabled) {
    return (
      <PageWrapper>
        <BotTradingHeader leagueSystemEnabled={leagueSystemEnabled} />
        <BotTradingComingSoon />
      </PageWrapper>
    );
  }

  return children;
}
