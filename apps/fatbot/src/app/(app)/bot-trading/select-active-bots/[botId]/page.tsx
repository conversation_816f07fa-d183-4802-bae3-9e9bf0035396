import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';

import { getGetMyBotsQueryOptions, TimeRange } from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { ActiveBotsContent } from '@/module/bot-trading/active-bots/select-active-bots-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('active-bots'),
  };
}

export default async function SelectActiveBotsPage({
  params,
}: {
  params: Promise<{ botId: string }>;
}) {
  const { botId } = await params;

  if (!botId) {
    return notFound();
  }

  const queryClient = getQueryClient();
  const myBots = await queryClient.fetchQuery(
    getGetMyBotsQueryOptions({ timeRange: TimeRange.ALL })
  );
  const bot = myBots.find((bot) => bot.id === botId);

  if (!bot) {
    return notFound();
  }

  return <ActiveBotsContent myBots={myBots} />;
}
