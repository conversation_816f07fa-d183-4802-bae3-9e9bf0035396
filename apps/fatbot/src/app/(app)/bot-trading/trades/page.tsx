import { getTranslations } from 'next-intl/server';

import PageWrapper from '@/components/page-wrapper';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { BotTradesDetailContent } from '@/module/bot-trading/bot-trades/bot-trades-detail-content';
import { BotTradingInnerSecondaryHeader } from '@/module/bot-trading/components/bot-trading-inner-secondary-header';

export async function generateMetadata() {
  const t = await getTranslations('metadata.bot-trading');

  return {
    title: t('trades'),
  };
}

export default async function BotDetailTradesPage() {
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  return (
    <PageWrapper>
      <BotTradingInnerSecondaryHeader
        leagueSystemEnabled={leagueSystemEnabled}
      />
      <BotTradesDetailContent />
    </PageWrapper>
  );
}
