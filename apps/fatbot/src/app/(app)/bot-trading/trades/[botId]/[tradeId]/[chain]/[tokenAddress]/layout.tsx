import type { ReactNode } from 'react';

import { type Chain, getAllChains } from '@/lib/api';
import { TokenChartProvider } from '@/module/token-detail/token-chart-provider';

interface Props {
  children?: ReactNode;
  params: Promise<{
    botId: string;
    tradeId: string;
    chain: Chain;
    tokenAddress: string;
  }>;
}

export default async function BotTradeDetailLayout({
  children,
  params,
}: Props) {
  const { chain, tokenAddress } = await params;
  const chains = await getAllChains();

  return (
    <TokenChartProvider
      chain={chain}
      enabledChains={chains.enabledChains}
      tokenAddress={tokenAddress}
    >
      {children}
    </TokenChartProvider>
  );
}
