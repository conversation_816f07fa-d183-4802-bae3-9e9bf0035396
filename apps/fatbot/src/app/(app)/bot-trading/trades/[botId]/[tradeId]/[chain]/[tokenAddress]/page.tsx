import { getTranslations } from 'next-intl/server';

import PageWrapper from '@/components/page-wrapper';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { BotTradeDetailContent } from '@/module/bot-trading/bot-trade-detail/bot-trade-detail-content';
import { BotTradingInnerSecondaryHeader } from '@/module/bot-trading/components/bot-trading-inner-secondary-header';

export async function generateMetadata() {
  const t = await getTranslations('metadata.bot-trading');

  return {
    title: t('trade-detail'),
  };
}

export default async function BotTradeDetailPage() {
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  return (
    <PageWrapper>
      <BotTradingInnerSecondaryHeader
        leagueSystemEnabled={leagueSystemEnabled}
      />
      <BotTradeDetailContent />
    </PageWrapper>
  );
}
