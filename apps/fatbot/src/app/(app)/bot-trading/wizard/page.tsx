import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';
import type { Viewport } from 'next';
import { getTranslations } from 'next-intl/server';

import {
  getGetAllBotDraftsQueryOptions,
  getGetBotSettingsStatisticsQueryOptions,
  getGetMyBotsQueryOptions,
  TimeRange,
} from '@/lib/api';
import { BotWizardContent } from '@/module/bot-trading/bot-wizard/bot-wizard-content';

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
};

export async function generateMetadata() {
  const t = await getTranslations('metadata.bot-trading');

  return {
    title: t('wizard'),
  };
}

export default async function BotWizardPage() {
  const queryClient = new QueryClient();

  await Promise.all([
    queryClient.prefetchQuery(getGetBotSettingsStatisticsQueryOptions()),
    queryClient.prefetchQuery(
      getGetMyBotsQueryOptions({ timeRange: TimeRange.ALL })
    ),
    queryClient.prefetchQuery(getGetAllBotDraftsQueryOptions()),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <BotWizardContent />
    </HydrationBoundary>
  );
}
