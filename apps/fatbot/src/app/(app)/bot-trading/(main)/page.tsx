import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import {
  BotSortParameter,
  getBotsLeaderboardQueryOptions,
  getBotsOverviewQueryOptions,
  getGetAllUserWalletsV3QueryOptions,
  getGetMyBotsQueryOptions,
} from '@/lib/api';
import { getTokens } from '@/lib/firebase/get-tokens';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { getQueryClient } from '@/lib/query-client';
import { BotTradingAuthenticatedContent } from '@/module/bot-trading/bot-trading-authenticated-content';
import { BotTradingPublicContent } from '@/module/bot-trading/bot-trading-public-content';
import { BotTradingHeader } from '@/module/bot-trading/components/bot-trading-header';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';
import { OnboardingHeader } from '@/module/onboarding/onboarding-header';

export async function generateMetadata() {
  const t = await getTranslations('metadata.bot-trading');

  return {
    title: t('root'),
  };
}

const DEFAULT_LEADERBOARD_SIZE = 5;

export default async function BotTradingPage() {
  const tokens = await getTokens();
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  if (!tokens) {
    return (
      <>
        <OnboardingHeader leagueSystemEnabled={leagueSystemEnabled} />
        <BotTradingPublicContent />
      </>
    );
  }

  const queryClient = getQueryClient();

  await Promise.all([
    queryClient.prefetchQuery(
      getGetAllUserWalletsV3QueryOptions({ useSelectedChains: true })
    ),
    queryClient.prefetchQuery(
      getBotsLeaderboardQueryOptions(
        {
          timeRange: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
          allBots: true,
          sortParameter: BotSortParameter.PROFIT_PERCENTAGE,
        },
        { size: DEFAULT_LEADERBOARD_SIZE }
      )
    ),
    queryClient.prefetchQuery(
      getGetMyBotsQueryOptions({
        timeRange: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
      })
    ),
    queryClient.prefetchQuery(
      getBotsOverviewQueryOptions({
        timeRange: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
      })
    ),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <BotTradingHeader leagueSystemEnabled={leagueSystemEnabled} />
      <BotTradingAuthenticatedContent />
    </HydrationBoundary>
  );
}
