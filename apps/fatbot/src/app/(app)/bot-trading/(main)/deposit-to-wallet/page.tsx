import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getGetMyBotsQueryOptions } from '@/lib/api';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { getQueryClient } from '@/lib/query-client';
import { BotDepositToWalletContent } from '@/module/bot-trading/bot-deposit-to-wallet/bot-deposit-to-wallet-content';
import { BotTradingInnerSecondaryHeader } from '@/module/bot-trading/components/bot-trading-inner-secondary-header';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('manual-trading.deposit-to-wallet'),
  };
}

export default async function BotDeposit() {
  const queryClient = getQueryClient();
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  await queryClient.prefetchQuery(
    getGetMyBotsQueryOptions({
      timeRange: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
    })
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <BotTradingInnerSecondaryHeader
        leagueSystemEnabled={leagueSystemEnabled}
      />
      <BotDepositToWalletContent />
    </HydrationBoundary>
  );
}
