import { getTranslations } from 'next-intl/server';

import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { BotPortfolioContent } from '@/module/bot-trading/bot-portfolio/bot-portfolio-detail-content';
import { BotTradingInnerSecondaryHeader } from '@/module/bot-trading/components/bot-trading-inner-secondary-header';

export async function generateMetadata() {
  const t = await getTranslations('metadata.bot-trading');

  return {
    title: t('portfolio'),
  };
}

export default async function PortfolioPage() {
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  return (
    <>
      <BotTradingInnerSecondaryHeader
        leagueSystemEnabled={leagueSystemEnabled}
      />
      <BotPortfolioContent />
    </>
  );
}
