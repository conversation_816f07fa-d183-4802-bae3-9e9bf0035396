import { getTranslations } from 'next-intl/server';

import { LeagueHeader } from '@/module/league/components/league-header';
import { LeagueLeaderboardContent } from '@/module/league/league-leaderboard-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('league.root'),
  };
}

export default async function LeagueLeaderboardPage() {
  return (
    <>
      <LeagueHeader leagueSystemEnabled />
      <LeagueLeaderboardContent />;
    </>
  );
}
