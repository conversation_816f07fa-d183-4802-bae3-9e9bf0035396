import { getTranslations } from 'next-intl/server';

import { LeagueHeader } from '@/module/league/components/league-header';
import { LeagueLeaderboardDetailContent } from '@/module/league/league-leaderboard-detail-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('league'),
  };
}

export default function LeagueLeaderboardDetailPage() {
  return (
    <>
      <LeagueHeader leagueSystemEnabled />
      <LeagueLeaderboardDetailContent />;
    </>
  );
}
