import { getTranslations } from 'next-intl/server';

import { LeagueHeader } from '@/module/league/components/league-header';
import { LeagueAuthenticatedContent } from '@/module/league/league-authenticated-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('league.root'),
  };
}

export default async function LeaguePage() {
  return (
    <>
      <LeagueHeader leagueSystemEnabled />
      <LeagueAuthenticatedContent />;
    </>
  );
}
