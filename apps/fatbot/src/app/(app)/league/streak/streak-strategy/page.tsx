import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import {
  getGetUserStreakGeneralInfoQueryOptions,
  getGetUserStreakQueryOptions,
} from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { LeagueHeader } from '@/module/league/components/league-header';
import { StreakStrategyContent } from '@/module/league/streak/streak-strategy-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('league.root'),
  };
}

export default async function StreakStrategyPage() {
  const queryClient = getQueryClient();

  await Promise.all([
    queryClient.prefetchQuery(getGetUserStreakGeneralInfoQueryOptions()),
    queryClient.prefetchQuery(getGetUserStreakQueryOptions()),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <LeagueHeader leagueSystemEnabled />
      <StreakStrategyContent />
    </HydrationBoundary>
  );
}
