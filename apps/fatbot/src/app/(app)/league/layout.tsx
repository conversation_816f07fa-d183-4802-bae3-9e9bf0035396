import { redirect } from 'next/navigation'

import PageWrapper from '@/components/page-wrapper';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { ROUTES } from '@/constants/routes';
import { getTokens } from '@/lib/firebase/get-tokens';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';

interface Props {
  children: React.ReactNode;
}

export default async function RootLeagueLayout({ children }: Props) {
  const [leagueEnabled, tokens] = await Promise.all([
    getRemoteConfigValue(REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED),
    getTokens(),
  ]);

  if (!leagueEnabled || !tokens) {
    redirect(ROUTES.HOME);
  }

  return <PageWrapper>{children}</PageWrapper>;
}
