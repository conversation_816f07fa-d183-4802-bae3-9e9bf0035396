import { getLocale } from 'next-intl/server';

import PageWrapper from '@/components/page-wrapper';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { ROUTES } from '@/constants/routes';
import { redirect } from '@/i18n/routing';
import { getTokens } from '@/lib/firebase/get-tokens';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';

interface Props {
  children: React.ReactNode;
}

export default async function RootLeagueLayout({ children }: Props) {
  const [leagueEnabled, tokens, locale] = await Promise.all([
    getRemoteConfigValue(REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED),
    getTokens(),
    getLocale(),
  ]);

  if (!leagueEnabled || !tokens) {
    redirect({ href: ROUTES.HOME, locale });
  }

  return <PageWrapper>{children}</PageWrapper>;
}
