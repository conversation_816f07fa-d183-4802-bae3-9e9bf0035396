import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { getGetAllUserWalletsV3QueryOptions } from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { WalletsContent } from '@/module/wallets/wallets-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata.wallets');

  return {
    title: t('root'),
  };
}

export default async function Wallets() {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery(
    getGetAllUserWalletsV3QueryOptions({ useSelectedChains: false })
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <WalletsContent />
    </HydrationBoundary>
  );
}
