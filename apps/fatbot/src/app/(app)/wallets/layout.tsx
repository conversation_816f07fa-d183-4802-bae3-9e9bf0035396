import type { ReactNode } from 'react';

import PageWrapper from '@/components/page-wrapper';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { DepositToWalletsHeader } from '@/module/deposit-to-wallet/deposit-to-wallet-header';

export default async function WalletsLayout({
  children,
}: {
  children: ReactNode;
}) {
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  return (
    <PageWrapper>
      <DepositToWalletsHeader leagueSystemEnabled={leagueSystemEnabled} />
      {children}
    </PageWrapper>
  );
}
