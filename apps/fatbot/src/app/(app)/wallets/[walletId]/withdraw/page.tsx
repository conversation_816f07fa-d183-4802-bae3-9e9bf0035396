import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';

import { isLeft } from '@/lib/either';
import { getUserWalletById } from '@/module/wallet-detail/get-user-wallet.action';
import { WalletWithdrawContent } from '@/module/wallet-withdraw/wallet-withdraw-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata.manual-trading');

  return {
    title: t('wallet-withdraw'),
  };
}

export default async function WalletWithdrawPage(props: {
  params: Promise<{ walletId: string }>;
}) {
  const params = await props.params;
  const walletEither = await getUserWalletById(params.walletId);

  if (isLeft(walletEither)) {
    return notFound();
  }

  return (
    <WalletWithdrawContent
      chain={walletEither.right.chain}
      walletAddress={walletEither.right.walletAddress}
      walletId={params.walletId}
    />
  );
}
