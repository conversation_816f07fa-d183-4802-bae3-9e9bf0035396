import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { getGetUserWalletQueryOptions } from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { WalletDetailContent } from '@/module/wallet-detail/wallet-detail-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata.wallets');

  return {
    title: t('wallet-detail'),
  };
}
interface Props {
  params: Promise<{ walletId: string }>;
}

export default async function WalletDetailPage(props: Props) {
  const params = await props.params;
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery(
    getGetUserWalletQueryOptions(params.walletId)
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <WalletDetailContent walletId={params.walletId} />
    </HydrationBoundary>
  );
}
