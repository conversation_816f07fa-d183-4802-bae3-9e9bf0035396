import { getTranslations } from 'next-intl/server';

import { SetupWalletDoneContent } from '@/module/setup/setup-wallet-done-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata.wallets');

  return {
    title: t('done'),
  };
}

export default async function SetupWalletDone(props: {
  searchParams: Promise<{
    type: 'create' | 'import';
    walletAddress: string;
  }>;
}) {
  const searchParams = await props.searchParams;
  return <SetupWalletDoneContent type={searchParams.type} walletAddress={searchParams.walletAddress} />;
}
