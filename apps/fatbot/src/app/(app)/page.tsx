import { cookies } from 'next/headers';
import { getTranslations } from 'next-intl/server';

import { AppHeader } from '@/components/app-header/app-header';
import PageWrapper from '@/components/page-wrapper';
import { HIDE_ONBOARDING_COOKIE_NAME } from '@/constants/config';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getTokens } from '@/lib/firebase/get-tokens';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { HomeContent } from '@/module/home/<USER>';
import { PublicHomeContentDesktop } from '@/module/home/<USER>/public-home-content-desktop';
import { PublicHomeContentMobile } from '@/module/home/<USER>/public-home-content-mobile';
import { PublicHomeContentTablet } from '@/module/home/<USER>/public-home-content-tablet';
import { OnboardingDialog } from '@/module/onboarding/onboarding-dialog';
import { OnboardingHeader } from '@/module/onboarding/onboarding-header';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('default'),
  };
}

export default async function Home() {
  const tokens = await getTokens();
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  const showOnboarding =
    (await cookies()).get(HIDE_ONBOARDING_COOKIE_NAME)?.value !== 'true';

  if (!tokens) {
    return (
      <PageWrapper>
        <OnboardingHeader leagueSystemEnabled={leagueSystemEnabled} />
        {showOnboarding ? <OnboardingDialog /> : null}

        <div className="block md:hidden">
          <PublicHomeContentMobile />
        </div>
        <div className="hidden md:block xl:hidden">
          <PublicHomeContentTablet />
        </div>
        <div className="hidden xl:block">
          <PublicHomeContentDesktop />
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <AppHeader leagueSystemEnabled={leagueSystemEnabled} />
      <HomeContent />
    </PageWrapper>
  );
}
