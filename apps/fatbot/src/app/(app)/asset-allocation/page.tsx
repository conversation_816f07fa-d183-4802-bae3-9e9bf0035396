import { getTranslations } from 'next-intl/server';

import { Card } from '@/components/ui/card';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { AssetAllocation } from '@/module/home/<USER>';
import { AssetAllocationWithoutBots } from '@/module/home/<USER>/asset-allocation-without-bots';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('asset-allocation.root'),
  };
}

export default async function AssetAllocationPage() {
  const botTradingEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.BOT_TRADING_ENABLED
  );

  return (
    <Card className="mx-auto max-w-(--breakpoint-lg)" variant="area">
      {botTradingEnabled ? (
        <AssetAllocation titleClassName="text-display-m" />
      ) : (
        <AssetAllocationWithoutBots />
      )}
    </Card>
  );
}
