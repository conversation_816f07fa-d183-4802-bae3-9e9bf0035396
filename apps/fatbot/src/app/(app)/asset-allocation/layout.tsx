import PageWrapper from '@/components/page-wrapper';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { AllocationHeader } from '@/module/asset-allocation/components/allocation-header';

interface Props {
  children: React.ReactNode;
}

export default async function AssetAllocationLayout({ children }: Props) {
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  return (
    <PageWrapper>
      <AllocationHeader leagueSystemEnabled={leagueSystemEnabled} />
      {children}
    </PageWrapper>
  );
}
