import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import {
  getGetAllUserWalletsV3QueryOptions,
  getGetClaimingLimitsQueryOptions,
  getGetReferralRewardsSummaryQueryOptions,
  getGetUserQueryOptions,
} from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { ReferralContent } from '@/module/referral/referral-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('profile'),
  };
}

export default async function Profile() {
  const queryClient = getQueryClient();

  const user = await queryClient.fetchQuery(getGetUserQueryOptions());

  const wallets = await queryClient.fetchQuery(
    getGetAllUserWalletsV3QueryOptions({ useSelectedChains: false })
  );

  await Promise.all([
    queryClient.prefetchQuery(getGetReferralRewardsSummaryQueryOptions()),
    queryClient.prefetchQuery(getGetClaimingLimitsQueryOptions()),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ReferralContent
        isReferralCodeSet={!!user.referralCode}
        wallets={wallets}
      />
    </HydrationBoundary>
  );
}
