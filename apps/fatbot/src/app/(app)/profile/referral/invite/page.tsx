import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import { getGetUserQueryOptions } from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { InviteContent } from '@/module/invite/invite-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('invite'),
  };
}

export default async function Invite() {
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery(getGetUserQueryOptions());

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <InviteContent />
    </HydrationBoundary>
  );
}
