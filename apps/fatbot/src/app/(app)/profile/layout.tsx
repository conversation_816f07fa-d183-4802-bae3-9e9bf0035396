import PageWrapper from '@/components/page-wrapper';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { ReferralHeader } from '@/module/referral/referral-header';

interface Props {
  children: React.ReactNode;
}

export default async function ProfileLayout({ children }: Props) {
  const leagueSystemEnabled = await getRemoteConfigValue(
    REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED
  );

  return (
    <PageWrapper>
      <ReferralHeader leagueSystemEnabled={leagueSystemEnabled} />
      {children}
    </PageWrapper>
  );
}
