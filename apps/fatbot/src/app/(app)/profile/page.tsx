import { dehydrate, HydrationBoundary } from '@tanstack/react-query';
import { getTranslations } from 'next-intl/server';

import {
  getGetAllUserWalletsV3QueryOptions,
  getGetUserLifetimeTradeVolumeQueryOptions,
} from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { ProfileContent } from '@/module/profile/profile-content';

export async function generateMetadata() {
  const t = await getTranslations('metadata');

  return {
    title: t('profile'),
  };
}

export default async function Profile() {
  const queryClient = getQueryClient();

  const userWalletsOptions = getGetAllUserWalletsV3QueryOptions({
    useSelectedChains: true,
  });

  await Promise.all([
    queryClient.prefetchQuery(userWalletsOptions),
    queryClient.prefetchQuery(getGetUserLifetimeTradeVolumeQueryOptions()),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ProfileContent />
    </HydrationBoundary>
  );
}
