import Script from 'next/script';
import { NextIntlClientProvider } from 'next-intl'
import { getLocale } from 'next-intl/server';

import { goldplay, sailec } from '@/app/fonts';
import { AppWrapper } from '@/components/app-wrapper';
import { ReactScan } from '@/components/react-scan';
import { Toaster } from '@/components/sonner';
import { REMOTE_CONFIG_KEYS } from '@/constants/remote-config';
import { getTokens } from '@/lib/firebase/get-tokens';
import { getRemoteConfigValue } from '@/lib/firebase/remote-config';
import { getMaintenanceConfig } from '@/module/maintenance/get-maintenance-config';
import { MaintenanceContent } from '@/module/maintenance/maintenance-content';
import { Providers } from '@/providers/providers';
import { env } from '@env/client';

interface Props {
  children: React.ReactNode;
}

export default async function RootLayout({ children } :Props) {
  const locale = await getLocale();
  const tokens = await getTokens();

  const [isMaintenanceEnabled, isLeagueFeatureEnabled] = await Promise.all([
    getMaintenanceConfig(),
    getRemoteConfigValue(REMOTE_CONFIG_KEYS.LEAGUE_SYSTEM_ENABLED),
  ]);

  return (
    <html lang={locale} suppressHydrationWarning={true}>
      <head>
        {env.NEXT_PUBLIC_GTM_ID ? (
          <Script
            dangerouslySetInnerHTML={{
              __html: `
              (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
              new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
              j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
              'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
              })(window,document,'script','dataLayer', '${env.NEXT_PUBLIC_GTM_ID}');
            `,
            }}
            id="google-tag-manager"
            strategy="afterInteractive"
          />
        ) : null}
      </head>
      <body
        className={`${goldplay.className} ${sailec.variable} ${goldplay.variable} bg-surface-background antialiased`}
      >
        <NextIntlClientProvider>
          <AppWrapper>
            <Providers
              isLeagueFeatureEnabled={isLeagueFeatureEnabled}
              token={tokens?.token}
            >
              {isMaintenanceEnabled ? <MaintenanceContent /> : children}
              {env.NEXT_PUBLIC_REACT_SCAN_ENABLED ? <ReactScan /> : null}
              <Toaster />
            </Providers>
          </AppWrapper>
        </NextIntlClientProvider>
        {env.NEXT_PUBLIC_GTM_ID ? (
          <noscript>
            <iframe
              className="hidden"
              height="0"
              src={`https://www.googletagmanager.com/ns.html?id=${env.NEXT_PUBLIC_GTM_ID}`}
              width="0"
            />
          </noscript>
        ) : null}
      </body>
    </html>
  );
}
