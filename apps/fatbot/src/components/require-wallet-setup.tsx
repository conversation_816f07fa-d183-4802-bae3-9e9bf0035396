import { redirect } from 'next/navigation'
import type { PropsWithChildren } from 'react';

import { DEPOSIT_DIALOG_SHOWN_COOKIE_NAME } from '@/constants/config';
import { ROUTES } from '@/constants/routes';
import { getAllUserWalletsV3 } from '@/lib/api';
import { isCookieValueTrue } from '@/lib/is-cookie-value-true';
import { WalletDepositDialog } from '@/module/setup/wallet-deposit-dialog/wallet-deposit-dialog';

export const RequireWalletSetup: React.FC<PropsWithChildren> = async ({ children }) => {
  const wallets = await getAllUserWalletsV3({
    useSelectedChains: false,
  });

  if (wallets.length === 0) {
    redirect(ROUTES.SETUP_WALLET.ROOT);
  }

  const depositDialogShown = await isCookieValueTrue(DEPOSIT_DIALOG_SHOWN_COOKIE_NAME);

  return (
    <>
      <WalletDepositDialog defaultOpen={!depositDialogShown} enableDownload wallets={wallets} />
      {children}
    </>
  );
};
