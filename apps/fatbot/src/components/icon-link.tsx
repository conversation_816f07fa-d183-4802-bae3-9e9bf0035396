import Link from 'next/link';
import type { ComponentProps, FC, PropsWithChildren } from 'react';

import { cn } from '@/lib/utils';

type Props = PropsWithChildren<ComponentProps<typeof Link>>;

export const IconLink: FC<Props> = ({ children, className, ...linkProps }) => (
  <Link
    {...linkProps}
    className={cn(
      'inline-block shrink-0 rounded-xxs p-0.5 text-text-secondary hover:bg-surface-active/10 hover:text-surface-active',
      className,
    )}
  >
    {children}
  </Link>
);
