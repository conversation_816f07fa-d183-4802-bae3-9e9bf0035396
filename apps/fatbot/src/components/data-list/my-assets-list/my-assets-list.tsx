'use client';

import { usePathname, useRouter } from 'next/navigation'

import { ROUTES } from '@/constants/routes';
import type { InfiniteScrollSliceSearchUserMarketPositionResultUUID } from '@/lib/api';
import { isNativeToken } from '@/module/assets/utils';

import { MyAssetsRow } from './my-assets-row';

interface Props {
  data: InfiniteScrollSliceSearchUserMarketPositionResultUUID['content'];
}

export const MyAssetsList: React.FC<Props> = ({ data }) => {
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div className="grid gap-2">
      {data.map((item) => (
        <MyAssetsRow
          key={item.id}
          chain={item.tokenChain}
          currentValueChangeFraction={item.currentValueChangeFraction}
          currentValueChangeUsd={item.currentValueChangeUsd}
          currentValueUsd={item.currentValueUsd}
          image={item.tokenImageUrl}
          oneDayChangeFraction={item.oneDayChangeFraction}
          oneHourChangeFraction={item.oneHourChangeFraction}
          pricePerTokenInUsd={item.pricePerTokenInUsd}
          tokenAmount={item.tokenNativeAmount}
          tokenDetailUrl={item.tokenDetailUrl}
          tokenName={item.tokenName}
          tokenSymbol={item.tokenSymbol}
          onClick={
            !isNativeToken(item)
              ? () => {
                  router.push(ROUTES.MANUAL_TRADING.TOKEN_DETAIL(item.tokenAddress, item.tokenChain, pathname));
                }
              : undefined
          }
        />
      ))}
    </div>
  );
};
