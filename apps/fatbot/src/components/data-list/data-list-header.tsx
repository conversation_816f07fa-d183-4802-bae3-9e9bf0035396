import Link from 'next/link'
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { cn } from '@/lib/utils';

interface Props {
  title: string;
  link?: string;
  titleClassName?: string;
}

export const DataListHeader: React.FC<Props> = ({ title, link = '', titleClassName = 'text-display-xs' }) => {
  const t = useTranslations();

  const header = useMemo(
    () => (
      <div className="group flex items-end justify-between">
        <h3 className={cn('font-bold capitalize text-text-primary', titleClassName)}>{title}</h3>
        {link ? (
          <span
            className={cn(
              'whitespace-nowrap text-md font-semibold capitalize text-text-secondary transition-colors hover:text-text-primary',
              {
                'group-hover:text-white': link,
              },
            )}
          >
            {t('lists.see-all')}
          </span>
        ) : null}
      </div>
    ),
    [t, title, link, titleClassName],
  );

  return link ? <Link href={link}>{header}</Link> : header;
};
