import { render, screen } from '@testing-library/react';
import type { Mo<PERSON> } from '@vitest/spy';
import { useSpring } from 'framer-motion';

import { AnimateNumber } from '@/components/animate-number';

// Mock framer-motion's useSpring hook
vi.mock('framer-motion', () => ({
  useSpring: vi.fn(),
}));

describe('AnimateNumber', () => {
  // Setup mock implementation for useSpring
  const mockSet = vi.fn();
  const mockOn = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Default mock implementation
    (useSpring as Mock).mockReturnValue({
      set: mockSet,
      on: mockOn,
    });
  });

  it('renders the initial value', () => {
    // Arrange
    const initialValue = 100;
    const renderFunction = vi.fn((value) => <div data-testid="animated-value">{value}</div>);

    // Act
    render(<AnimateNumber value={initialValue}>{renderFunction}</AnimateNumber>);

    // Assert
    expect(renderFunction).toHaveBeenCalledWith(initialValue);
    expect(screen.getByTestId('animated-value')).toHaveTextContent('100');
  });

  it('sets up the spring animation with correct parameters', () => {
    // Arrange
    const initialValue = 100;

    // Act
    render(<AnimateNumber value={initialValue}>{(value) => <div>{value}</div>}</AnimateNumber>);

    // Assert
    expect(useSpring).toHaveBeenCalledWith(initialValue, {
      bounce: 0,
      duration: 1_500,
    });
  });

  it('updates the spring target value when the value prop changes', () => {
    // Arrange
    const initialValue = 100;
    const renderFunction = vi.fn((value) => <div>{value}</div>);

    // Act
    const { rerender } = render(<AnimateNumber value={initialValue}>{renderFunction}</AnimateNumber>);

    // Change the value prop
    const newValue = 200;
    rerender(<AnimateNumber value={newValue}>{renderFunction}</AnimateNumber>);

    // Assert
    expect(mockSet).toHaveBeenCalledWith(newValue);
  });

  it('updates the current value when the spring animation changes', () => {
    // Arrange
    const initialValue = 100;
    const animatedValue = 150;
    const renderFunction = vi.fn((value) => <div data-testid="animated-value">{value}</div>);

    // Simulate the spring.on('change') callback
    mockOn.mockImplementation((event, callback: (value: number) => void) => {
      if (event === 'change') {
        callback(animatedValue);
      }
      return { on: mockOn };
    });

    // Act
    render(<AnimateNumber value={initialValue}>{renderFunction}</AnimateNumber>);

    // Assert
    expect(mockOn).toHaveBeenCalledWith('change', expect.any(Function));
    expect(renderFunction).toHaveBeenCalledWith(animatedValue);
    expect(screen.getByTestId('animated-value')).toHaveTextContent('150');
  });
});
