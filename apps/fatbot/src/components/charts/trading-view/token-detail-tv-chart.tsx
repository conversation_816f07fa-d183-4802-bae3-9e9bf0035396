import dynamic from 'next/dynamic';
import { type FC } from 'react';

import type { <PERSON>, Dex } from '@/lib/api';

const TradingViewWidget = dynamic(
  () => import('@/components/charts/trading-view/widget').then((module_) => module_.TradingViewWidget),
  {
    ssr: false,
  },
);

interface Props {
  chain: Chain;
  tokenAddress: string;
  format?: 'price' | 'volume';
  dex: Dex;
  tokenSymbol: string;
}

export const TokenDetailTVChart: FC<Props> = (props) => <TradingViewWidget {...props} className="flex flex-col" />;
