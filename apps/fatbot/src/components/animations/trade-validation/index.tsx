'use client';

import { useEffect, useState } from 'react';

import { TradeValidationLoading } from './trade-validation-loading';

interface Props {
  className?: string;
}

// eslint-disable-next-line @typescript-eslint/consistent-type-imports
type State = typeof import('./trade-validation-lottie');

export const TradeValidationAnimation = (props: Props) => {
  const [module, setModule] = useState<State>();

  useEffect(() => {
    void import('./trade-validation-lottie').then((module) => {
      setModule(module);
    });
  }, []);

  if (module) {
    return <module.TradeValidationLottie {...props} />;
  }

  return <TradeValidationLoading className={props.className} />;
};
