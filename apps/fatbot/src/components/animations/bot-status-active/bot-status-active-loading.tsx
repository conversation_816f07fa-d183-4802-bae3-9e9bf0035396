import type { SVGProps } from 'react';

export const BotStatusActiveLoading = (props: SVGProps<SVGSVGElement>) => (
  <svg
    height="308"
    preserveAspectRatio="xMidYMid meet"
    style={{
      width: '100%',
      height: '100%',
      transform: 'translate3d(0px, 0px, 0px)',
      contentVisibility: 'visible',
    }}
    viewBox="0 0 38 38"
    width="308"
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <defs>
      <clipPath id="__lottie_element_2">
        <rect height="38" width="38" x="0" y="0" />
      </clipPath>
      <clipPath id="__lottie_element_11">
        <path d="M0,0 L10,0 L10,10 L0,10z" />
      </clipPath>
      <clipPath id="__lottie_element_20">
        <path d="M0,0 L10,0 L10,10 L0,10z" />
      </clipPath>
      <clipPath id="__lottie_element_29">
        <path d="M0,0 L10,0 L10,10 L0,10z" />
      </clipPath>
      <g id="__lottie_element_37">
        <g opacity="1" style={{ display: 'block' }} transform="matrix(1,0,0,1,3,3)">
          <path
            d=" M16,0 C24.830400466918945,0 32,7.169600009918213 32,16 C32,24.830400466918945 24.830400466918945,32 16,32 C7.169600009918213,32 0,24.830400466918945 0,16 C0,7.169600009918213 7.169600009918213,0 16,0z"
            fill="rgb(0,0,0)"
            fillOpacity="1"
          />
        </g>
      </g>
      <mask id="__lottie_element_37_1" type="alpha">
        <use href="#__lottie_element_37" />
      </mask>
      <g id="__lottie_element_45">
        <g opacity="1" style={{ display: 'block' }} transform="matrix(1,0,0,1,9,9)">
          <path
            d=" M10,0 C15.519000053405762,0 20,4.480999946594238 20,10 C20,15.519000053405762 15.519000053405762,20 10,20 C4.480999946594238,20 0,15.519000053405762 0,10 C0,4.480999946594238 4.480999946594238,0 10,0z"
            fill="rgb(0,0,0)"
            fillOpacity="1"
          />
        </g>
      </g>
      <mask id="__lottie_element_45_1" type="alpha">
        <use href="#__lottie_element_45" />
      </mask>
      <g id="__lottie_element_52">
        <g opacity="1" style={{ display: 'block' }} transform="matrix(1,0,0,1,15,15)">
          <path
            d=" M4,0 C6.207600116729736,0 8,1.7924000024795532 8,4 C8,6.207600116729736 6.207600116729736,8 4,8 C1.7924000024795532,8 0,6.207600116729736 0,4 C0,1.7924000024795532 1.7924000024795532,0 4,0z"
            fill="rgb(0,0,0)"
            fillOpacity="1"
          />
        </g>
      </g>
      <mask id="__lottie_element_52_1" type="alpha">
        <use href="#__lottie_element_52" />
      </mask>
    </defs>
    <g clipPath="url(#__lottie_element_2)">
      <g opacity="1" style={{ display: 'block' }} transform="matrix(1,0,0,1,0,0)">
        <path
          d=" M38,0 C38,0 0,0 0,0 C0,0 0,38 0,38 C0,38 38,38 38,38 C38,38 38,0 38,0z"
          fill="rgb(23,80,53)"
          fillOpacity="1"
        />
      </g>
      <g mask="url(#__lottie_element_52_1)" style={{ display: 'block' }}>
        <g opacity="1" transform="matrix(1,0,0,1,15,15)">
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M4,0 C6.207600116729736,0 8,1.7924000024795532 8,4 C8,6.207600116729736 6.207600116729736,8 4,8 C1.7924000024795532,8 0,6.207600116729736 0,4 C0,1.7924000024795532 1.7924000024795532,0 4,0z"
              fillOpacity="0"
              stroke="rgb(27,153,94)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="4"
            />
          </g>
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M10,-2 C10,-2 -2,-2 -2,-2 C-2,-2 -2,10 -2,10 C-2,10 10,10 10,10 C10,10 10,-2 10,-2z"
              fill="rgb(0,0,0)"
              fillOpacity="0"
            />
          </g>
        </g>
      </g>
      <g mask="url(#__lottie_element_45_1)" style={{ display: 'block' }}>
        <g opacity="1" transform="matrix(1,0,0,1,9,9)">
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M10,0 C15.519000053405762,0 20,4.480999946594238 20,10 C20,15.519000053405762 15.519000053405762,20 10,20 C4.480999946594238,20 0,15.519000053405762 0,10 C0,4.480999946594238 4.480999946594238,0 10,0z"
              fillOpacity="0"
              stroke="rgb(27,153,94)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="4"
            />
          </g>
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M22,-2 C22,-2 -2,-2 -2,-2 C-2,-2 -2,22 -2,22 C-2,22 22,22 22,22 C22,22 22,-2 22,-2z"
              fill="rgb(0,0,0)"
              fillOpacity="0"
            />
          </g>
        </g>
      </g>
      <g mask="url(#__lottie_element_37_1)" style={{ display: 'block' }}>
        <g opacity="1" transform="matrix(1,0,0,1,3,3)">
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M16,0 C24.830400466918945,0 32,7.169600009918213 32,16 C32,24.830400466918945 24.830400466918945,32 16,32 C7.169600009918213,32 0,24.830400466918945 0,16 C0,7.169600009918213 7.169600009918213,0 16,0z"
              fillOpacity="0"
              stroke="rgb(27,153,94)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="4"
            />
          </g>
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M34,-2 C34,-2 -2,-2 -2,-2 C-2,-2 -2,34 -2,34 C-2,34 34,34 34,34 C34,34 34,-2 34,-2z"
              fill="rgb(0,0,0)"
              fillOpacity="0"
            />
          </g>
        </g>
      </g>
      <g
        clipPath="url(#__lottie_element_29)"
        opacity="0"
        style={{ display: 'block' }}
        transform="matrix(1,0,0,1,14,-1)"
      >
        <g opacity="1" style={{ display: 'block' }} transform="matrix(0,1,-1,0,7,3)">
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M2,0 C3.103800058364868,0 4,0.8962000012397766 4,2 C4,3.103800058364868 3.103800058364868,4 2,4 C0.8962000012397766,4 0,3.103800058364868 0,2 C0,0.8962000012397766 0.8962000012397766,0 2,0z"
              fillOpacity="0"
              stroke="rgb(23,80,53)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="4"
            />
            <path
              d=" M2,0 C3.103800058364868,0 4,0.8962000012397766 4,2 C4,3.103800058364868 3.103800058364868,4 2,4 C0.8962000012397766,4 0,3.103800058364868 0,2 C0,0.8962000012397766 0.8962000012397766,0 2,0z"
              fill="rgb(29,225,133)"
              fillOpacity="1"
            />
          </g>
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M6,-2 C6,-2 -2,-2 -2,-2 C-2,-2 -2,6 -2,6 C-2,6 6,6 6,6 C6,6 6,-2 6,-2z"
              fill="rgb(0,0,0)"
              fillOpacity="0"
            />
          </g>
        </g>
      </g>
      <g clipPath="url(#__lottie_element_20)" opacity="0" style={{ display: 'block' }} transform="matrix(1,0,0,1,5,14)">
        <g opacity="1" style={{ display: 'block' }} transform="matrix(0,1,-1,0,7,3)">
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M2,0 C3.103800058364868,0 4,0.8962000012397766 4,2 C4,3.103800058364868 3.103800058364868,4 2,4 C0.8962000012397766,4 0,3.103800058364868 0,2 C0,0.8962000012397766 0.8962000012397766,0 2,0z"
              fillOpacity="0"
              stroke="rgb(23,80,53)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="4"
            />
            <path
              d=" M2,0 C3.103800058364868,0 4,0.8962000012397766 4,2 C4,3.103800058364868 3.103800058364868,4 2,4 C0.8962000012397766,4 0,3.103800058364868 0,2 C0,0.8962000012397766 0.8962000012397766,0 2,0z"
              fill="rgb(29,225,133)"
              fillOpacity="1"
            />
          </g>
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M6,-2 C6,-2 -2,-2 -2,-2 C-2,-2 -2,6 -2,6 C-2,6 6,6 6,6 C6,6 6,-2 6,-2z"
              fill="rgb(0,0,0)"
              fillOpacity="0"
            />
          </g>
        </g>
      </g>
      <g
        clipPath="url(#__lottie_element_11)"
        opacity="0"
        style={{ display: 'block' }}
        transform="matrix(-0.7071067690849304,-0.7071067690849304,0.7071067690849304,-0.7071067690849304,25.36396026611328,32.435028076171875)"
      >
        <g opacity="1" style={{ display: 'block' }} transform="matrix(0,1,-1,0,7,3)">
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M2,0 C3.103800058364868,0 4,0.8962000012397766 4,2 C4,3.103800058364868 3.103800058364868,4 2,4 C0.8962000012397766,4 0,3.103800058364868 0,2 C0,0.8962000012397766 0.8962000012397766,0 2,0z"
              fillOpacity="0"
              stroke="rgb(23,80,53)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="4"
            />
            <path
              d=" M2,0 C3.103800058364868,0 4,0.8962000012397766 4,2 C4,3.103800058364868 3.103800058364868,4 2,4 C0.8962000012397766,4 0,3.103800058364868 0,2 C0,0.8962000012397766 0.8962000012397766,0 2,0z"
              fill="rgb(29,225,133)"
              fillOpacity="1"
            />
          </g>
          <g opacity="1" transform="matrix(1,0,0,1,0,0)">
            <path
              d=" M6,-2 C6,-2 -2,-2 -2,-2 C-2,-2 -2,6 -2,6 C-2,6 6,6 6,6 C6,6 6,-2 6,-2z"
              fill="rgb(0,0,0)"
              fillOpacity="0"
            />
          </g>
        </g>
      </g>
      <g
        opacity="1"
        style={{ display: 'block' }}
        transform="matrix(0.760405957698822,-0.649448037147522,0.649448037147522,0.760405957698822,19.871362686157227,16.940698623657227)"
      >
        <path
          d=" M16,1 C16,0.4481000006198883 15.551899909973145,0 15,0 C15,0 1,0 1,0 C0.4481000006198883,0 0,0.4481000006198883 0,1 C0,1 0,1 0,1 C0,1.551900029182434 0.4481000006198883,2 1,2 C1,2 15,2 15,2 C15.551899909973145,2 16,1.551900029182434 16,1 C16,1 16,1 16,1z"
          fill="rgb(29,225,133)"
          fillOpacity="1"
        />
      </g>
    </g>
  </svg>
);
