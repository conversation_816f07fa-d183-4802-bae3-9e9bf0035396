import type { SVGProps } from 'react';

export const FireworksAnimationLoading = (props: SVGProps<SVGSVGElement>) => (
  <svg
    height="403"
    preserveAspectRatio="xMidYMid meet"
    viewBox="0 0 460 460"
    width="403"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <defs>
      <clipPath id="__lottie_element_31">
        <rect height="460" width="460" x="0" y="0" />
      </clipPath>
      <clipPath id="__lottie_element_33">
        <path d="M0,0 L960,0 L960,960 L0,960z" />
      </clipPath>
      <mask id="__lottie_element_157">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_160">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_163">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_166">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_169">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_172">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_175">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_178">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_181">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_184">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_187">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_190">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_193">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_196">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_199">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_202">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
      <mask id="__lottie_element_205">
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#ffffff"
          fillOpacity="1"
        />
        <path
          clipRule="nonzero"
          d=" M33.3120002746582,-151.33599853515625 C12.777999877929688,-151.33599853515625 -3.867000102996826,-134.6909942626953 -3.867000102996826,-114.15699768066406 C-3.867000102996826,-93.62300109863281 12.777999877929688,-76.97699737548828 33.3120002746582,-76.97699737548828 C53.84600067138672,-76.97699737548828 70.49199676513672,-93.62300109863281 70.49199676513672,-114.15699768066406 C70.49199676513672,-134.6909942626953 53.84600067138672,-151.33599853515625 33.3120002746582,-151.33599853515625"
          fill="#000000"
          fillOpacity="1"
        />
      </mask>
    </defs>
    <g clipPath="url(#__lottie_element_31)">
      <g
        clipPath="url(#__lottie_element_33)"
        opacity="1"
        style={{ display: 'block' }}
        transform="matrix(0.4791699945926666,0,0,0.4791699945926666,-0.0016021728515625,-0.0016021728515625)"
      >
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_205)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01727294921875,520.041748046875)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,-45.71699905395508,98.50800323486328)">
            <path
              d=" M28.899999618530273,-92.1500015258789 C11.100000381469727,-65.3499984741211 -23,60.150001525878906 -28.899999618530273,92.1500015258789"
              fillOpacity="0"
              stroke="rgb(210,4,6)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="7"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_202)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01727294921875,520.0418090820312)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,190.38299560546875,47.757999420166016)">
            <path
              d=" M69.30000305175781,70.80000305175781 C58,40.599998474121094 -11,-59.20000076293945 -69.30000305175781,-70.80000305175781"
              fillOpacity="0"
              stroke="rgb(210,4,6)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="7"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_199)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.0172424316406,520.0425415039062)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,266.88299560546875,-196.54299926757812)">
            <path
              d=" M90.80000305175781,-39.5989990234375 C58.599998474121094,-39.79899978637695 -59.29999923706055,-10.699000358581543 -90.80000305175781,39.60100173950195"
              fillOpacity="0"
              stroke="rgb(210,4,6)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="7"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_196)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01727294921875,520.041748046875)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,-84.86699676513672,-252.8419952392578)">
            <path
              d=" M46.45000076293945,54.29999923706055 C20.950000762939453,7 -23.850000381469727,-39.5 -46.45000076293945,-54.29999923706055"
              fillOpacity="0"
              stroke="rgb(210,4,6)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="7"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_193)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01678466796875,520.0421752929688)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,-108.66000366210938,142.73699951171875)">
            <path
              d=" M65.84400177001953,-158.6790008544922 C68.24400329589844,-161.47900390625 67.24400329589844,-165.7790069580078 63.944000244140625,-167.1790008544922 C63.84400177001953,-167.2790069580078 63.64400100708008,-167.27999877929688 63.54399871826172,-167.3800048828125 C61.04399871826172,-168.3800048828125 58.64400100708008,-168.5800018310547 56.84400177001953,-168.3800048828125 C55.04399871826172,-168.27999877929688 53.64400100708008,-167.1790008544922 52.54399871826172,-165.6790008544922 C34.04399871826172,-140.57899475097656 12.244000434875488,-94.18000030517578 -4.75600004196167,-50.47999954223633 C-28.256000518798828,9.920000076293945 -52.45600128173828,86.12100219726562 -67.05599975585938,161.7209930419922 C-67.65599822998047,164.52099609375 -65.75599670410156,167.32000732421875 -62.95600128173828,168.02000427246094 C-60.95600128173828,168.52000427246094 -59.15599822998047,168.6199951171875 -56.95600128173828,168.1199951171875 C-54.75600051879883,167.6199951171875 -53.055999755859375,165.82000732421875 -52.65599822998047,163.6199951171875 C-34.35599899291992,59.119998931884766 31.444000244140625,-118.87899780273438 65.84400177001953,-158.6790008544922z"
              fill="rgb(255,218,99)"
              fillOpacity="1"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_190)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01690673828125,520.04248046875)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,-267.1960144042969,-277.57501220703125)">
            <path
              d=" M159.2790069580078,63.93199920654297 C162.07899475097656,66.33200073242188 166.37899780273438,65.33200073242188 167.7790069580078,61.93199920654297 C167.87899780273438,61.832000732421875 167.87899780273438,61.63199996948242 167.97900390625,61.53200149536133 C168.97900390625,59.03200149536133 169.07899475097656,56.63199996948242 168.97900390625,54.832000732421875 C168.87899780273438,53.03200149536133 167.6790008544922,51.63199996948242 166.2790069580078,50.63199996948242 C140.87899780273438,32.43199920654297 94.27899932861328,11.131999969482422 50.479000091552734,-5.368000030517578 C-10.121000289916992,-28.167999267578125 -86.72100067138672,-51.46799850463867 -162.42100524902344,-65.16799926757812 C-165.2209930419922,-65.66799926757812 -168.02099609375,-63.86800003051758 -168.62100219726562,-61.06800079345703 C-169.12100219726562,-59.06800079345703 -169.12100219726562,-57.268001556396484 -168.7209930419922,-55.06800079345703 C-168.2209930419922,-52.86800003051758 -166.42100524902344,-51.268001556396484 -164.12100219726562,-50.86800003051758 C-59.72100067138672,-33.768001556396484 118.97899627685547,29.93199920654297 159.2790069580078,63.93199920654297z"
              fill="rgb(255,218,99)"
              fillOpacity="1"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_187)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01611328125,520.0425415039062)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,283.6960144042969,77.96600341796875)">
            <path
              d=" M-163.91200256347656,-116.70899963378906 C-168.21200561523438,-116.40899658203125 -172.41200256347656,-121.30899810791016 -170.01199340820312,-124.90899658203125 C-169.91200256347656,-125.00900268554688 -169.81199645996094,-125.20899963378906 -169.71200561523438,-125.30899810791016 C-167.81199645996094,-127.90899658203125 -165.61199951171875,-131.50900268554688 -157.81199645996094,-130.70899963378906 C-122.91200256347656,-127.00900268554688 -66.61199951171875,-104.90799713134766 -22.011999130249023,-72.50800323486328 C39.6879997253418,-27.607999801635742 98.48699951171875,38.09199905395508 168.98699951171875,117.69200134277344 C171.28700256347656,120.29199981689453 171.28700256347656,124.09200286865234 168.98699951171875,126.49199676513672 C167.28700256347656,128.19200134277344 165.68800354003906,129.49200439453125 163.18800354003906,130.39199829101562 C160.68800354003906,131.39199829101562 157.88800048828125,130.5919952392578 156.08799743652344,128.5919952392578 C115.98799896240234,83.39199829101562 45.987998962402344,-4.208000183105469 -23.11199951171875,-55.507999420166016 C-74.91200256347656,-93.80799865722656 -131.61199951171875,-119.30899810791016 -163.91200256347656,-116.70899963378906z"
              fill="rgb(255,218,99)"
              fillOpacity="1"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_184)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01702880859375,520.042724609375)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,245.8820037841797,-333.5509948730469)">
            <path
              d=" M-121.39900207519531,131.60800170898438 C-122.9990005493164,135.10800170898438 -127.4990005493164,136.10800170898438 -130.2989959716797,133.50799560546875 C-130.3990020751953,133.4080047607422 -130.49899291992188,133.30799865722656 -130.5989990234375,133.20799255371094 C-132.69900512695312,131.20799255371094 -133.7989959716797,129.10800170898438 -134.49899291992188,127.30799865722656 C-135.19900512695312,125.60800170898438 -134.69900512695312,123.70800018310547 -133.7989959716797,122.10800170898438 C-117.9990005493164,93.60800170898438 -83.5989990234375,52.70800018310547 -49.5989990234375,17.607999801635742 C-2.5989999771118164,-30.892000198364258 59.10100173950195,-87.09200286865234 124.3010025024414,-133.89199829101562 C126.8010025024414,-135.69200134277344 130.2010040283203,-135.19200134277344 132.00100708007812,-132.79200744628906 C133.30099487304688,-131.0919952392578 134.2010040283203,-129.49200439453125 134.7010040283203,-127.19200134277344 C135.2010040283203,-124.89199829101562 134.2010040283203,-122.49199676513672 132.30099487304688,-121.19200134277344 C41.4010009765625,-58.29199981689453 -98.5989990234375,81.70800018310547 -121.39900207519531,131.60800170898438z"
              fill="rgb(255,218,99)"
              fillOpacity="1"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_181)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01727294921875,520.041748046875)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,274.5329895019531,-240.69200134277344)">
            <path
              d=" M125.3499984741211,-77.25 C49.75,-56.150001525878906 -67.6500015258789,4.550000190734863 -125.3499984741211,77.25"
              fillOpacity="0"
              stroke="rgb(232,151,3)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="4"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_178)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.0172424316406,520.041748046875)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,-57.46699905395508,-306.4419860839844)">
            <path
              d=" M44.150001525878906,92.69999694824219 C21.450000762939453,8.699999809265137 8.649999618530273,-20.100000381469727 -44.150001525878906,-92.69999694824219"
              fillOpacity="0"
              stroke="rgb(232,151,3)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="4"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_175)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.0172424316406,520.041748046875)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,-155.56700134277344,0.1080000028014183)">
            <path
              d=" M80.8499984741211,-63.25 C46.95000076293945,-48.349998474121094 -40.25,18.649999618530273 -80.8499984741211,63.25"
              fillOpacity="0"
              stroke="rgb(232,151,3)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="4"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_172)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01727294921875,520.041748046875)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,128.58299255371094,67.80799865722656)">
            <path
              d=" M53.099998474121094,85.8499984741211 C42.20000076293945,52.95000076293945 -2.5,-42.04999923706055 -53.099998474121094,-85.8499984741211"
              fillOpacity="0"
              stroke="rgb(232,151,3)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="4"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_169)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.0172424316406,520.041748046875)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,69.63300323486328,105.70800018310547)">
            <path
              d=" M24.75,87.55000305175781 C24.75,47.95000076293945 19.149999618530273,-42.650001525878906 -24.75,-87.55000305175781"
              fillOpacity="0"
              stroke="rgb(9,116,164)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="3"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_166)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01727294921875,520.0422973632812)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,322.38299560546875,-94.47599792480469)">
            <path
              d=" M146.3000030517578,14.734000205993652 C110,-3.865999937057495 -49.400001525878906,-22.666000366210938 -146.3000030517578,-11.265999794006348"
              fillOpacity="0"
              stroke="rgb(9,116,164)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="3"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_163)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.0172424316406,520.0424194335938)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,-238.7169952392578,-142.38900756835938)">
            <path
              d=" M-146.8000030517578,9.246999740600586 C-109.5999984741211,-7.353000164031982 50.70000076293945,-17.35300064086914 146.8000030517578,-0.652999997138977"
              fillOpacity="0"
              stroke="rgb(9,116,164)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="3"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_160)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01727294921875,520.041748046875)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,-24.816999435424805,-347.8919982910156)">
            <path
              d=" M-37.099998474121094,-101.25 C-11.899999618530273,-62.849998474121094 14.800000190734863,16.149999618530273 37.099998474121094,101.25"
              fillOpacity="0"
              stroke="rgb(9,116,164)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="3"
            />
          </g>
        </g>
        <g
          mask="url(#__lottie_element_157)"
          opacity="1"
          style={{ display: 'block' }}
          transform="matrix(0.7799999713897705,0,0,0.7799999713897705,473.01727294921875,520.041748046875)"
        >
          <g opacity="1" transform="matrix(1,0,0,1,138.68299865722656,-327.5920104980469)">
            <path
              d=" M50.400001525878906,-97.3499984741211 C20.600000381469727,-53.349998474121094 -19,21.649999618530273 -50.400001525878906,97.3499984741211"
              fillOpacity="0"
              stroke="rgb(9,116,164)"
              strokeLinecap="butt"
              strokeLinejoin="miter"
              strokeMiterlimit="10"
              strokeOpacity="1"
              strokeWidth="3"
            />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
        <g style={{ display: 'none' }}>
          <g>
            <path />
          </g>
        </g>
      </g>
    </g>
  </svg>
);
