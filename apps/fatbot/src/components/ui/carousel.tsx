/* eslint-disable no-magic-numbers */
'use client';

import { ArrowLeftIcon, ArrowRightIcon } from '@radix-ui/react-icons';
import type { EmblaCarouselType, EmblaEventType } from 'embla-carousel';
import useEmblaCarousel, { type UseEmblaCarouselType } from 'embla-carousel-react';
import {
  type ComponentProps,
  createContext,
  type FC,
  type HTMLAttributes,
  type KeyboardEvent,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';

import { cn } from '@/lib/utils';

import { Button } from './button';

type CarouselApi = UseEmblaCarouselType[1];
type UseCarouselParameters = Parameters<typeof useEmblaCarousel>;
type CarouselOptions = UseCarouselParameters[0];
type CarouselPlugin = UseCarouselParameters[1];

interface CarouselProps {
  opts?: CarouselOptions;
  plugins?: CarouselPlugin;
  orientation?: 'horizontal' | 'vertical';
  setApi?: (api: CarouselApi) => void;
  onSelectItemIndex?: (current: number) => void;
  scale?: number;
}

type CarouselContextProps = CarouselProps & {
  carouselRef: ReturnType<typeof useEmblaCarousel>[0];
  api: ReturnType<typeof useEmblaCarousel>[1];
  scrollPrev: () => void;
  scrollNext: () => void;
  canScrollPrev: boolean;
  canScrollNext: boolean;
};

const CarouselContext = createContext<CarouselContextProps | null>(null);

function useCarousel() {
  const context = useContext(CarouselContext);

  if (!context) {
    throw new Error('useCarousel must be used within a <Carousel />');
  }

  return context;
}

const numberWithinRange = (number: number, min: number, max: number): number => Math.min(Math.max(number, min), max);

const Carousel = ({
  orientation = 'horizontal',
  opts,
  setApi,
  plugins,
  className,
  children,
  onSelectItemIndex,
  scale = 1,
  ...props
}: CarouselProps & HTMLAttributes<HTMLDivElement>) => {
  const [carouselRef, api] = useEmblaCarousel(
    {
      ...opts,
      axis: orientation === 'horizontal' ? 'x' : 'y',
    },
    plugins,
  );
  const [canScrollPrevious, setCanScrollPrevious] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const tweenFactor = useRef(0);
  const tweenNodes = useRef<HTMLElement[]>([]);

  const setTweenNodes = useCallback((emblaApi: EmblaCarouselType): void => {
    tweenNodes.current = emblaApi.slideNodes().map((slideNode) => slideNode.firstElementChild as HTMLElement);
  }, []);

  const setTweenFactor = useCallback(
    (emblaApi: EmblaCarouselType) => {
      tweenFactor.current = scale * emblaApi.scrollSnapList().length;
    },
    [scale],
  );

  const tweenScale = useCallback((emblaApi: EmblaCarouselType, eventName?: EmblaEventType) => {
    const engine = emblaApi.internalEngine();
    const scrollProgress = emblaApi.scrollProgress();
    const slidesInView = emblaApi.slidesInView();
    const isScrollEvent = eventName === 'scroll';

    emblaApi.scrollSnapList().forEach((scrollSnap, snapIndex) => {
      let diffToTarget = scrollSnap - scrollProgress;
      const slidesInSnap = engine.slideRegistry[snapIndex];

      slidesInSnap?.forEach((slideIndex) => {
        if (isScrollEvent && !slidesInView.includes(slideIndex)) {
          return;
        }

        if (engine.options.loop) {
          engine.slideLooper.loopPoints.forEach((loopItem) => {
            const target = loopItem.target();

            if (slideIndex === loopItem.index && target !== 0) {
              const sign = Math.sign(target);

              if (sign === -1) {
                diffToTarget = scrollSnap - (1 + scrollProgress);
              }
              if (sign === 1) {
                diffToTarget = scrollSnap + (1 - scrollProgress);
              }
            }
          });
        }

        const tweenValue = 1 - Math.abs(diffToTarget * tweenFactor.current);
        const scale = numberWithinRange(tweenValue, 0, 1);
        const tweenNode = tweenNodes.current[slideIndex];

        if (tweenNode) {
          tweenNode.style.transform = `scale(${scale})`;
          tweenNode.style.opacity = scale.toString();
          tweenNode.style.translate = `${-diffToTarget * tweenFactor.current * (90 + (0.8 - scale) * 100)}%`;
        }
      });
    });
  }, []);

  const onSelect = useCallback(
    (api: CarouselApi) => {
      if (!api) {
        return;
      }
      onSelectItemIndex?.(api.selectedScrollSnap());
      setCanScrollPrevious(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    },
    [onSelectItemIndex],
  );

  const scrollPrevious = useCallback(() => {
    api?.scrollPrev();
  }, [api]);

  const scrollNext = useCallback(() => {
    api?.scrollNext();
  }, [api]);

  const handleKeyDown = useCallback(
    (event: KeyboardEvent<HTMLDivElement>) => {
      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        scrollPrevious();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        scrollNext();
      }
    },
    [scrollPrevious, scrollNext],
  );

  useEffect(() => {
    if (!api || !setApi) {
      return;
    }

    setApi(api);
  }, [api, setApi]);

  useEffect(() => {
    if (!api) {
      return;
    }

    api.on('reInit', onSelect);
    api.on('select', onSelect);

    setTweenNodes(api);
    setTweenFactor(api);
    tweenScale(api);
    onSelect(api);

    api
      .on('reInit', setTweenNodes)
      .on('reInit', setTweenFactor)
      .on('reInit', tweenScale)
      .on('scroll', tweenScale)
      .on('slideFocus', tweenScale);

    return () => {
      api.off('select', onSelect);
      api.off('scroll', tweenScale);
      api.off('slideFocus', tweenScale);
    };
  }, [scale, api, onSelect, setTweenFactor, setTweenNodes, tweenScale]);

  return (
    <CarouselContext.Provider
      value={{
        carouselRef,
        api: api,
        opts,
        orientation,
        scrollPrev: scrollPrevious,
        scrollNext,
        canScrollPrev: canScrollPrevious,
        canScrollNext,
      }}
    >
      <div
        aria-roledescription="carousel"
        className={cn('relative', className)}
        role="region"
        onKeyDownCapture={handleKeyDown}
        {...props}
      >
        {children}
      </div>
    </CarouselContext.Provider>
  );
};
Carousel.displayName = 'Carousel';

const CarouselContent = ({ className, ...props }: HTMLAttributes<HTMLDivElement>) => {
  const { carouselRef, orientation } = useCarousel();

  return (
    <div ref={carouselRef} className="overflow-hidden">
      <div className={cn('flex', orientation === 'horizontal' ? '-ml-4' : '-mt-4 flex-col', className)} {...props} />
    </div>
  );
};
CarouselContent.displayName = 'CarouselContent';

const CarouselItem = ({ className, ...props }: HTMLAttributes<HTMLDivElement>) => {
  const { orientation } = useCarousel();

  return (
    <div
      aria-roledescription="slide"
      className={cn('min-w-0 shrink-0 grow-0 basis-full', orientation === 'horizontal' ? 'pl-4' : 'pt-4', className)}
      role="group"
      {...props}
    />
  );
};
CarouselItem.displayName = 'CarouselItem';

const CarouselPrevious = ({
  className,
  variant = 'outline',
  size = 'icon',
  ...props
}: ComponentProps<typeof Button>) => {
  const { scrollPrev, canScrollPrev } = useCarousel();

  return (
    <Button
      className={cn('absolute size-7 w-9 rounded-full', className)}
      disabled={!canScrollPrev}
      size={size}
      variant={variant}
      onClick={scrollPrev}
      {...props}
    >
      <ArrowLeftIcon className="size-3" />
      <span className="sr-only">Previous slide</span>
    </Button>
  );
};
CarouselPrevious.displayName = 'CarouselPrevious';

const CarouselNext = ({ className, variant = 'outline', size = 'icon', ...props }: ComponentProps<typeof Button>) => {
  const { scrollNext, canScrollNext } = useCarousel();

  return (
    <Button
      className={cn('absolute size-7 w-9 rounded-full', className)}
      disabled={!canScrollNext}
      size={size}
      variant={variant}
      onClick={scrollNext}
      {...props}
    >
      <ArrowRightIcon className="size-3" />
      <span className="sr-only">Next slide</span>
    </Button>
  );
};
CarouselNext.displayName = 'CarouselNext';

interface CarouselIndexIndicatorProps {
  readonly isSelected: boolean;
  readonly className?: string;
}

const CarouselIndexIndicator: FC<CarouselIndexIndicatorProps> = ({ isSelected, className }) => (
  <div
    className={cn(
      'size-1 rounded-full bg-white/10 transition-colors',
      {
        'bg-primary': isSelected,
      },
      className,
    )}
  />
);
CarouselIndexIndicator.displayName = 'CarouselIndexIndicator';

export {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselIndexIndicator,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
};
