'use client';

import type { StaticImport } from 'next/dist/shared/lib/get-img-props';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation'

import profile from '@/assets/profile.svg';
import { ROUTES } from '@/constants/routes';
import { cn } from '@/lib/utils';

export const ProfileLink = ({ className }: { className?: string }) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const params = new URLSearchParams(searchParams);
  params.set('redirectUrl', pathname);

  return (
    <Link
      className={cn('shrink-0 select-none', className)}
      data-testid="user-profile-icon"
      href={`${ROUTES.PROFILE.ROOT}?${params.toString()}`}
    >
      <Image alt="Logo" className="m-auto w-5" src={profile as StaticImport} />
    </Link>
  );
};
