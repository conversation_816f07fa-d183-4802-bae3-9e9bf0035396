import { type FC } from 'react';

import { Input } from '@/components/form/input';
import { cn } from '@/lib/utils';

interface Props {
  displayValue: string;
  placeholder?: string;
  className?: string;
  onFocus: () => void;
}

const onInfinityChange = () => {};

export const InfiniteValueInput: FC<Props> = ({ displayValue, placeholder, className, onFocus }) => (
  <Input
    className={cn('font-semibold', className)}
    placeholder={placeholder}
    value={displayValue}
    onChange={onInfinityChange}
    onFocus={onFocus}
  />
);
