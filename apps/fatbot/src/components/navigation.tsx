'use client';

import Link, { LinkProps } from 'next/link';
import { type FC, type ReactNode, useMemo } from 'react';

import { ROUTES } from '@/constants/routes';
import { cn } from '@/lib/utils';
import { usePathname } from 'next/navigation';

const disabledRoutes = [ROUTES.PROFILE.REFERRAL.INVITE];

interface Menu {
  href: string;
  text: string;
  icon: ReactNode;
  checkActive?: (href: string) => boolean;
}
type NavigationItemProps = LinkProps & Menu;

export const NavigationItem = ({ href, icon, text, checkActive }: NavigationItemProps) => {
  const pathname = usePathname();

  const isCurrentPath = useMemo(() => {
    if (checkActive) {
      return checkActive(pathname);
    }
    if (href === ROUTES.HOME) {
      return pathname === href;
    }

    const hrefWithoutSearchParams = href.split('?')[0];
    return pathname.includes(hrefWithoutSearchParams ?? '');
  }, [checkActive, href, pathname]);

  return (
    <Link
      className={cn(
        'flex w-fit flex-col items-center gap-0.25 font-semibold text-text-secondary transition-colors hover:text-primary',
        isCurrentPath && 'font-bold text-primary',
      )}
      data-onboarding-clickable="true"
      href={href}
    >
      {icon}
      <span className="text-nowrap text-xxs sm:text-xs">{text}</span>
    </Link>
  );
};

interface NavigationProps {
  menu: Menu[];
  className?: string;
}

export const Navigation: FC<NavigationProps> = ({ menu, className }) => {
  const pathname = usePathname();

  const isNavigationHidden = disabledRoutes.some((route) => pathname.includes(route));

  return (
    <div
      className={cn(
        'fixed inset-x-0 bottom-4 z-40 mx-auto flex justify-center px-3 sm:px-5 md:static',
        isNavigationHidden && 'hidden',
        className,
      )}
    >
      <nav className="flex w-fit items-center gap-4 rounded-full bg-surface-elevated px-4 py-1.5 shadow-primary lg:gap-5">
        {menu.map((item) => (
          <NavigationItem key={item.href} {...item} />
        ))}
      </nav>
    </div>
  );
};
