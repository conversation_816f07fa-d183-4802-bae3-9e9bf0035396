import type { FC } from 'react';

import { cn } from '@/lib/utils';

export interface Props {
  steps: string[];
  currentStepIndex: number;
  containerClassName?: string;
  stepIndicatorClassName?: string;
}

export const StepCounter: FC<Props> = ({ steps, currentStepIndex, containerClassName, stepIndicatorClassName }) => (
  <div className={cn('flex w-full items-center justify-center gap-2', containerClassName)}>
    {steps.map((step, index) => (
      <div
        key={step}
        className={cn(
          'size-1 rounded-full bg-surface-subtle',
          {
            ['bg-surface-active']: index <= currentStepIndex,
          },
          stepIndicatorClassName,
        )}
      />
    ))}
  </div>
);
