import { getRequestConfig } from 'next-intl/server';

export default getRequestConfig(async () => {
  // TODO: Read locale from cookie when B<PERSON> is ready
  // https://cleevio.atlassian.net/browse/CXFB-2199
  const locale = 'en';

  console.log('--request--');



  return {
    locale,
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
    messages: (await import(`../../messages/${locale}.json`)).default,
  };
});
