import { type NextRequest, NextResponse } from 'next/server';
import { authMiddleware, redirectToLogin } from 'next-firebase-auth-edge';

import { HIDE_ONBOARDING_COOKIE_NAME, REFERRAL_CODE_COOKIE_NAME } from '@/constants/config';
import { ROUTES } from '@/constants/routes';
import { authConfig } from '@/lib/firebase/server-config';
import { getFirebaseModeRedirect } from '@/module/auth/get-firebase-mode-redirect';

export const PUBLIC_PATHS = [
  ROUTES.TERMS_AND_CONDITIONS,
  ROUTES.PRIVACY_POLICY,
  ROUTES.RESET_PASSWORD,
  ROUTES.VERIFY_EMAIL,
  ROUTES.SIGN_UP,
  ROUTES.BOT_TRADING.ROOT,
  new RegExp(`^${ROUTES.MANUAL_TRADING.ROOT}(/.*)?$`),
  ROUTES.HOME,
  ROUTES.HOT_TOKENS.ROOT,
];

export default async function middleware(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  // Check action URL generated by firebase and determine where to go
  // e.g. verify-email, reset-password etc.
  const mode = searchParams.get('mode');
  const user = searchParams.get('user');
  const oobCode = searchParams.get('oobCode');
  const referralCode = searchParams.get(REFERRAL_CODE_COOKIE_NAME);

  if (referralCode) {
    const redirectUrl = new URL(request.url);
    redirectUrl.searchParams.delete(REFERRAL_CODE_COOKIE_NAME);
    const response = NextResponse.redirect(redirectUrl);

    response.cookies.set(REFERRAL_CODE_COOKIE_NAME, referralCode);

    return response;
  }

  if (mode) {
    const redirectUrl = new URL(getFirebaseModeRedirect(mode), request.url);

    redirectUrl.searchParams.set('oobCode', oobCode ?? '');
    redirectUrl.searchParams.set('user', user ?? '');

    return NextResponse.redirect(redirectUrl);
  }

  return authMiddleware(request, {
    loginPath: '/api/login',
    logoutPath: '/api/logout',
    refreshTokenPath: '/api/refresh-token',
    debug: authConfig.debug,
    enableMultipleCookies: authConfig.enableMultipleCookies,
    apiKey: authConfig.apiKey,
    cookieName: authConfig.cookieName,
    cookieSerializeOptions: authConfig.cookieSerializeOptions,
    cookieSignatureKeys: authConfig.cookieSignatureKeys,
    serviceAccount: authConfig.serviceAccount,
    experimental_enableTokenRefreshOnExpiredKidHeader: authConfig.experimental_enableTokenRefreshOnExpiredKidHeader,
    // eslint-disable-next-line @typescript-eslint/require-await -- Needs promise, but callback is not thenable
    handleInvalidToken: async () => {
      const hideOnboardingCookie = request.cookies.get(HIDE_ONBOARDING_COOKIE_NAME)?.value;
      const hideOnboarding = String(hideOnboardingCookie).toLowerCase() === 'true';

      if (hideOnboarding) {
        return redirectToLogin(request, {
          path: ROUTES.SIGN_UP,
          publicPaths: PUBLIC_PATHS,
        });
      }

      return redirectToLogin(request, {
        path: ROUTES.SIGN_UP,
        publicPaths: PUBLIC_PATHS,
      });
    },
    // eslint-disable-next-line @typescript-eslint/require-await -- Needs promise, but callback is not thenable
    handleError: async () =>
      redirectToLogin(request, {
        path: ROUTES.SIGN_UP,
        publicPaths: PUBLIC_PATHS,
      }),
  });
}

export const config = {
  matcher: ['/', '/((?!_next|api|.*\\.).*)', '/api/login', '/api/logout', '/api/refresh-token'],
};
