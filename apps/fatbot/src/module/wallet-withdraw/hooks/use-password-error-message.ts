import { useTranslations } from 'next-intl';

export const usePasswordErrorMessage = () => {
  const t = useTranslations('auth-error-messages');

  const errorMap = {
    'invalid password.': t('invalid-password'),
    'too many attempts.': t('too-many-attempts'),
    'auth/invalid-credential': t('wrong-password'),
    'auth/too-many-requests': t('too-many-attempts'),
  };

  const getErrorMessage = (error: string) => errorMap[error.toLowerCase() as keyof typeof errorMap];

  return { getErrorMessage };
};
