'use client';

import BigNumber from 'bignumber.js';
import Link from 'next/link';
import { notFound, useSearchParams , usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { Coins, EditIcon, ExternalLinkIcon, Key, LinkBroken, TrendUp } from '@/assets';
import { Menu } from '@/components/menu/menu';
import { MenuItem } from '@/components/menu/menu-item';
import { Trend } from '@/components/trend';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import Display from '@/components/ui/display';
import WalletAddressCard from '@/components/wallet-address-card';
import { ROUTES } from '@/constants/routes';
import { useGetUserWallet } from '@/lib/api';
import { formatUsd } from '@/lib/formatters/format-usd';
import { ChainIcon } from '@/module/multichain/chain-icon';
import { WalletDetailSkeleton } from '@/module/wallet-detail/wallet-detail-skeleton';
import { WalletDisconnectDialog } from '@/module/wallet-detail/wallet-disconnect-dialog';
import { WalletEditDialog } from '@/module/wallet-detail/wallet-edit-dialog';
import { WalletPrivateKeyDialog } from '@/module/wallet-detail/wallet-private-key-dialog';
import { SELECTED_TAB_QUERY } from '@/module/wallets/use-tabs-literal';
import { WalletCardValue } from '@/module/wallets/wallet-card-value';

import { SingleWalletDeposit } from './single-wallet-deposit';
import { ToggleDefaultWallet } from './toggle-default-wallet';

interface Props {
  walletId: string;
}

export const WalletDetailContent = ({ walletId }: Props) => {
  const t = useTranslations('wallets');
  const tCommon = useTranslations('common');
  const pathname = usePathname();

  const selectedChain = useSearchParams().get(SELECTED_TAB_QUERY) ?? '';

  const { data, isLoading } = useGetUserWallet(walletId, {
    query: { staleTime: 0 },
  });

  if (isLoading) {
    return <WalletDetailSkeleton />;
  }

  if (!data) {
    return notFound();
  }

  const {
    chain,
    isDefault,
    customName,
    currentPortfolioValueChangeUsd,
    currentPortfolioValueChangeFraction,
    currentPortfolioValueUsd,
    portfolioOneDayChangeAmountUsd,
    portfolioOneDayChangeFraction,
    walletAddress,
    walletDetailUri,
  } = data;

  // is the wallet is empty, we disable the withdraw button
  const withdrawButtonProps = Number(currentPortfolioValueUsd) === 0 ? { disabled: true } : { asChild: true };

  return (
    <Card className="mx-auto mb-20 max-w-2xl gap-5 md:mb-0">
      <div className="flex flex-col">
        <div className="flex w-full items-center">
          <div className="flex w-full items-center gap-0">
            <div className="flex items-center gap-1 self-start text-text-secondary">
              <ChainIcon chain={data.chain} className="size-3 " />
              <span className="text-md font-medium">{data.customName}</span>
              {isDefault ? (
                <Badge size="medium" variant="warning">
                  {t('main')}
                </Badge>
              ) : null}
            </div>
            <WalletDisconnectDialog chain={chain} walletId={walletId}>
              <Button className="ml-auto" size="icon" variant="ghost">
                <LinkBroken className="size-3 text-text-brand-1" />
              </Button>
            </WalletDisconnectDialog>
            <Button size="icon" variant="ghost">
              <Link href={walletDetailUri} rel="noopener noreferrer" target="_blank">
                <ExternalLinkIcon className="size-3 text-text-brand-1" />
              </Link>
            </Button>

            <WalletEditDialog walletId={walletId} walletName={customName}>
              <Button size="icon" variant="ghost">
                <EditIcon className="size-3 text-text-brand-1" />
              </Button>
            </WalletEditDialog>
          </div>
        </div>
        <div>
          <Display size="L" weight="bold">
            {formatUsd(currentPortfolioValueUsd)}
          </Display>
          <WalletCardValue walletId={walletId} />
        </div>
      </div>

      <div className="flex w-full flex-col gap-1 sm:flex-row">
        <Card className="flex flex-1 flex-col gap-1 shadow-primary" variant="primary">
          <p className="text-sm font-medium text-text-secondary">
            {new BigNumber(currentPortfolioValueChangeUsd).gte(0) ? t('total-profit') : tCommon('total-loss')}
          </p>
          <div className="flex items-center justify-between gap-1">
            <Display size="XS" weight="bold">
              {formatUsd(currentPortfolioValueChangeUsd)}
            </Display>
          </div>
          <Trend badgeProps={{ size: 'small' }} value={currentPortfolioValueChangeFraction} />
        </Card>
        <Card className="flex flex-1 flex-col gap-1 shadow-primary" variant="primary">
          <p className="text-sm font-medium text-text-secondary">{t('total-1d-change')}</p>
          <div className="flex items-center justify-between gap-1">
            <Display size="XS" weight="bold">
              {formatUsd(portfolioOneDayChangeAmountUsd)}
            </Display>
          </div>
          <Trend badgeProps={{ size: 'small' }} value={portfolioOneDayChangeFraction} />
        </Card>
      </div>

      <WalletAddressCard address={walletAddress} />

      <Menu>
        <WalletPrivateKeyDialog walletId={walletId}>
          <MenuItem bordered icon={<Key className="size-3" />} label={t('show-private-key')} />
        </WalletPrivateKeyDialog>
        <MenuItem
          bordered
          href={ROUTES.LAST_TRANSACTIONS.path(
            walletId,
            ROUTES.WALLETS.DETAIL(walletId, selectedChain, ROUTES.WALLETS.ROOT),
          )}
          icon={<TrendUp className="size-3" />}
          label={t('transactions')}
        />
        <MenuItem
          bordered
          href={ROUTES.MY_ASSETS.path(walletId, ROUTES.WALLETS.DETAIL(walletId, selectedChain, ROUTES.WALLETS.ROOT))}
          icon={<Coins className="size-3" />}
          label={t('my-tokens')}
        />
        <ToggleDefaultWallet chain={chain} isDefault={isDefault} walletId={walletId} />
      </Menu>

      <div className="mt-5 flex flex-col gap-2 sm:mt-0 sm:flex-row">
        { }
        <Button className="flex-1" variant="outline" {...withdrawButtonProps}>
          <Link href={ROUTES.WALLETS.WITHDRAW(walletId, ROUTES.WALLETS.WITHDRAW(walletId, pathname))}>
            {t('withdraw')}
          </Link>
        </Button>
        <SingleWalletDeposit
          wallet={{
            ...data,
            isDefault,
            walletId: data.id,
            walletDetailUrl: data.walletDetailUri,
          }}
          walletAddress={walletAddress}
        >
          <Button className="flex-1">{t('deposit')}</Button>
        </SingleWalletDeposit>
      </div>
    </Card>
  );
};
