import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { logout } from '@/api';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { getErrorMessage } from '@/lib/get-error-message';

export const usePasswordSchema = () => {
  const t = useTranslations('sign-in.sign-up.form');

  return z.object({
    password: z.string().min(1, { message: t('password-required') }),
  });
};

type PasswordFormSchema = z.infer<ReturnType<typeof usePasswordSchema>>;

export const usePasswordForm = (onPasswordEntered: (password: string) => Promise<void>) => {
  const t = useTranslations('auth-error-messages');
  const queryClient = useQueryClient();
  const { push } = useRouter();
  const schema = usePasswordSchema();
  const form = useForm<PasswordFormSchema>({
    resolver: zodResolver(schema),
    defaultValues: {
      password: '',
    },
  });

  const onSubmit = async (data: PasswordFormSchema) => {
    try {
      await onPasswordEntered(data.password);
    } catch (error) {
      form.setError('password', {
        type: 'manual',
        message: getErrorMessage(error) ?? t('wrong-password'),
      });
    }
  };

  const onResetPassword = async () => {
    await logout();
    queryClient.clear();
    push(ROUTES.RESET_PASSWORD);
  };

  return {
    form,
    onSubmit,
    onResetPassword,
  };
};
