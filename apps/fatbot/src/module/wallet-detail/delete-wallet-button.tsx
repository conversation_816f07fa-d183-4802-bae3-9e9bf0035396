'use client';

import { useTranslations } from 'next-intl';
import { useTransition } from 'react';

import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/tooltip';
import { LoadingButton } from '@/components/ui/loading-button';
import { ROUTES } from '@/constants/routes';
import { useEphemeralValue } from '@/hooks/use-ephemeral-value';
import { useRouter } from 'next/navigation';
import { type Chain, getGetAllUserWalletsV3QueryKey, useDeleteWallet } from '@/lib/api';
import { getErrorMessage } from '@/lib/get-error-message';
import { getQueryClient } from '@/lib/query-client';
import { SELECTED_TAB_QUERY } from '@/module/wallets/use-tabs-literal';

interface Props {
  walletId: string;
  chain: Chain;
}

export const DeleteWalletButton = ({ walletId, chain }: Props) => {
  const t = useTranslations('wallets');
  const [pending, startTransition] = useTransition();
  const { push } = useRouter();
  const queryClient = getQueryClient();

  const deleteWallet = useDeleteWallet({
    mutation: {
      onSuccess: async () => {
        await queryClient.invalidateQueries({
          queryKey: getGetAllUserWalletsV3QueryKey({
            useSelectedChains: false,
          }),
        });
      },
    },
  });

  const [tooltip, setTooltip] = useEphemeralValue<string>();

  const handleClick = () => {
    startTransition(async () => {
      try {
        await deleteWallet.mutateAsync({ walletId });

        setTooltip(t('delete-success'));
        push(`${ROUTES.WALLETS.ROOT}?${SELECTED_TAB_QUERY}=${chain}`);
      } catch (error) {
        setTooltip(getErrorMessage(error) ?? '');
      }
    });
  };

  return (
    <Tooltip open={!!tooltip}>
      <TooltipTrigger asChild>
        <LoadingButton className="w-full" isLoading={pending} variant="destructive" onClick={handleClick}>
          {t('disconnect')}
        </LoadingButton>
      </TooltipTrigger>
      <TooltipContent>{tooltip}</TooltipContent>
    </Tooltip>
  );
};
