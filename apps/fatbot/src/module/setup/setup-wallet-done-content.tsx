'use client';

import { AnimatePresence, motion } from 'motion/react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';

import { WalletSuccessAnimation } from '@/components/animations/wallet-success';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@/components/ui/dialog';
import Display from '@/components/ui/display';
import WalletAddressCard from '@/components/wallet-address-card';
import { SHOW_QR_DELAY } from '@/constants/config';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { useGetAllUserWalletsV3 } from '@/lib/api';
import { WalletDepositInfo } from '@/module/wallet-detail/wallet-deposit-info/wallet-deposit-info';
import { SELECTED_TAB_QUERY } from '@/module/wallets/use-tabs-literal';

export const SetupWalletDoneContent = ({
  type,
  walletAddress,
}: {
  type: 'create' | 'import';
  walletAddress: string;
}) => {
  const selectedChain = useSearchParams().get(SELECTED_TAB_QUERY) ?? '';
  const [showQR, setShowQR] = useState(false);

  const { data: wallets } = useGetAllUserWalletsV3({
    useSelectedChains: false,
  });

  useEffect(() => {
    setTimeout(() => {
      setShowQR(true);
    }, SHOW_QR_DELAY);
  }, []);

  const selectedWallet = wallets?.find(
    (wallet) =>
      wallet.walletAddress === walletAddress && wallet.chain === selectedChain
  );

  const router = useRouter();
  const t = useTranslations();

  const redirectUrl = `${ROUTES.WALLETS.ROOT}?${SELECTED_TAB_QUERY}=${selectedChain}`;

  const onOpenChange = () => {
    router.replace(redirectUrl);
  };

  return (
    <Dialog open onOpenChange={onOpenChange}>
      <DialogContent invert>
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />
        <div className="flex w-full flex-col gap-2 transition-all duration-300 ease-in-out sm:mt-0 sm:gap-4 ">
          <AnimatePresence mode="wait">
            {showQR ? (
              <motion.div
                key="content1"
                animate={{ opacity: 1 }}
                className="w-full"
                exit={{ opacity: 0 }}
                initial={{ opacity: 0 }}
                layout
                transition={{ duration: 0.3 }}
              >
                <WalletDepositInfo
                  wallet={selectedWallet}
                  walletAddress={walletAddress}
                />
              </motion.div>
            ) : (
              <motion.div
                key="content2"
                className="flex w-full flex-col gap-4"
                exit={{ opacity: 0 }}
                layout
                transition={{ duration: 0.3 }}
              >
                <Display
                  className="text-balance text-center text-3xl text-text-invert"
                  size="S"
                  weight="bold"
                >
                  {t(`setup-wallet.done.title-${type}`)}
                </Display>
                <WalletSuccessAnimation />
              </motion.div>
            )}
          </AnimatePresence>
          <div className="flex w-full flex-col gap-3">
            {walletAddress ? <WalletAddressCard
                address={walletAddress}
                className="py-1"
                invert
                label={
                  showQR
                    ? t('setup-wallet.deposit.copy-address')
                    : t('setup-wallet.done.copy-address')
                }
              /> : null}
            <Button asChild className="w-full" variant="invert">
              <Link href={redirectUrl || ROUTES.HOME}>
                {t('setup-wallet.continue')}
              </Link>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
