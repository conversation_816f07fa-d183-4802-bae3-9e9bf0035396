import { useTranslations } from 'next-intl';

import { CloseableAlert } from '@/components/closeable-alert';
import { ROUTES } from '@/constants/routes';
import { Link, usePathname } from '@/i18n/routing';
import type { GetBotMarketPositionResult } from '@/lib/api';
import { formatUsd } from '@/lib/formatters/format-usd';
import { cn } from '@/lib/utils';
import { BotTradeTimestamps } from '@/module/bot-trading/components/bot-trade-timestamps';
import { BotTrend } from '@/module/bot-trading/components/bot-trend';
import { TokenAvatar } from '@/module/manual-trading/components/token-avatar';

type Props = GetBotMarketPositionResult & {
  isOlderThan24Hours: boolean;
};

export const BotTradeDetailHeader: React.FC<Props> = ({
  tokenChain,
  tokenImageUrl,
  tokenName,
  tokenSymbol,
  openValueUsd,
  closeValueUsd,
  currentValueUsd,
  pnlAmountUsd,
  pnlAmountFraction,
  openTimeStampAt,
  closedTimeStampAt,
  tokenAddress,
  isOlderThan24Hours,
}) => {
  const pathname = usePathname();
  const t = useTranslations('bot-trading.bot-trades');

  return (
    <div
      className={cn(
        'flex flex-col sm:flex-row items-center mt-2 gap-4 sm:items-start justify-between',
        {
          'mb-4 sm:mb-5': isOlderThan24Hours,
        }
      )}
    >
      <div className="flex cursor-default flex-col overflow-hidden">
        <div className="flex items-center gap-x-1 text-md font-semibold">
          <TokenAvatar
            chain={tokenChain}
            className="my-0"
            imageUrl={tokenImageUrl}
            name={tokenName}
          />
          <div className="truncate text-text-primary">
            <Link
              href={ROUTES.MANUAL_TRADING.TOKEN_DETAIL(
                tokenAddress,
                tokenChain,
                pathname
              )}
            >
              {tokenName}
            </Link>
          </div>
          <div className="text-text-secondary">{tokenSymbol}</div>
        </div>
        <div className="flex items-center gap-x-2 text-display-s font-bold md:text-display-m">
          {closeValueUsd
            ? `${formatUsd(openValueUsd)} → ${formatUsd(closeValueUsd)}`
            : formatUsd(currentValueUsd)}
        </div>
        <div className="flex flex-wrap items-center gap-x-2">
          <BotTrend
            percentage={pnlAmountFraction.toString()}
            usdValue={pnlAmountUsd.toString()}
          />
          <BotTradeTimestamps
            endTime={closedTimeStampAt}
            startTime={openTimeStampAt}
          />
        </div>
      </div>
      {isOlderThan24Hours ? <div className="w-full sm:max-w-72">
          <CloseableAlert
            closeIconVisible={true}
            message={t('its-only-available-for-24-hours')}
            title={t('cant-find-the-chart')}
            variant="info"
          />
        </div> : null}
    </div>
  );
};
