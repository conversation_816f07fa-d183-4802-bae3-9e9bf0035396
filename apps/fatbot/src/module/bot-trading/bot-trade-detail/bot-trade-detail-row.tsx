import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { ExternalLink } from '@/assets/external-link';
import { IconLink } from '@/components/icon-link';
import { TransactionTypeTag } from '@/components/transaction-type-tag';
import { ROUTES } from '@/constants/routes';
import type { BotMarketPositionSellReason, BotTradeTransaction, Chain } from '@/lib/api';
import { formatUsd } from '@/lib/formatters/format-usd';
import { useSellReason } from '@/module/bot-trading/bot-trade-detail/utils/use-sell-reason';
import { BotTradeTimestamps } from '@/module/bot-trading/components/bot-trade-timestamps';
import { isNativeCurrencyTransfer } from '@/module/bot-trading/utils/is-native-currency-transfer';

type Props = Pick<BotTradeTransaction, 'amountUsd' | 'txCreatedAt' | 'txDetailUrl' | 'txStatus' | 'txType'> & {
  tokenChain: Chain;
  tokenName: string;
  containerClassName?: string;
  tokenAddress: string;
  sellReason?: BotMarketPositionSellReason;
};

export const BotTradeDetailRow: React.FC<Props> = ({
  txCreatedAt,
  tokenChain,
  tokenName,
  txType,
  txStatus,
  amountUsd,
  tokenAddress,
  txDetailUrl,
  sellReason,
}) => {
  const t = useTranslations('bot-trading.bot-trades');
  const pathname = usePathname();

  const { translateSellReason } = useSellReason();

  return (
    <div className="grid grid-cols-[10fr_2fr] items-center gap-2">
      <div className="flex min-w-0 items-center gap-2">
        <TransactionTypeTag chain={tokenChain} status={txStatus} type={txType} />
        <div className="flex min-w-0 flex-col justify-center gap-0">
          <span className="flex items-center gap-x-0.5 text-md font-medium text-text-primary">
            <Link
              className="truncate text-md font-medium text-text-primary"
              href={ROUTES.MANUAL_TRADING.TOKEN_DETAIL(tokenAddress, tokenChain, pathname)}
            >
              {tokenName}
            </Link>
            {!isNativeCurrencyTransfer(txType) && txDetailUrl ? (
              <IconLink
                href={txDetailUrl}
                target="_blank"
                onClick={(event) => {
                  event.stopPropagation();
                }}
              >
                <ExternalLink />
              </IconLink>
            ) : null}
          </span>
          {txType === 'SELL' && !!sellReason ? (
            <p className="font-medium">
              <span className="text-text-secondary">{`${t('sell-reason.text')}: `}</span>
              <span className="text-white">{translateSellReason(sellReason)}</span>
            </p>
          ) : null}
        </div>
      </div>
      <div className="grid items-center justify-end">
        <span className="text-right font-bold text-text-primary">{formatUsd(amountUsd)}</span>
        {txCreatedAt ? <BotTradeTimestamps startTime={txCreatedAt} textClassName="font-medium" /> : null}
      </div>
    </div>
  );
};
