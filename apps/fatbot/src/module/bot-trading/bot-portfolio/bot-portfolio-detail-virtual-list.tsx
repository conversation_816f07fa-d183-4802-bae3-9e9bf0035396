/* eslint-disable no-nested-ternary -- TODO: Get rid off disgusting ternary operator */
'use client';

import { useVirtualizer } from '@tanstack/react-virtual';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect, useRef } from 'react';
import { useDebounceValue } from 'usehooks-ts';

import { DEFAULT_OVERSCAN, DEFAULT_PAGINATION_SIZE } from '@/api/constants';
import { EndOfListMessage } from '@/components/data-list/messages/end-of-list-message';
import { LoadingDataMessage } from '@/components/data-list/messages/loading-data-message';
import { NoDataMessage } from '@/components/data-list/messages/no-data-message';
import { MyAssetsRow } from '@/components/data-list/my-assets-list/my-assets-row';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';

import { useBotPortfolioInfinite } from './use-bot-portfolio-infinite';

const ROW_GAP = 16;
const ROW_HEIGHT = 62;
const DEFAULT_DEBOUNCE_MS = 500;

export const BotPortfolioDetailVirtualList: React.FC = () => {
  const t = useTranslations('bot-trading.bot-portfolio');
  const router = useRouter();

  const searchParams = useSearchParams();
  const selectedBotId = searchParams.get('botId');
  const searchString = searchParams.get('searchString');
  const [searchStringDebounced] = useDebounceValue(searchString, DEFAULT_DEBOUNCE_MS);
  const parentRef = useRef(null);

  const { data, isFetchingNextPage, fetchNextPage, hasNextPage, status, isLoading } = useBotPortfolioInfinite(
    DEFAULT_PAGINATION_SIZE,
    selectedBotId ?? '',
    searchStringDebounced ?? undefined,
  );

  const allRows = data?.pages.flatMap((page) => page.content) ?? [];

  const rowVirtualizer = useVirtualizer({
    count: allRows.length + (hasNextPage ? 1 : 0),
    estimateSize: () => ROW_HEIGHT + ROW_GAP,
    getScrollElement: () => parentRef.current,
    measureElement: (element) => {
      const naturalHeight = element.children[0]?.getBoundingClientRect().height ?? 0;
      return naturalHeight + ROW_GAP;
    },
    overscan: DEFAULT_OVERSCAN,
  });

  const virtualItems = rowVirtualizer.getVirtualItems();

  useEffect(() => {
    const [lastItem] = virtualItems.toReversed();

    if ((lastItem?.index ?? 0) >= allRows.length - 1 && hasNextPage && !isFetchingNextPage) {
      void fetchNextPage();
    }
  }, [hasNextPage, fetchNextPage, allRows.length, isFetchingNextPage, virtualItems]);

  const fullListLoaded = !hasNextPage && !isLoading;

  return (
    <div className="flex h-full flex-col">
      {status === 'success' ? (
        <div ref={parentRef} className="-mr-1.5 h-full overflow-auto pr-1.5">
          <div className="relative transition-all duration-75" style={{ height: `${rowVirtualizer.getTotalSize()}px` }}>
            {virtualItems.map((virtualRow) => {
              const isLoaderRow = virtualRow.index > allRows.length - 1;

              const item = allRows[virtualRow.index];

              if (!item) return null;

              const {
                tokenAddress,
                id,
                pnlAmountFraction,
                pnlAmountUsd,
                currentValueUsd,
                tokenDetailUrl,
                tokenName,
                tokenSymbol,
                tokenImageUrl,
                tokenChain,
              } = item;

              return (
                <div
                  key={virtualRow.index}
                  ref={rowVirtualizer.measureElement}
                  className="absolute left-0 top-0 w-full transition-transform-ease"
                  data-index={virtualRow.index}
                  style={{
                    paddingBottom: `${ROW_GAP}px`,
                    transform: `translateY(${virtualRow.start}px)`,
                  }}
                >
                  {isLoaderRow && hasNextPage ? (
                    <LoadingDataMessage className="m-3" />
                  ) : (
                    <MyAssetsRow
                      key={id}
                      className="flex! pr-1"
                      currentValueChangeFraction={pnlAmountFraction}
                      currentValueChangeUsd={pnlAmountUsd}
                      currentValueUsd={currentValueUsd}
                      image={tokenImageUrl ?? ''}
                      tokenAmount={'0'}
                      tokenDetailUrl={tokenDetailUrl}
                      tokenName={tokenName}
                      tokenSymbol={tokenSymbol}
                      onClick={() => {
                        router.push(ROUTES.MANUAL_TRADING.TOKEN_DETAIL(tokenAddress, tokenChain));
                      }}
                    />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      ) : null}

      {fullListLoaded ? (
        virtualItems.length > 0 ? (
          <EndOfListMessage />
        ) : (
          <NoDataMessage message={t('no-more-tokens-available')} />
        )
      ) : null}
      {isLoading ? <LoadingDataMessage className="mt-2" message={t('loading-tokens')} /> : null}
    </div>
  );
};
