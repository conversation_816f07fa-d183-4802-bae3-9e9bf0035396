'use client';

import { formatDate } from 'date-fns';
import { useRouter , usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { Card } from '@/components/ui/card';
import type { GetBotResult } from '@/lib/api';
import { DATE_TIME_FORMAT } from '@/lib/formatters/format-date';
import { BotDeleteDialog } from '@/module/bot-trading/bot-wizard/components/bot-delete-dialog';
import { SummaryItemCard } from '@/module/bot-trading/bot-wizard/components/summary-item-card';
import { SummaryWalletCard } from '@/module/bot-trading/bot-wizard/components/summary-wallet-card';
import { SUMMARY_SECTION_DATA } from '@/module/bot-trading/bot-wizard/constants';
import { prepareBotWizardUrl } from '@/module/bot-trading/bot-wizard/utils/prepare-bot-wizard-url';

export type BotSummaryProps = Partial<
  Omit<GetBotResult, 'buyFrequencyLastResetAt' | 'remainingBuyFrequency' | 'userReadableId'>
> & {
  id: string;
};

interface Props {
  data: BotSummaryProps;
  isDraft?: boolean;
  isActive?: boolean;
  createdAt?: string;
}

export const BotCompareSummary = ({ data, isDraft, isActive, createdAt }: Props) => {
  const t = useTranslations('bot-trading.create-bot.bot-summary');
  const router = useRouter();
  const pathname = usePathname();

  const handleSummaryItemClick = (step: number) => {
    const url = prepareBotWizardUrl({
      ...(!isDraft && { botId: data.id }),
      ...(isDraft && { botDraftId: data.id }),
      botData: data,
      redirectUrl: pathname,
      step,
    });

    // set full wizard URL
    router.push(url);
  };

  return (
    <div className="grid grid-cols-1">
      <Card className="contents gap-4 md:flex" variant="area">
        <div className="flex h-[206px] flex-col gap-2">
          <div className="text-display-xs font-bold">{t('basics')}</div>
          {SUMMARY_SECTION_DATA.basics.map((item) => (
            <div
              key={item.value}
              className="cursor-pointer"
              onClick={() => {
                handleSummaryItemClick(item.step);
              }}
            >
              <SummaryItemCard key={item.value} {...item} value={data[item.value]} />
            </div>
          ))}

          {data.botWalletAddress && data.botWalletBalanceUsd ? (
            <SummaryWalletCard walletAddress={data.botWalletAddress} walletBalance={data.botWalletBalanceUsd} />
          ) : null}
        </div>
      </Card>

      <Card className="flex gap-2" variant="area">
        <h3 className="text-display-xs font-bold">{t('strategy')}</h3>
        {SUMMARY_SECTION_DATA.strategy.map((item) => (
          <div
            key={item.value}
            className="cursor-pointer"
            onClick={() => {
              handleSummaryItemClick(item.step);
            }}
          >
            <SummaryItemCard {...item} value={data[item.value]} />
          </div>
        ))}
      </Card>

      <Card className="flex gap-2" variant="area">
        <div className="flex items-center justify-between">
          <h3 className="text-display-xs font-bold">{t('targets')}</h3>
          <div className="rounded-full bg-primary-alphaLight px-1.5 py-0.5 text-xxs font-semibold uppercase text-text-active">
            {t('optional')}
          </div>
        </div>
        {SUMMARY_SECTION_DATA.targets.map((item) => (
          <div
            key={`${item.valueFrom}-${item.valueTo}`}
            className="cursor-pointer"
            onClick={() => {
              handleSummaryItemClick(item.step);
            }}
          >
            <SummaryItemCard {...item} valueFrom={data[item.valueFrom]} valueTo={data[item.valueTo]} />
          </div>
        ))}

        {createdAt || data.id ? (
          <div className="mt-2 flex items-center justify-between">
            {createdAt ? (
              <div className="text-md text-text-secondary">
                {t('created-at')} {formatDate(createdAt, DATE_TIME_FORMAT)}
              </div>
            ) : null}

            {data.id ? (
              <BotDeleteDialog
                botBalance={Number(data.botWalletBalanceUsd ?? 0)}
                botId={data.id}
                isActive={isActive ? !isDraft : undefined}
              />
            ) : null}
          </div>
        ) : null}
      </Card>
    </div>
  );
};
