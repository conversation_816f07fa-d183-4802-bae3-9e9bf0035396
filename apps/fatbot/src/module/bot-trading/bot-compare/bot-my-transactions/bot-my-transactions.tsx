import { useTranslations } from 'next-intl';

import { DataListHeader } from '@/components/data-list/data-list-header';
import { Skeleton } from '@/components/skeleton';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import type { CompareBotsResult } from '@/lib/api';
import { BotTransactionsRow } from '@/module/bot-trading/bot-transactions/bot-transactions-row';
import { isNativeCurrencyTransfer } from '@/module/bot-trading/utils/is-native-currency-transfer';

interface Props {
  data: CompareBotsResult['botTransactions'];
  botId: string;
  isLoading: boolean;
  redirectUrl?: string;
}

export const BotMyTransactions: React.FC<Props> = ({ data, botId, isLoading, redirectUrl }) => {
  const t = useTranslations('bot-trading.bot-compare');
  const router = useRouter();
  const hasData = !isLoading && !!data.length;

  return (
    <div className="grid grid-cols-1 gap-2">
      <DataListHeader link={ROUTES.BOT_TRADING.BOT_TRANSACTIONS(botId, redirectUrl)} title={t('my-transactions')} />

      {isLoading ? (
        <>
          <Skeleton className="h-7.5 w-full rounded-xs shadow-primary" />
          <Skeleton className="h-7.5 w-full rounded-xs shadow-primary" />
        </>
      ) : null}

      <div className="grid gap-2 @container/mtr">
        {hasData
          ? data.slice(0, 1).map((item) => (
              <BotTransactionsRow
                key={item.txId}
                chain={item.txChain}
                containerClassName="md:grid-cols-[6fr_2fr_4fr]"
                explorerUrl={item.txDetailUrl}
                tokenAmount={item.nativeAmount}
                tokenName={item.tokenName ?? ''}
                tokenSymbol={item.tokenSymbol ?? ''}
                totalWethAmountUsd={item.amountUsd}
                transactionStatus={item.txStatus}
                transactionType={item.txType}
                {...(!isNativeCurrencyTransfer(item.txType) && {
                  onClick: () => {
                    router.push(ROUTES.BOT_TRADING.BOT_TRANSACTIONS(botId, redirectUrl));
                  },
                })}
              />
            ))
          : null}

        {!isLoading && !hasData ? (
          <div className="text-center text-sm text-text-secondary">{t('no-bot-transactions-found')}</div>
        ) : null}
      </div>
    </div>
  );
};
