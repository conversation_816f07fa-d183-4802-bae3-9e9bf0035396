import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';
import { useMemo, useTransition } from 'react';
import { toast } from 'sonner';

import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import {
  getGetBotByIdQueryOptions,
  getGetMyBotsQueryOptions,
  getSearchUserBotDetailQueryOptions,
  TimeRange,
  useGetMyBots,
  useUpdateBotState,
} from '@/lib/api';
import { isActiveBot as isActiveBotUtility } from '@/module/bot-trading/active-bots/utils';
import { MAX_ACTIVE_BOTS_ALLOWED } from '@/module/bot-trading/bot-wizard/constants';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';
import { parseTimeRange } from '@/module/bot-trading/utils/timerange-utils';

export const useToggleActivateBot = () => {
  const t = useTranslations('bot-trading.bot-compare');
  const queryClient = useQueryClient();
  const [isPending, startTransition] = useTransition();
  const { push } = useRouter();
  const { mutateAsync: updateBotState } = useUpdateBotState();
  const { data: myBots } = useGetMyBots({ timeRange: TimeRange.ALL });

  const [timeRange] = useQueryState('timeRange', {
    parse: parseTimeRange,
    defaultValue: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
  });

  const activeBots = useMemo(() => (myBots ?? []).filter((bot) => isActiveBotUtility(bot)), [myBots]);

  const handleToggleActivateBot = (botId: string, checked: boolean, onSuccess?: () => void) => {
    startTransition(async () => {
      try {
        const isMaxActiveBotsReached = activeBots.length >= MAX_ACTIVE_BOTS_ALLOWED;

        if (checked && isMaxActiveBotsReached) {
          push(ROUTES.BOT_TRADING.ACTIVE_BOTS_SELECTION(botId));
          return;
        }

        await updateBotState({ botId, params: { active: checked } });
        toast.success(t('bot-status-updated'));
        onSuccess?.();

        await queryClient.invalidateQueries(
          getSearchUserBotDetailQueryOptions({
            botId,
            timeRange: timeRange,
          }),
        );
        await queryClient.invalidateQueries(getGetMyBotsQueryOptions({ timeRange: TimeRange.ALL }));
        await queryClient.invalidateQueries(getGetBotByIdQueryOptions(botId));
      } catch {
        toast.error(t('bot-status-update-failed'));
      }
    });
  };

  return { isPending, handleToggleActivateBot };
};
