import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { match } from 'ts-pattern';

import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { Link } from '@/i18n/routing';
import { BotStatus, type GetAllUserWalletsResult } from '@/lib/api';
import { BotStatusToggleDialog } from '@/module/bot-trading/bot-compare/portfolio/bot-status-toggle-dialog';
import { useToggleActivateBot } from '@/module/bot-trading/bot-compare/portfolio/use-toggle-activate-bot';
import { useResetDailyLimit } from '@/module/bot-trading/hooks/use-reset-daily-limit';
import { WalletDepositDialog } from '@/module/wallet-detail/wallet-deposit-dialog';

interface Props {
  status: BotStatus;
  botId: string;
  isActive: boolean;
  walletAddress: string;
  selectedWallet: GetAllUserWalletsResult | null;
}

export const BotStatusCardAction: React.FC<Props> = ({ status, botId, isActive, walletAddress, selectedWallet }) => {
  const t = useTranslations('bot-trading.bot-status');
  const router = useRouter();
  const [depositOpen, setDepositOpen] = useState(false);
  const { handleResetLimit, isPending } = useResetDailyLimit(botId);
  const { handleToggleActivateBot } = useToggleActivateBot();
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);

  const handleAdjustSettings = () => {
    router.push(ROUTES.BOT_TRADING.SETTINGS(botId));
  };

  const handleBotStatusToggle = () => {
    handleToggleActivateBot(botId, true);
    setIsDialogOpen(false);
  };

  return match(status)
    .with(BotStatus.HAS_PURCHASED, () => null)
    .with(BotStatus.NO_PURCHASE, () => (
      <Button className="w-full" variant="success" onClick={handleAdjustSettings}>
        {t('adjust-settings')}
      </Button>
    ))
    .with(BotStatus.BUY_DAILY_LIMIT_REACHED, () => (
      <div className="@container/bsca">
        <div className="flex-col @[320px]/bsca:flex-row flex w-full items-center gap-2">
          <Button className="w-full" disabled={isPending || !isActive} variant="warning" onClick={handleResetLimit}>
            {t('reset')}
          </Button>
          <Button className="w-full" variant="warning-inverted" onClick={handleAdjustSettings}>
            {t('adjust')}
          </Button>
        </div>
      </div>
    ))
    .with(BotStatus.DEACTIVATED, () => (
      <>
        <BotStatusToggleDialog
          handleSwitchChange={() => {
            handleBotStatusToggle();
          }}
          isLoading={isPending}
          isOpen={isDialogOpen}
          type="enable"
          onClose={() => {
            setIsDialogOpen(false);
          }}
        />
        <Button
          className="w-full"
          variant="primary"
          onClick={() => {
            setIsDialogOpen(true);
          }}
        >
          {t('activate-bot')}
        </Button>
      </>
    ))
    .with(BotStatus.INSUFFICIENT_BALANCE, () => (
      <div className="flex w-full items-center gap-2">
        <Button
          className="w-full"
          variant="error"
          onClick={() => { setDepositOpen(true) }}
        >
          {t('deposit-funds')}
        </Button>
        <WalletDepositDialog
          open={depositOpen}
          selectedWallet={selectedWallet}
          walletAddress={walletAddress}
          onOpenChange={setDepositOpen}
        />
      </div>
    ))
    .with(BotStatus.INSUFFICIENT_BALANCE_FUNDS_IN_OPEN_POSITIONS, () => (
      <div className="grid grid-cols-2 items-center gap-2">
        <Button
          className="w-full"
          variant="warning"
          onClick={() => { setDepositOpen(true) }}
        >
          {t('deposit-funds')}
        </Button>
        <WalletDepositDialog
          open={depositOpen}
          selectedWallet={selectedWallet}
          walletAddress={walletAddress}
          onOpenChange={setDepositOpen}
        />
        <Link href={ROUTES.BOT_TRADING.BOT_TRADES(botId, ROUTES.BOT_TRADING.DETAIL(botId))}><Button className="w-full" variant="warning-inverted">{t('sell-manually')}</Button></Link>
      </div>
    ))
    .exhaustive();
};
