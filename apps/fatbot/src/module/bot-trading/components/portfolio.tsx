/* eslint-disable no-nested-ternary -- TODO: Get rid of disgusting nested ternary operator */
'use client';

import BigNumber from 'bignumber.js';
import type { AreaData } from 'lightweight-charts';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';
import { useMemo, useState } from 'react';

import { AreaChart } from '@/components/charts/area-chart/area-chart';
import { useTimeRangeOptions } from '@/components/charts/area-chart/use-options';
import { generateEmptyAreaData } from '@/components/charts/area-chart/utils';
import { timeRangeToTickMarkMap } from '@/components/charts/constants';
import { timeTickFormatter } from '@/components/charts/utils/time-tick-formatter';
import { CloseableAlert } from '@/components/closeable-alert';
import { ProfitCard } from '@/components/profit-card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DEFAULT_LOCALE } from '@/constants';
import { ROUTES } from '@/constants/routes';
import { TimeRange, type BotsOverviewResult, type GetMyBotsResult } from '@/lib/api';
import { formatCompactUsd } from '@/lib/formatters/format-compact-usd';
import { formatUsd } from '@/lib/formatters/format-usd';
import { cn } from '@/lib/utils';
import { MAX_BOTS_ALLOWED } from '@/module/bot-trading/bot-wizard/constants';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';
import { getBotWallet } from '@/module/bot-trading/utils/get-bot-wallet';
import { getTimeRangeLabel, parseTimeRange, TIME_RANGE_QUERY_PARAM } from '@/module/bot-trading/utils/timerange-utils';
import { WalletDepositDialog } from '@/module/wallet-detail/wallet-deposit-dialog';

import { CreateBotInfoCard } from './create-bot-info-card';

interface Props {
  data: BotsOverviewResult | null | undefined;
  myBots: GetMyBotsResult[];
}

export const Portfolio: React.FC<Props> = ({ data, myBots }) => {
  const t = useTranslations();
  const timeRangeOptions = useTimeRangeOptions([TimeRange.YEAR]);
  const [openDepositDialog, setOpenDepositDialog] = useState(false);

  const [timeRange, setTimeRange] = useQueryState(TIME_RANGE_QUERY_PARAM, {
    defaultValue: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
    parse: parseTimeRange,
  });

  const canCreateBot = myBots.length < MAX_BOTS_ALLOWED;

  const {
    botsTotalValueAmountUsd,
    botsTotalPnlAmountUsd,
    botsTotalPnlAmountFraction,
    botsOneDayChangeAmountUsd,
    botsOneDayChangeFraction,
    botsPortfolioValueSumPastValues,
  } = data ?? {};

  const isEmptyPortfolio = !data || botsTotalValueAmountUsd === '0';

  const launchedBots = myBots.filter((bot) => !bot.draftCompleteness);

  const showDepositButton = launchedBots.length > 0;
  const showDepositPage = launchedBots.length > 1;

  const chartData = useMemo(() => {
    if (isEmptyPortfolio) {
      return generateEmptyAreaData(timeRange);
    }

    return (
      botsPortfolioValueSumPastValues
        ?.map((value) => ({
          value: Number(value.portfolioValueUsd),
          time: Math.floor(new Date(value.createdAt).getTime() / 1_000),
        }))
        .toSorted((a, b) => a.time - b.time) ?? []
    );
  }, [botsPortfolioValueSumPastValues, isEmptyPortfolio, timeRange]);

  const defaultWallet = getBotWallet(myBots?.[0] ?? null);
  const isPublic = data === null;

  return (
    <div className="flex flex-col gap-y-5 @container/BOTP">
      <div>
        <div className="grid">
          <p className="text-md font-medium capitalize text-text-secondary" data-testid="bot-portfolio-label">
            {t('bot-trading.all-my-bots-portfolio-value')}
          </p>
          <h3 className="text-display-l font-bold" data-testid="bot-portfolio-total-value">
            {formatUsd(botsTotalValueAmountUsd ?? '0')}
          </h3>
        </div>
        <div className="flex flex-col gap-y-2">
          <div className="h-[240px]">
            <AreaChart
              data={chartData as AreaData[]}
              error={null}
              loading={false}
              options={{
                formatter: formatCompactUsd,
                height: 240,
                interval: timeRange,
                timeFormatter: timeTickFormatter({
                  isTimestampInSeconds: true,
                }),
                localization: {
                  locale: DEFAULT_LOCALE,
                  timeFormatter: (value: number) =>
                    timeTickFormatter({ isTimestampInSeconds: true })(value, timeRangeToTickMarkMap[timeRange]),
                },
                scaleMargins: isEmptyPortfolio
                  ? {
                      top: 0.1,
                      bottom: 0,
                    }
                  : undefined,
                autoScaleInfoProvider: isEmptyPortfolio
                  ? () => ({
                      priceRange: {
                        minValue: 0,
                        maxValue: 1,
                      },
                    })
                  : undefined,
              }}
            />
          </div>
          <Tabs
            value={timeRange}
            onValueChange={(value) => {
              void setTimeRange(value as TimeRange);
            }}
          >
            <TabsList className="flex w-auto border">
              {timeRangeOptions.map(({ key, value }) => (
                <TabsTrigger
                  key={key}
                  className={cn('px-1 py-0.5 text-md font-medium', {
                    'font-bold': key === timeRange,
                  })}
                  data-testid={`timerange-tabs-trigger-${value}`}
                  value={key}
                >
                  {value}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>

          <div className="grid grid-cols-1 gap-1 @[340px]/BOTP:grid-cols-2">
            <ProfitCard
              className="gap-0.5"
              testId="bot-total-profit-value"
              title={
                new BigNumber(botsTotalPnlAmountUsd ?? '0').gte(0)
                  ? t('bot-trading.total-profit')
                  : t('common.total-loss')
              }
              trend={botsTotalPnlAmountFraction ?? '0'}
              value={botsTotalPnlAmountUsd ?? '0'}
            />
            <ProfitCard
              className="gap-0.5"
              testId="bot-period-change-value"
              title={t('period-change.total-period-change', {
                period: getTimeRangeLabel(timeRange),
              })}
              trend={botsOneDayChangeFraction ?? '0'}
              value={botsOneDayChangeAmountUsd ?? '0'}
            />
          </div>
        </div>
      </div>
      <div className="grid w-full gap-2">
        <CreateBotInfoCard canCreateBot={canCreateBot} isPublic={isPublic} />
        {showDepositButton ? (
          showDepositPage ? (
            <Button asChild className="flex-1" variant="outline">
              <Link href={ROUTES.BOT_TRADING.BOT_DEPOSIT_TO_WALLET(ROUTES.BOT_TRADING.ROOT)}>
                {t('manual-trading.deposit-wallet.deposit')}
              </Link>
            </Button>
          ) : (
            <WalletDepositDialog
              open={openDepositDialog}
              selectedWallet={defaultWallet}
              walletAddress={defaultWallet?.walletAddress ?? ''}
              onOpenChange={setOpenDepositDialog}
            >
              <Button disabled={!defaultWallet?.walletAddress} variant="outline">
                {t('manual-trading.deposit-wallet.deposit')}
              </Button>
            </WalletDepositDialog>
          )
        ) : null}
        {!canCreateBot ? (
          <CloseableAlert
            message={t('bot-trading.create-bot.maximum-bots', {
              count: MAX_BOTS_ALLOWED,
            })}
            title={t('common.warning')}
            variant="warning"
          />
        ) : null}
      </div>
    </div>
  );
};
