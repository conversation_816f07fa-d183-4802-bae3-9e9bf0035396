import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { InfoCard } from '@/components/info-card';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes';
import { useGetAllBots } from '@/lib/api';
import { cn } from '@/lib/utils';

interface Props {
  className?: string;
  isPublic?: boolean;
  canCreateBot?: boolean;
}

export const CreateBotInfoCard: React.FC<Props> = ({
  className,
  isPublic,
  canCreateBot,
}) => {
  const t = useTranslations('bot-trading');
  const { data } = useGetAllBots({
    query: {
      enabled: !isPublic,
    },
  });

  return (data?.length ?? 0) > 0 ? (
    <Button asChild>
      <Link
        className={cn({
          'pointer-events-none cursor-not-allowed opacity-50':
            !isPublic && !canCreateBot,
        })}
        href={ROUTES.BOT_TRADING.BOT_WIZARD()}
      >
        {t('create-new-bot')}
      </Link>
    </Button>
  ) : (
    <InfoCard
      action={
        <div className="grid w-full gap-2">
          <Button asChild>
            <Link
              className={cn({
                'pointer-events-none cursor-not-allowed opacity-50':
                  !isPublic && !canCreateBot,
              })}
              href={ROUTES.BOT_TRADING.BOT_WIZARD()}
            >
              {t('create-new-bot')}
            </Link>
          </Button>
        </div>
      }
      className={cn('w-full rounded-md p-2', className)}
      description={t('create-your-first-bot')}
      title={t('ready-to-get-started')}
      variant="info"
    />
  );
};
