'use client';

import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { type ReactNode, useMemo } from 'react';

import { cn } from '@/lib/utils';

interface Props {
  children: ReactNode;
  link?: string;
  linkTitle?: string;
}

export const MyBotsHeader: React.FC<Props> = ({
  children,
  linkTitle,
  link = '',
}) => {
  const t = useTranslations();

  const header = useMemo(
    () => (
      <div className="group">
        <h3 className="flex w-full items-end justify-between text-display-xs font-bold capitalize leading-none text-text-primary">
          {children}
          {link ? <span
              className={cn(
                'whitespace-nowrap text-md font-semibold capitalize text-text-secondary transition-colors hover:text-text-primary',
                {
                  'group-hover:text-white': link,
                }
              )}
            >
              {linkTitle ?? t('lists.see-all')}
            </span> : null}
        </h3>
      </div>
    ),
    [t, children, link, linkTitle]
  );

  return link ? (
    <Link className="w-full" href={link}>
      {header}
    </Link>
  ) : (
    header
  );
};
