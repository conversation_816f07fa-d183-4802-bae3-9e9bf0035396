'use client';

import { useTranslations } from 'next-intl';
import type { FC } from 'react';

import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { type GetMyBotsResult } from '@/lib/api';
import { BotTradingCardSkeleton } from '@/module/bot-trading/bot-trading-card-skeleton';
import { prepareBotWizardUrl } from '@/module/bot-trading/bot-wizard/utils/prepare-bot-wizard-url';
import { useBotTradingData } from '@/module/bot-trading/use-bot-trading-data';

import { MyBotsCard } from './my-bots-card';
import { MyBotsHeader } from './my-bots-header';

export const MyBots: FC = () => {
  const t = useTranslations('bot-trading');
  const router = useRouter();
  const { myBots, myBotsIsLoading, myBotsIsPending } = useBotTradingData();

  const handleCardClick = (botId: string, bot: GetMyBotsResult) => {
    const isDraft = bot.draftCompleteness;
    if (isDraft) {
      // redirect to bot draft summary step
      router.push(
        prepareBotWizardUrl({
          botDraftId: botId,
          botData: bot,
          redirectUrl: ROUTES.BOT_TRADING.ROOT,
        })
      );
      return;
    }

    // redirect finished bot to bot compare activity page
    router.push(ROUTES.BOT_TRADING.DETAIL(botId, ROUTES.BOT_TRADING.ROOT));
  };

  if (!myBots && !(myBotsIsLoading || myBotsIsPending)) {
    return null;
  }

  if (myBotsIsLoading || myBotsIsPending) {
    return <BotTradingCardSkeleton />;
  }

  return (
    <div className="flex flex-col gap-y-2">
      <MyBotsHeader
        link={ROUTES.BOT_TRADING.BOT_COMPARE_ACTIVITY(ROUTES.BOT_TRADING.ROOT)}
        linkTitle={t('compare')}
      >
        <div className="flex flex-col gap-1">
          <h3 className="text-display-xs font-bold">{t('my-bots')}</h3>
        </div>
      </MyBotsHeader>

      <div className="scrollbar-hidden grid h-[80dvh] max-h-[700px] grid-cols-1 place-content-start gap-2 overflow-y-auto pb-1">
        {myBots?.map((bot) => (
          <div
            key={bot.id}
            className="cursor-pointer"
            onClick={() => { handleCardClick(bot.id, bot); }}
          >
            <MyBotsCard
              avatarFileId={bot.avatarFileId}
              balanceUsd={bot.balanceUsd}
              buyCount={bot.buyCount}
              draftCompleteness={bot.draftCompleteness}
              isActive={bot.isActive}
              name={bot.name}
              sellCount={bot.sellCount}
              timeRangeChangeFraction={bot.timeRangeChangeFraction}
              timeRangeChangeUsd={bot.timeRangeChangeUsd}
            />
          </div>
        ))}
      </div>
    </div>
  );
};
