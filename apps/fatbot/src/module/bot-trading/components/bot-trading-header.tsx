'use client';

import { usePathname } from 'next/navigation';

import { Logo } from '@/assets';
import { AppNavigation } from '@/components/app-header/app-navigation';
import { ProfileLink } from '@/components/profile-link';
import { ROUTES } from '@/constants/routes';
import { useMediaQueryMatch } from '@/lib/use-media-query-match';

import { ChainSelector } from './chain-selector';

const mobileHiddenRoutes = [ROUTES.BOT_TRADING.BOT_WITHDRAW('')];

export const BotTradingHeader = ({ leagueSystemEnabled }: { leagueSystemEnabled?: boolean }) => {
  const isSmallScreen = useMediaQueryMatch('large');
  const pathname = usePathname();
  const isMobileHidden = isSmallScreen && mobileHiddenRoutes.some((route) => pathname.includes(route));

  if (isMobileHidden) {
    return null;
  }

  return (
    <div className="grid grid-cols-2 items-center px-0 py-2 sm:px-0 sm:pb-3 sm:pt-4 md:grid-cols-[1fr_auto_1fr]">
      <Logo className="size-5 text-primary" />
      <AppNavigation leagueSystemEnabled={leagueSystemEnabled} />
      <div className="flex items-center justify-end gap-3">
        <ChainSelector />
        <ProfileLink />
      </div>
    </div>
  );
};
