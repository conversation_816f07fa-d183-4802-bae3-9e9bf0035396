import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { InfoCard } from '@/components/info-card';
import { Skeleton } from '@/components/skeleton';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { type Chain, type GetAllUserWalletsResult } from '@/lib/api';
import { useBotDetailData } from '@/module/bot-trading/bot-detail/hooks/use-bot-detail-data';
import { useBotDetailSettingsData } from '@/module/bot-trading/bot-detail/hooks/use-bot-detail-settings-data';
import { WalletDepositDialog } from '@/module/wallet-detail/wallet-deposit-dialog';

import { WithdrawDeactivateBotDialog } from './withdraw-deactivate-bot-dialog';

interface BotDetailInsufficientFundsProps {
  botId: string;
  botWalletAddress: string;
  botWalletChain: string;
}

export const BotDetailInsufficientFunds: React.FC<BotDetailInsufficientFundsProps> = ({
  botId,
  botWalletAddress,
  botWalletChain,
}) => {
  const router = useRouter();
  const t = useTranslations('bot-trading');
  const { botDetailSettings, isLoading } = useBotDetailSettingsData({ botId });
  const { refetchBotDetail } = useBotDetailData({ botId });
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deactivateBotDialogOpen, setDeactivateBotDialogOpen] = useState(false);
  const { botDetail, isLoading: botDetailIsLoading } = useBotDetailData({
    botId,
  });

  const redirectUrl = ROUTES.BOT_TRADING.DETAIL(botId);

  if (isLoading || botDetailIsLoading) {
    return <Skeleton className="h-8 w-full rounded-full" data-testid="loading-skeleton" />;
  }

  const handleDialogChange = (value: boolean) => {
    setDialogOpen(value);
    if (!value) {
      // refetch bot detail after deposit dialog is closed
      void refetchBotDetail();
    }
  };

  // TODO: replace with actual wallet data if bot wallets EP is ready
  const walletData: GetAllUserWalletsResult = {
    walletAddress: botWalletAddress,
    walletId: '',
    isDefault: true,
    chain: botWalletChain as Chain,
    currentPortfolioValueUsd: '0',
    currentPortfolioValueChangeUsd: '0',
    currentPortfolioValueChangeFraction: '0',
    walletDetailUrl: '',
    walletBalance: '0',
    walletBalanceUsd: '0',
  };

  if (botDetailSettings?.hasSufficientBalanceForTrade) {
    return (
      <div className="flex items-center gap-2" data-testid="sufficient-balance-container">
        <WalletDepositDialog
          open={dialogOpen}
          selectedWallet={walletData}
          walletAddress={botWalletAddress}
          onOpenChange={handleDialogChange}
        >
          <Button className="flex-1 px-0" data-testid="deposit-button">
            {t('bot-detail.deposit')}
          </Button>
        </WalletDepositDialog>
        {botDetail?.isActive ? (
          <WithdrawDeactivateBotDialog
            botAvatar={botDetail.botAvatarFileId}
            botId={botId}
            open={deactivateBotDialogOpen}
            onOpenChange={setDeactivateBotDialogOpen}
            onSuccess={() => {
              router.push(ROUTES.BOT_TRADING.BOT_WITHDRAW(botId, redirectUrl));
            }}
          >
            <Button
              className="flex-1 px-0"
              data-testid="withdraw-button"
              variant="outline"
              onClick={() => {
                setDeactivateBotDialogOpen(true);
              }}
            >
              {t('bot-detail.withdraw')}
            </Button>
          </WithdrawDeactivateBotDialog>
        ) : (
          <Button asChild className="flex-1 px-0" data-testid="withdraw-button" variant="outline">
            <Link href={ROUTES.BOT_TRADING.BOT_WITHDRAW(botId, redirectUrl)}>{t('bot-detail.withdraw')}</Link>
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2" data-testid="insufficient-balance-container">
      <InfoCard
        action={
          <WalletDepositDialog
            open={dialogOpen}
            selectedWallet={{
              ...walletData,
              isDefault: false,
            }}
            walletAddress={botWalletAddress}
            onOpenChange={handleDialogChange}
          >
            <Button className="w-full" data-testid="insufficient-funds-deposit-button" variant="warning">
              {t('insufficient-funds-cta')}
            </Button>
          </WalletDepositDialog>
        }
        className="w-full rounded-md"
        description={t('insufficient-funds-description')}
        title={t('insufficient-funds')}
        variant="warning"
      />
    </div>
  );
};
