'use client';

import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';
import { useState } from 'react';

import { OptionCard } from '@/components/option-card';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { RadioGroup } from '@/components/ui/radio-group';
import { useRouter } from 'next/navigation';
import { Chain, type GetMyBotsResult, useGetMyBots } from '@/lib/api';
import { formatAddress } from '@/lib/formatters/format-address';
import { formatUsd } from '@/lib/formatters/format-usd';
import { BotAvatar } from '@/module/bot-trading/components/bot-avatar';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';
import { getBotWallet } from '@/module/bot-trading/utils/get-bot-wallet';
import { parseTimeRange, TIME_RANGE_QUERY_PARAM } from '@/module/bot-trading/utils/timerange-utils';
import { useRedirectUrl } from '@/module/routing/use-redirect-url';
import { WalletDepositDialog } from '@/module/wallet-detail/wallet-deposit-dialog';

export const BotDepositToWalletContent: React.FC = () => {
  const router = useRouter();
  const t = useTranslations();
  const redirectUrl = useRedirectUrl();
  const [timeRange] = useQueryState(TIME_RANGE_QUERY_PARAM, {
    defaultValue: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
    parse: parseTimeRange,
  });
  const { data: bots } = useGetMyBots({
    timeRange,
  });

  const launchedBots = bots?.filter((bot) => !bot.draftCompleteness) ?? [];

  const [selectedBot, setSelectedBot] = useState<GetMyBotsResult | null>(
    bots?.find((bot) => !bot.draftCompleteness) ?? null,
  );

  const selectedWallet = getBotWallet(selectedBot);

  return (
    <Card className="mx-auto max-w-2xl gap-3" data-testid="bot-deposit-to-wallet">
      <div className="flex flex-col gap-2">
        <h2 className="text-display-l font-bold">{t('bot-trading.deposit.title')}</h2>
      </div>

      <RadioGroup>
        {launchedBots.map((bot) => (
          <OptionCard
            key={bot.id}
            active={selectedBot?.id === bot.id}
            caption={formatUsd(bot.balanceUsd ?? '0')}
            chain={Chain.SOLANA}
            leadingContent={<BotAvatar avatarFileId={bot.avatarFileId ?? ''} className="size-4" variant="head" />}
            subtitle={formatAddress(bot.botWalletAddress ?? '')}
            title={bot.name ?? ''}
            onClick={() => {
              setSelectedBot(bot);
            }}
          />
        ))}
      </RadioGroup>

      <WalletDepositDialog
        selectedWallet={selectedWallet}
        walletAddress={selectedWallet?.walletAddress ?? ''}
        onContinue={() => {
          router.push(redirectUrl);
        }}
      >
        <Button disabled={!selectedWallet?.walletAddress}>{t('deposit-to-wallet.continue')}</Button>
      </WalletDepositDialog>
    </Card>
  );
};
