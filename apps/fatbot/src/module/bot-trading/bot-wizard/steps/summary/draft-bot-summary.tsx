'use client';

import { useQ<PERSON>yClient } from '@tanstack/react-query';
import { useRouter , usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { type FC, useMemo, useTransition } from 'react';
import { toast } from 'sonner';

import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/tooltip';
import { ROUTES } from '@/constants/routes';
import { getGetMyBotsQueryOptions } from '@/lib/api';
import { getErrorMessage } from '@/lib/get-error-message';
import { isActiveBot } from '@/module/bot-trading/active-bots/utils';
import { BotSummary } from '@/module/bot-trading/bot-wizard/components/bot-summary';
import { BotWizardAppHeader } from '@/module/bot-trading/bot-wizard/components/bot-wizard-app-header';
import { BotWizardProcessBar } from '@/module/bot-trading/bot-wizard/components/bot-wizard-process-bar';
import { NextStepButton } from '@/module/bot-trading/bot-wizard/components/next-step-button';
import { MAX_ACTIVE_BOTS_ALLOWED, SUMMARY_SECTION_DATA } from '@/module/bot-trading/bot-wizard/constants';
import { useBotWizardApi } from '@/module/bot-trading/bot-wizard/hooks/use-bot-wizard-api';
import { useBotWizardStep } from '@/module/bot-trading/bot-wizard/hooks/use-bot-wizard-step';
import { useExitWizard } from '@/module/bot-trading/bot-wizard/hooks/use-exit-wizard';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';

interface Props {
  botDraftId: string;
}

export const DraftBotSummary: FC<Props> = ({ botDraftId }) => {
  const t = useTranslations('bot-trading');
  const queryClient = useQueryClient();
  const { push } = useRouter();
  const pathname = usePathname();
  const { handleExitWizard } = useExitWizard();
  const { setStep } = useBotWizardStep();
  const [isLaunchPending, startLaunchTransition] = useTransition();

  const { apiCreateBot, botDrafts, bots: myBots } = useBotWizardApi();

  const botDraftToBeCreated = botDrafts?.find((draft) => draft.id === botDraftId);

  const activeBots = useMemo(() => (myBots ?? []).filter(isActiveBot), [myBots]);

  const mandatoryFieldMissing = SUMMARY_SECTION_DATA.strategy.some((item) => botDraftToBeCreated?.[item.value] == null);

  const handleNextClick = () => {
    if (mandatoryFieldMissing) {
      const missingMandatoryStep = SUMMARY_SECTION_DATA.strategy.find(
        (item) => botDraftToBeCreated?.[item.value] == null,
      );

      if (missingMandatoryStep) {
        setStep(missingMandatoryStep.step);
      }
    }
  };

  const handleLaunchBot = () => {
    startLaunchTransition(async () => {
      if (!botDraftId) {
        toast.error(t('missing-bot-id'));
        return;
      }

      if (activeBots.length >= MAX_ACTIVE_BOTS_ALLOWED) {
        push(ROUTES.BOT_TRADING.ACTIVE_BOTS_SELECTION(botDraftId));

        return;
      }

      try {
        const { botId } = await apiCreateBot({ botDraftId });
        await queryClient.invalidateQueries(
          getGetMyBotsQueryOptions({
            timeRange: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
          }),
        );

        push(ROUTES.BOT_TRADING.LAUNCH(botId, pathname));
      } catch (error) {
        toast.error(getErrorMessage(error));
      }
    });
  };

  const isBotDataEmpty = myBots == null;

  const disabledLaunchButton = mandatoryFieldMissing || isBotDataEmpty;

  const getTooltip = () => {
    if (!myBots) {
      return null;
    }

    if (mandatoryFieldMissing) {
      return t('create-bot.launch-bot-missing-required-fields');
    }

    return t('create-bot.launch-bot-my-bots-undefined');
  };
  const tooltip = getTooltip();

  if (!botDraftToBeCreated) {
    return null;
  }

  return (
    <>
      <BotWizardAppHeader onExit={handleExitWizard} />
      <div className="flex h-full flex-col overflow-auto sm:px-5 sm:pb-3 sm:pt-4">
        <BotSummary botData={botDraftToBeCreated} />
      </div>

      <div>
        <BotWizardProcessBar />
        <div className="wizard-action-wrapper">
          <Tooltip open={disabledLaunchButton}>
            <TooltipTrigger asChild className="flex-1 sm:flex-none">
              <NextStepButton
                className="px-3 disabled:bg-surface-brand-1-button"
                disabled={disabledLaunchButton || isLaunchPending}
                label={t('create-bot.launch-bot')}
                onClick={handleLaunchBot}
              />
            </TooltipTrigger>
            {tooltip ? (
              <TooltipContent>
                <p>{tooltip}</p>
              </TooltipContent>
            ) : null}
            {mandatoryFieldMissing ? <NextStepButton className="flex-1" onClick={handleNextClick} /> : null}
          </Tooltip>
        </div>
      </div>
    </>
  );
};
