'use client';

import { useSearchParams } from 'next/navigation';

import { ArrowLeft } from '@/assets';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { useBotWizardStep } from '@/module/bot-trading/bot-wizard/hooks/use-bot-wizard-step';

export const BotWizardBackButton = () => {
  const { push } = useRouter();
  const { currentStep, prev } = useBotWizardStep();
  const redirectUrl = useSearchParams().get('redirectUrl') ?? '';

  const onBackClick = () => {
    if (redirectUrl) {
      push(redirectUrl);
      return;
    }

    const newPreviousStep = currentStep - 1;

    if (!newPreviousStep) {
      push(ROUTES.BOT_TRADING.ROOT);
      return;
    }

    void prev();
  };

  return (
    <Button size="icon" type="button" variant="secondary" onClick={onBackClick}>
      <ArrowLeft className="size-3 shrink-0" />
    </Button>
  );
};
