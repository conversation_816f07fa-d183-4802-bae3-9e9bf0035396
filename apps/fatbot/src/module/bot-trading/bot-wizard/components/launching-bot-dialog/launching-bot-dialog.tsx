'use client';

import { type FC, useState } from 'react';

import { Dialog, DialogContent, DialogDescription, DialogTitle } from '@/components/ui/dialog';
import { Loader } from '@/components/ui/loader';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { useBotDetailData } from '@/module/bot-trading/bot-detail/hooks/use-bot-detail-data';
import { BotFundDeposit } from '@/module/bot-trading/bot-wizard/components/launching-bot-dialog/steps/bot-funds-deposit';
import { LaunchBotStep } from '@/module/bot-trading/bot-wizard/components/launching-bot-dialog/steps/launch-bot';
import {
  LaunchBotStatus,
  LaunchStep,
} from '@/module/bot-trading/bot-wizard/components/launching-bot-dialog/steps/utils';

interface Props {
  botId: string;
}

export const LaunchingBotDialog: FC<Props> = ({ botId }) => {
  const { push } = useRouter();
  const [step, setStep] = useState<LaunchStep>(LaunchStep.LAUNCH);
  const [status, setStatus] = useState<LaunchBotStatus>(LaunchBotStatus.INITIATED);

  const { botDetail, isLoading: isLoadingBotDetail } = useBotDetailData({
    botId,
  });

  const closeDialog = () => {
    push(ROUTES.BOT_TRADING.DETAIL(botId));
  };

  const renderContent = (step: LaunchStep, status: LaunchBotStatus) => {
    if (isLoadingBotDetail) {
      return <Loader className="h-80" />;
    }

    if (step === LaunchStep.DEPOSIT) {
      return <BotFundDeposit botId={botId} botWalletAddress={botDetail?.botWalletAddress ?? ''} />;
    }

    return (
      <LaunchBotStep
        botAvatarFileId={botDetail?.botAvatarFileId ?? ''}
        botName={botDetail?.botName ?? ''}
        botWalletAddress={botDetail?.botWalletAddress ?? ''}
        setStatus={setStatus}
        setStep={setStep}
        status={status}
      />
    );
  };

  return (
    <div>
      <Dialog open={true} onOpenChange={closeDialog}>
        <DialogContent
          hideClose={step === LaunchStep.LAUNCH ? status === LaunchBotStatus.SUCCESS : undefined}
          invert={status === LaunchBotStatus.SUCCESS}
        >
          <DialogTitle className="hidden" />
          <DialogDescription className="hidden" />
          {renderContent(step, status)}
        </DialogContent>
      </Dialog>
    </div>
  );
};
