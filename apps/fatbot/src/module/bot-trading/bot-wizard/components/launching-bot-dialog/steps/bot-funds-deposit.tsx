import { useTranslations } from 'next-intl';
import type { FC } from 'react';
import QRCode from 'react-qr-code';

import { SolIcon } from '@/assets/sol-icon';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { formatRichText } from '@/lib/formatters/format-rich-text';

import { BotAddressCard } from './bot-address-card';

interface Props {
  botId: string;
  botWalletAddress: string;
}

export const BotFundDeposit: FC<Props> = ({ botId, botWalletAddress }) => {
  const t = useTranslations('bot-trading.bot-launch-dialog');
  const { push } = useRouter();

  const onContinue = () => {
    push(ROUTES.BOT_TRADING.DETAIL(botId));
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex w-full flex-col gap-2">
        <h1 className="text-balance text-center text-3xl font-bold text-text-invert">
          {t.rich('deposit-bot-funds.message', formatRichText)}
        </h1>
        <p className="text-center text-md font-medium text-text-invert">
          {t.rich('deposit-bot-funds.description', formatRichText)}
        </p>
        <div className="flex items-center justify-center gap-1 tracking-wide">
          <SolIcon className="text-black/60" />
          <p className="text-center text-xs font-bold uppercase text-black/60">{t('deposit-bot-funds.network')}</p>
        </div>
      </div>
      <div className="mx-auto aspect-square size-[50vw] max-w-96 rounded-md bg-white p-2 shadow-primary sm:h-fit sm:w-3/5 sm:p-[18px] lg:w-full">
        <QRCode className="size-full" value={`solana:${botWalletAddress}`} />
      </div>

      <BotAddressCard address={botWalletAddress} label={t('deposit-bot-funds.copy-address')} />

      <Button variant="invert" onClick={onContinue}>
        {t('continue')}
      </Button>
    </div>
  );
};
