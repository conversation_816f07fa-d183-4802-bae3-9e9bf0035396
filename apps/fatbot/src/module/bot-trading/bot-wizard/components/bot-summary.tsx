/* eslint-disable no-nested-ternary -- TODO: Get rid off disgusting nested ternary expression */
'use client';

import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';

import { EditIcon } from '@/assets';
import { InfoCard } from '@/components/info-card';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useRouter } from 'next/navigation';
import type { GetBotResult } from '@/lib/api';
import { cn } from '@/lib/utils';
import { AdvancedBotTradingSettings } from '@/module/bot-trading/bot-wizard/components/advanced-bot-trading-settings/advanced-bot-trading-settings';
import { SummaryItemCard } from '@/module/bot-trading/bot-wizard/components/summary-item-card';
import { SummaryPriorityCard } from '@/module/bot-trading/bot-wizard/components/summary-priority-card';
import { SummaryWalletCard } from '@/module/bot-trading/bot-wizard/components/summary-wallet-card';
import { SUMMARY_SECTION_DATA } from '@/module/bot-trading/bot-wizard/constants';
import { isBotDraftReady } from '@/module/bot-trading/bot-wizard/utils/is-bot-draft-ready';
import { prepareBotWizardUrl } from '@/module/bot-trading/bot-wizard/utils/prepare-bot-wizard-url';
import { BotAvatar } from '@/module/bot-trading/components/bot-avatar';
import { BotDeleteSection } from '@/module/bot-trading/components/bot-delete-section';

const AVATAR_EDIT_STEP = 3;

type BotSummaryProps = Partial<
  Omit<GetBotResult, 'buyFrequencyLastResetAt' | 'remainingBuyFrequency' | 'userReadableId'>
> & {
  id: string;
};

interface Props {
  botData: BotSummaryProps;
  redirectUrl?: string;
  className?: string;
}

export const BotSummary: React.FC<Props> = ({ botData, redirectUrl, className }) => {
  const router = useRouter();
  const t = useTranslations('bot-trading.create-bot');
  const [botDraftId] = useQueryState('botDraftId');
  const botDraftReadyToLaunch = isBotDraftReady(botData);
  const isLaunchedBot = !!botData.botWalletAddress;

  const handleSummaryItemClick = (step: number) => {
    const url = prepareBotWizardUrl({
      ...(isLaunchedBot && { botId: botData.id }),
      ...(botDraftId && { botDraftId }),
      botData,
      redirectUrl,
      step,
    });

    // set full wizard URL
    router.push(url);
  };

  return (
    <div className={cn('grid items-start gap-3 md:grid-cols-2 md:gap-2 xl:grid-cols-3', className)}>
      <Card className="contents gap-4 md:flex" variant="area">
        <div className="relative flex items-center justify-center">
          {botData.avatarFileId ? <BotAvatar avatarFileId={botData.avatarFileId} className="max-h-[160px]" /> : null}
          <Button
            className="absolute -bottom-2 shadow-primary"
            size="icon"
            variant="secondary"
            onClick={() => {
              handleSummaryItemClick(AVATAR_EDIT_STEP);
            }}
          >
            <EditIcon className="size-3" />
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center gap-0.5">
          <div className="text-display-m font-bold">{botDraftId ? t('bot-summary.title') : t('bot-setting.title')}</div>
          <div className="text-md font-semibold text-text-secondary">{t('bot-summary.description')}</div>
        </div>

        {botDraftId ? (
          botDraftReadyToLaunch ? (
            <InfoCard title={t('bot-summary.success-caption')} variant="success" />
          ) : (
            <InfoCard title={t('bot-summary.warning-caption')} variant="info" />
          )
        ) : null}

        <div className="flex flex-col gap-2">
          <div className="text-display-xs font-bold">{t('bot-summary.basics')}</div>
          {SUMMARY_SECTION_DATA.basics.map((item) => (
            <div
              key={item.value}
              className="cursor-pointer"
              onClick={() => {
                handleSummaryItemClick(item.step);
              }}
            >
              <SummaryItemCard key={item.value} {...item} value={botData[item.value]} />
            </div>
          ))}
          {botData.botWalletAddress && botData.botWalletBalanceUsd ? (
            <SummaryWalletCard walletAddress={botData.botWalletAddress} walletBalance={botData.botWalletBalanceUsd} />
          ) : null}
        </div>
      </Card>

      <div className="flex flex-col gap-2">
        <Card className="flex gap-2" variant="area">
          <h3 className="text-display-xs font-bold">{t('bot-summary.strategy')}</h3>
          {SUMMARY_SECTION_DATA.strategy.map((item) => (
            <div
              key={item.value}
              className="cursor-pointer"
              onClick={() => {
                handleSummaryItemClick(item.step);
              }}
            >
              <SummaryItemCard {...item} value={botData[item.value]} />
            </div>
          ))}
          {botDraftId ? <SummaryPriorityCard className="hidden sm:flex" /> : null}
        </Card>

        {isLaunchedBot ? (
          <AdvancedBotTradingSettings
            botId={botData.id}
            settings={{
              tokenTickerCopyIsChecked: botData.tokenTickerCopyIsChecked ?? false,
              creatorHighBuyIsChecked: botData.creatorHighBuyIsChecked ?? false,
              bundledBuysDetectedIsChecked: botData.bundledBuysDetectedIsChecked ?? false,
              suspiciousWalletsDetectedIsChecked: botData.suspiciousWalletsDetectedIsChecked ?? false,
              shouldWaitBeforeBuying: botData.shouldWaitBeforeBuying ?? false,
              shouldAutoSellAfterHoldTime: botData.shouldAutoSellAfterHoldTime ?? false,
              singleHighBuyIsChecked: botData.singleHighBuyIsChecked ?? false,
            }}
          />
        ) : null}
      </div>

      <Card className="flex gap-2" variant="area">
        <div className="flex items-center justify-between">
          <h3 className="text-display-xs font-bold">{t('bot-summary.targets')}</h3>
          <div className="rounded-full bg-primary-alphaLight px-1.5 py-0.5 text-xxs font-semibold uppercase text-text-active">
            {t('optional.title')}
          </div>
        </div>
        {SUMMARY_SECTION_DATA.targets.map((item) => (
          <div
            key={`${item.valueFrom}-${item.valueTo}`}
            className="cursor-pointer"
            onClick={() => {
              handleSummaryItemClick(item.step);
            }}
          >
            <SummaryItemCard {...item} valueFrom={botData[item.valueFrom]} valueTo={botData[item.valueTo]} />
          </div>
        ))}
        {botData.id ? <SummaryPriorityCard className="flex sm:hidden" /> : null}

        <BotDeleteSection botId={botData.id} />
      </Card>
    </div>
  );
};
