'use client';

import { useSearchParams } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { useMemo } from 'react';

import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { DATA_NOT_SAVED_STEP_BOT, DATA_NOT_SAVED_STEP_DRAFT } from '@/module/bot-trading/bot-wizard/constants';
import { useBotWizardStep } from '@/module/bot-trading/bot-wizard/hooks/use-bot-wizard-step';
import { getWizardTotalSteps } from '@/module/bot-trading/bot-wizard/step-content';

export const useExitWizard = () => {
  const router = useRouter();
  const redirectUrl = useSearchParams().get('redirectUrl');
  const { currentStep, setStep } = useBotWizardStep();
  const [botId] = useQueryState('botId');

  const dataNotSavedStep = useMemo(() => (botId ? DATA_NOT_SAVED_STEP_BOT : DATA_NOT_SAVED_STEP_DRAFT), [botId]);

  const exitToDashboard = () => {
    router.push(ROUTES.BOT_TRADING.ROOT);
  };

  const handleExitWizard = () => {
    if (redirectUrl) {
      router.push(redirectUrl);
      return;
    }

    const noDataToSave = currentStep <= dataNotSavedStep;
    const isLastStep = currentStep === getWizardTotalSteps();
    const shouldReturnToDashboard = noDataToSave || isLastStep;

    if (shouldReturnToDashboard) {
      exitToDashboard();
      return;
    }

    const lastStep = getWizardTotalSteps();
    setStep(lastStep);
  };

  return { handleExitWizard, exitToDashboard };
};
