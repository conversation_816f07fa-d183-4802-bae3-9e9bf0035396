/* eslint-disable no-magic-numbers */
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { useGetBotSettingsStatistics } from '@/lib/api';
import { useExitWizard } from '@/module/bot-trading/bot-wizard/hooks/use-exit-wizard';
import { validatePositiveSliderValues } from '@/module/bot-trading/bot-wizard/utils/slider-values-utils';

import { useBotWizardApi } from './use-bot-wizard-api';
import { useBotWizardFormData } from './use-bot-wizard-form-data';
import { useBotWizardStep } from './use-bot-wizard-step';

interface Step10FormData {
  stopLossFraction: string[];
}

// left value is PERCENTAGE so we need to divide by 100
const STOP_LOSS_SMALL_FROM = 0;
const STOP_LOSS_SMALL_TO = 1 / 100;
const STOP_LOSS_NORMAL_FROM = 1 / 100;
const STOP_LOSS_NORMAL_TO = 6 / 100;
const STOP_LOSS_LARGE_FROM = 6 / 100;
const STOP_LOSS_LARGE_TO = 100 / 100;
const MAX_STOP_LOSS_VALUE = 1;

export const useTradeStopLossForm = () => {
  const t = useTranslations('bot-trading.create-bot.step-10');
  const { next } = useBotWizardStep();
  const [formData, setFormData] = useBotWizardFormData();
  const { data, isLoading } = useGetBotSettingsStatistics();
  const { apiUpdateBot } = useBotWizardApi();
  const { handleExitWizard } = useExitWizard();

  const stopLossValidationConfig = {
    smallAmount: {
      from: STOP_LOSS_SMALL_FROM,
      to: STOP_LOSS_SMALL_TO,
      message: t('amount-too-small'),
    },
    normalAmount: {
      from: STOP_LOSS_NORMAL_FROM,
      to: STOP_LOSS_NORMAL_TO,
      message: t('amount-normal'),
    },
    largeAmount: {
      from: STOP_LOSS_LARGE_FROM,
      to: STOP_LOSS_LARGE_TO,
      message: t('amount-too-large'),
    },
  };

  const schema = z.object({
    stopLossFraction: z
      .array(z.string())
      .refine((array) => array.length > 0, {
        message: t('validation-required'),
        path: ['[0]'],
      })
      .refine((value) => parseFloat(value[0] ?? '') <= MAX_STOP_LOSS_VALUE, {
        message: t('validation-max-stop-loss'),
        path: ['[0]'],
      })
      .refine(validatePositiveSliderValues, {
        path: ['[0]'],
        message: t('validation'),
      }),
  });

  const form = useForm<Step10FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      stopLossFraction: [formData.stopLossFraction],
    },
  });

  const error = form.formState.errors.stopLossFraction?.[0]?.message;

  const saveData = async (values: Step10FormData) => {
    await setFormData((preValue) => ({
      ...preValue,
      stopLossFraction: values.stopLossFraction[0],
    }));
    await apiUpdateBot({
      stopLossFraction: values.stopLossFraction[0],
    });
  };

  const handleNextStep = async (values: Step10FormData) => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }
    await saveData(values);
    await next();
  };

  const handleExit = async (values: Step10FormData) => {
    const isValid = await form.trigger();
    if (!isValid) {
      return;
    }
    await saveData(values);
    handleExitWizard();
  };

  return {
    form,
    handleNextStep,
    handleExit,
    stopLossData: data?.stopLoss ?? [],
    stopLossValidationConfig,
    isLoading,
    error,
  };
};
