'use client';

import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { parseAsBoolean, parseAsString, useQueryStates } from 'nuqs';
import React from 'react';

import { BOT_DETAIL_LISTS_SIZE } from '@/api/constants';
import { useTimeRangeOptions } from '@/components/charts/area-chart/use-options';
import { ErrorMessage } from '@/components/error-message';
import { Card } from '@/components/ui/card';
import { Loader } from '@/components/ui/loader';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ROUTES } from '@/constants/routes';
import { usePathname, useRouter } from 'next/navigation';
import { type TimeRange, useSearchBotMarketPositionsInfinite } from '@/lib/api';
import { getErrorMessage } from '@/lib/get-error-message';
import { isNativeToken } from '@/module/assets/utils';
import { BotTradeRow } from '@/module/bot-trading/components/bot-trade-row';
import { TradeTabs } from '@/module/bot-trading/components/trades/trade-tabs';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';
import { parseTimeRange } from '@/module/bot-trading/utils/timerange-utils';

export const BotTradesMobile: React.FC = () => {
  const t = useTranslations('bot-trading.bot-trades');
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [{ timeRange, searchString, botId, isBotMarketPositionActive }, setQueryState] = useQueryStates({
    timeRange: {
      parse: parseTimeRange,
      defaultValue: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
    },
    searchString: parseAsString,
    botId: parseAsString,
    isBotMarketPositionActive: parseAsBoolean.withDefault(true),
  });

  const {
    data,
    error,
    isLoading,
    refetch: refetchTrades,
  } = useSearchBotMarketPositionsInfinite(
    botId ?? '',
    {
      timeRange,
      isBotMarketPositionActive,
      searchString: searchString ?? undefined,
    },
    { size: BOT_DETAIL_LISTS_SIZE },
    {
      query: {
        enabled: !!botId,
        getNextPageParam: (lastPage) => (lastPage.hasMore ? lastPage.lastId?.toString() : undefined),
      },
    },
  );

  const allRows = data?.pages.flatMap((page) => page.content) ?? [];

  const hasLoadingError = !!error;
  const hasData = !!allRows.length;

  const timeRangeOptions = useTimeRangeOptions();

  return (
    <Card className="flex w-full flex-col gap-y-2">
      <TradeTabs />

      <Tabs
        defaultValue={timeRange}
        onValueChange={(value) => {
          void setQueryState({ timeRange: value as TimeRange });
        }}
      >
        <TabsList className="flex w-auto border">
          {timeRangeOptions.map(({ key, value }) => (
            <TabsTrigger
              key={key}
              className="min-w-5 px-2 py-0.5 text-md font-medium data-[state=active]:font-bold"
              value={key}
            >
              {value}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      {isLoading ? <Loader /> : null}

      {hasLoadingError ? (
        <ErrorMessage className="text-left" variant="error">
          {getErrorMessage(error) ?? t('errors.loading-failed')}
        </ErrorMessage>
      ) : null}

      {!hasLoadingError && !hasData && !isLoading ? (
        <ErrorMessage className="text-left">{t('no-more-tokens-available')}</ErrorMessage>
      ) : null}

      {hasData && botId ? (
        <div className="grid grid-cols-1 gap-2">
          {allRows.map((item) => (
            <BotTradeRow
              key={item.id}
              botId={botId}
              botTradeId={item.id}
              chain={item.tokenChain}
              currentValueChangeFraction={item.pnlAmountFraction}
              currentValueChangeUsd={item.pnlAmountUsd}
              endTime={item.closedTimeStampAt}
              image={item.tokenImageUrl ?? ''}
              startTime={item.openTimeStampAt}
              startValueUsd={item.currentValueUsd}
              state={item.state}
              tokenDetailUrl={item.tokenDetailUrl}
              tokenName={item.tokenName}
              tokenSymbol={item.tokenSymbol}
              onClick={
                !isNativeToken(item)
                  ? () => {
                      router.push(
                        ROUTES.BOT_TRADING.BOT_TRADE_DETAIL(
                          botId,
                          item.id,
                          item.tokenChain,
                          item.tokenAddress,
                          pathname + '?' + searchParams.toString(),
                        ),
                      );
                    }
                  : undefined
              }
              onSellClick={refetchTrades}
            />
          ))}
        </div>
      ) : null}
    </Card>
  );
};
