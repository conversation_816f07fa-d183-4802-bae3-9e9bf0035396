'use client';

import { useSearchPara<PERSON> , usePathname, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { parseAsString, useQueryStates } from 'nuqs';

import { BOT_DETAIL_LISTS_SIZE } from '@/api/constants';
import { useTimeRangeOptions } from '@/components/charts/area-chart/use-options';
import { ErrorMessage } from '@/components/error-message';
import { Card } from '@/components/ui/card';
import { Loader } from '@/components/ui/loader';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ROUTES } from '@/constants/routes';
import { type TimeRange, useSearchBotMarketPositionsInfinite } from '@/lib/api';
import { getErrorMessage } from '@/lib/get-error-message';
import { isNativeToken } from '@/module/assets/utils';
import { sortBotTrades } from '@/module/bot-trading/bot-trades/utils/sort-bot-trades';
import { BotTradeRow } from '@/module/bot-trading/components/bot-trade-row';
import { BOT_TRADING_DEFAULT_TIMERANGE_VALUE } from '@/module/bot-trading/config';
import { parseTimeRange } from '@/module/bot-trading/utils/timerange-utils';

export const BotTradesClosed = () => {
  const t = useTranslations('bot-trading.bot-trades');
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [{ timeRange, searchString, botId }, setQueryState] = useQueryStates({
    timeRange: {
      parse: parseTimeRange,
      defaultValue: BOT_TRADING_DEFAULT_TIMERANGE_VALUE,
    },
    searchString: parseAsString,
    botId: parseAsString,
  });

  const {
    data = [],
    error,
    isLoading,
  } = useSearchBotMarketPositionsInfinite(
    botId ?? '',
    {
      timeRange,
      isBotMarketPositionActive: false,
      searchString: searchString ?? undefined,
    },
    { size: BOT_DETAIL_LISTS_SIZE },
    {
      query: {
        enabled: !!botId,
        getNextPageParam: (lastPage) => (lastPage.hasMore ? lastPage.lastId?.toString() : undefined),
        select: sortBotTrades,
      },
    },
  );

  const hasLoadingError = !!error;
  const hasData = !!data.length;

  const timeRangeOptions = useTimeRangeOptions();

  return (
    <Card className="flex w-full flex-col gap-y-3">
      <div className="flex min-h-5 items-center justify-between">
        <span className="text-display-xs font-bold">{t('closed')}</span>
        <Tabs defaultValue={timeRange} onValueChange={(value) => void setQueryState({ timeRange: value as TimeRange })}>
          <TabsList className="flex w-auto border">
            {timeRangeOptions.map(({ key, value }) => (
              <TabsTrigger
                key={key}
                className="min-w-8 px-2 py-0.5 text-md font-medium data-[state=active]:font-bold"
                value={key}
              >
                {value}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {isLoading ? <Loader /> : null}

      {hasLoadingError ? (
        <ErrorMessage className="text-left" variant="error">
          {getErrorMessage(error) ?? t('errors.loading-failed')}
        </ErrorMessage>
      ) : null}

      {!hasLoadingError && !hasData && !isLoading ? (
        <ErrorMessage className="text-left">{t('no-bot-trades-found')}</ErrorMessage>
      ) : null}

      {hasData && botId ? (
        <div className="scrollbar-hidden grid h-[80dvh] max-h-[400px] grid-cols-1 place-content-start gap-2 overflow-y-auto">
          {data.map((item) => (
            <BotTradeRow
              key={item.id}
              botId={botId}
              botTradeId={item.id}
              chain={item.tokenChain}
              currentValueChangeFraction={item.pnlAmountFraction}
              currentValueChangeUsd={item.pnlAmountUsd}
              endTime={item.closedTimeStampAt}
              endValueUsd={item.closeValueUsd}
              image={item.tokenImageUrl ?? ''}
              startTime={item.openTimeStampAt}
              startValueUsd={item.openValueUsd}
              state={item.state}
              tokenDetailUrl={item.tokenDetailUrl}
              tokenName={item.tokenName}
              tokenSymbol={item.tokenSymbol}
              onClick={
                !isNativeToken(item)
                  ? () => {
                      router.push(
                        ROUTES.BOT_TRADING.BOT_TRADE_DETAIL(
                          botId,
                          item.id,
                          item.tokenChain,
                          item.tokenAddress,
                          pathname + '?' + searchParams.toString(),
                        ),
                      );
                    }
                  : undefined
              }
            />
          ))}
        </div>
      ) : null}
    </Card>
  );
};
