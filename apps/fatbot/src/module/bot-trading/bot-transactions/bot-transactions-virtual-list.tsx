'use client';

import { useVirtualizer } from '@tanstack/react-virtual';
import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';
import { useEffect, useRef } from 'react';
import { useDebounceValue } from 'usehooks-ts';

import { DEFAULT_OVERSCAN, DEFAULT_PAGINATION_SIZE } from '@/api/constants';
import { EndOfListMessage } from '@/components/data-list/messages/end-of-list-message';
import { LoadingDataMessage } from '@/components/data-list/messages/loading-data-message';
import { NoDataMessage } from '@/components/data-list/messages/no-data-message';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { useSearchUserBotsTransactionsInfinite } from '@/lib/api';
import { BotTransactionsRow } from '@/module/bot-trading/bot-transactions/bot-transactions-row';

import { BotTransactionsListHeader } from './bot-transactions-list-header';

const ROW_GAP = 16;
const ROW_HEIGHT = 62;
const DEFAULT_DEBOUNCE_MS = 500;

export const BotTransactionsVirtualList: React.FC = () => {
  const t = useTranslations('bot-trading.bot-transactions');
  const router = useRouter();

  const [selectedBotId] = useQueryState('botId');
  const [searchString] = useQueryState('searchString');
  const [searchStringDebounced] = useDebounceValue(searchString, DEFAULT_DEBOUNCE_MS);
  const parentRef = useRef(null);

  const { data, fetchNextPage, isFetchingNextPage, hasNextPage, status, isLoading } =
    useSearchUserBotsTransactionsInfinite(
      {
        botId: selectedBotId || '',
        searchString: searchStringDebounced || undefined,
      },
      {
        size: DEFAULT_PAGINATION_SIZE,
      },
      {
        query: {
          enabled: !!selectedBotId,
          refetchOnWindowFocus: true,
          getNextPageParam: (lastPage) => (lastPage.hasMore ? lastPage.lastId?.toString() : undefined),
        },
      },
    );

  const allRows = data?.pages.flatMap((page) => page.content) ?? [];

  const rowVirtualizer = useVirtualizer({
    count: allRows.length + (hasNextPage ? 1 : 0),
    estimateSize: () => ROW_HEIGHT + ROW_GAP,
    getScrollElement: () => parentRef.current,
    measureElement: (element) => {
      const naturalHeight = element.children[0]?.getBoundingClientRect().height ?? 0;
      return naturalHeight + ROW_GAP;
    },
    overscan: DEFAULT_OVERSCAN,
  });

  const virtualItems = rowVirtualizer.getVirtualItems();

  useEffect(() => {
    const [lastItem] = virtualItems.toReversed();

    if ((lastItem?.index ?? 0) >= allRows.length - 1 && hasNextPage && !isFetchingNextPage) {
      void fetchNextPage();
    }
  }, [hasNextPage, fetchNextPage, allRows.length, isFetchingNextPage, virtualItems]);

  const fullListLoaded = !hasNextPage && !isLoading;

  return (
    <div className="flex h-full flex-col">
      {status === 'success' ? (
        <div ref={parentRef} className="-mr-1.5 h-full overflow-auto pr-1.5">
          <div className="sticky top-0 z-10 hidden bg-surface-area py-2 md:flex">
            <BotTransactionsListHeader />
          </div>

          <div
            className="relative transition-transform duration-75"
            style={{ height: `${rowVirtualizer.getTotalSize()}px` }}
          >
            {virtualItems.map((virtualRow) => {
              const isLoaderRow = virtualRow.index > allRows.length - 1;
              const tx = allRows[virtualRow.index];

              if (!tx) return null;

              const tokenAddress = !isLoaderRow && tx.tokenAddress;

              return (
                <div
                  key={virtualRow.index}
                  ref={rowVirtualizer.measureElement}
                  className="absolute left-0 top-0 w-full pb-0 transition-transform-ease"
                  data-index={virtualRow.index}
                  style={{
                    transform: `translateY(${virtualRow.start}px)`,
                  }}
                >
                  {isLoaderRow && hasNextPage ? (
                    <LoadingDataMessage className="m-2" message={t('loading-transactions')} />
                  ) : (
                    <BotTransactionsRow
                      key={tx.txId}
                      chain={tx.txChain}
                      containerClassName="md:grid-cols-[5fr_3fr_4fr] lg:grid-cols-[5fr_3fr_4fr]"
                      createdAt={tx.txCreatedAt}
                      explorerUrl={tx.txDetailUrl}
                      tokenAmount={tx.nativeAmount}
                      tokenName={tx.tokenName ?? ''}
                      tokenSymbol={tx.tokenSymbol ?? ''}
                      totalWethAmountUsd={tx.amountUsd}
                      transactionStatus={tx.txStatus}
                      transactionType={tx.txType}
                      {...(tokenAddress && {
                        onClick: () => {
                          router.push(ROUTES.MANUAL_TRADING.TOKEN_DETAIL(tokenAddress, tx.txChain));
                        },
                      })}
                    />
                  )}
                </div>
              );
            })}
          </div>
          {fullListLoaded ? (
            <>{virtualItems.length > 0 ? <EndOfListMessage /> : <NoDataMessage message={t('no-transactions')} />}</>
          ) : null}
        </div>
      ) : null}
    </div>
  );
};
