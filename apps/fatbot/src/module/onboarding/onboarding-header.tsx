import { SearchIcon } from 'lucide-react';
import Link from 'next/link';
import { getTranslations } from 'next-intl/server';

import { Logo } from '@/assets';
import { BotTrading } from '@/assets/bot-trading';
import { Home } from '@/assets/home';
import { ManualTrading } from '@/assets/manual-trading';
import { Sniping2Point0 } from '@/assets/sniping-2-point-0';
import { Trophy01 } from '@/assets/trophy-01';
import { User } from '@/assets/user';
import { Navigation } from '@/components/navigation';
import { ROUTES } from '@/constants/routes';

export const OnboardingHeader = async ({
  leagueSystemEnabled,
}: {
  leagueSystemEnabled?: boolean;
}) => {
  const t = await getTranslations('navigation');

  const menu = leagueSystemEnabled
    ? [
        {
          href: ROUTES.HOME,
          text: t('home'),
          icon: <Home />,
        },
        {
          href: ROUTES.MANUAL_TRADING.ROOT,
          text: t('fat-screener'),
          icon: <SearchIcon />,
        },
        {
          href: ROUTES.BOT_TRADING.ROOT,
          text: t('sniping-2-point-0'),
          icon: <Sniping2Point0 />,
        },
        {
          href: ROUTES.LEAGUE.ROOT,
          text: t('league'),
          icon: <Trophy01 />,
        },
      ]
    : [
        {
          href: ROUTES.HOME,
          text: t('home'),
          icon: <Home />,
        },
        {
          href: ROUTES.MANUAL_TRADING.ROOT,
          text: t('manual-trading'),
          icon: <ManualTrading />,
        },
        {
          href: ROUTES.BOT_TRADING.ROOT,
          text: t('bot-trading'),
          icon: <BotTrading />,
        },
      ];

  return (
    <div className="grid grid-cols-2 items-center py-2 sm:px-3 sm:pb-3 sm:pt-4 md:grid-cols-[1fr_auto_1fr]">
      <Logo className="size-5 text-primary" />
      <Navigation menu={menu} />
      <div className="flex items-center justify-end gap-3">
        <Link
          className="shrink-0"
          data-testid="user-profile-icon"
          href={ROUTES.SIGN_UP}
        >
          <div className="rounded-full border-2 border-border-subtle p-1.5">
            <User />
          </div>
        </Link>
      </div>
    </div>
  );
};
