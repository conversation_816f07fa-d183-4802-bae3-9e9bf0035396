'use client';

import { useState } from 'react';

import { hideOnboarding } from '@/api/actions';
import { Dialog, DialogContent, DialogDescription, DialogTitle } from '@/components/ui/dialog';
import { useRouter } from 'next/navigation';
import { OnboardingStep } from '@/module/onboarding/onboarding-step-schema';

import { OnboardingStep1 } from './onboarding-step-1';
import { OnboardingStep2 } from './onboarding-step-2';
import { OnboardingStep3 } from './onboarding-step-3';

export const OnboardingDialog = () => {
  const router = useRouter();

  const [step, setStep] = useState(OnboardingStep.TRADE_SMART_WAY);

  const onSkip = () => {
    void hideOnboarding();
    router.refresh();
  };

  const stepContent: Record<OnboardingStep, React.ReactNode> = {
    [OnboardingStep.TRADE_SMART_WAY]: (
      <OnboardingStep1
        onContinue={() => {
          setStep(OnboardingStep.MANUAL_TRADING);
        }}
        onSkip={onSkip}
      />
    ),
    [OnboardingStep.MANUAL_TRADING]: (
      <OnboardingStep2
        onBack={() => {
          setStep(OnboardingStep.TRADE_SMART_WAY);
        }}
        onContinue={() => {
          setStep(OnboardingStep.AUTOMATED_BOTS);
        }}
        onSkip={onSkip}
      />
    ),
    [OnboardingStep.AUTOMATED_BOTS]: (
      <OnboardingStep3
        onBack={() => {
          setStep(OnboardingStep.MANUAL_TRADING);
        }}
        onSkip={onSkip}
      />
    ),
  };

  return (
    <Dialog open={true} onOpenChange={onSkip}>
      <DialogTitle />
      <DialogDescription />
      <DialogContent className="p-0 sm:max-w-fit sm:bg-transparent sm:p-0" hideClose={true}>
        {stepContent[step]}
      </DialogContent>
    </Dialog>
  );
};
