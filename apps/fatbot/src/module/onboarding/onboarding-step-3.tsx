import { useTranslations } from 'next-intl';

import { hideOnboarding } from '@/api/actions';
import { OnboardingAnimationStep3 } from '@/components/animations/onboarding-step-3';
import { BackButton } from '@/components/back-button';
import { Button } from '@/components/ui/button';
import Display from '@/components/ui/display';
import { useRouter } from 'next/navigation';
import { AuthCard } from '@/module/auth/auth-card';
import AuthCardHeader from '@/module/auth/auth-card-header';
import { OnboardingStepCounter } from '@/module/onboarding/onboarding-step-counter';

export const OnboardingStep3 = ({ onBack, onSkip }: { onBack: () => void; onSkip: () => void }) => {
  const t = useTranslations('onboarding');
  const router = useRouter();

  return (
    <AuthCard className="flex h-dvh gap-4 px-[28px] sm:h-auto">
      <AuthCardHeader onSkip={onSkip}>
        <BackButton onClick={onBack} />
      </AuthCardHeader>

      <div className="-mx-10 flex flex-1 items-center justify-center">
        <OnboardingAnimationStep3 className="h-full w-[430px] max-w-[100vw] object-cover sm:w-[420px]" />
      </div>

      <div className="flex flex-col gap-3 sm:gap-5">
        <Display className="text-center" size="M" weight="bold">
          {t('step-3.title')}
        </Display>
        <p className="text-center font-semibold text-text-secondary">{t('step-3.description')}</p>
        <OnboardingStepCounter currentStepIndex={2} />

        <Button
          className="w-full"
          onClick={() => {
            void hideOnboarding();
            router.refresh();
          }}
        >
          {t('continue')}
        </Button>
      </div>
    </AuthCard>
  );
};
