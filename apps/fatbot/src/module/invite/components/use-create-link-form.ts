import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { checkReferralCode, createReferralC<PERSON>, getGetUserQueryKey } from '@/lib/api';
import { getErrorMessage } from '@/lib/get-error-message';
import { getQueryClient } from '@/lib/query-client';

const MIN_LENGTH = 4;
const MAX_LENGTH = 20;
const REGEX = /^[a-zA-Z0-9]+$/;

const useSchema = () => {
  const t = useTranslations('invite.validation-messages');

  return z.object({
    referralCode: z
      .string()
      .min(1, { message: t('required') })
      .min(MIN_LENGTH, { message: t('min-length') })
      .max(MAX_LENGTH, { message: t('max-length') })
      .regex(REGEX, {
        message: t('format'),
      }),
  });
};

type Schema = z.infer<ReturnType<typeof useSchema>>;

export const useCreateInviteLinkDialogForm = () => {
  const t = useTranslations('invite.validation-messages');
  const queryClient = getQueryClient();
  const router = useRouter();
  const schema = useSchema();
  const form = useForm<Schema>({
    resolver: zodResolver(schema),
    defaultValues: {
      referralCode: '',
    },
  });

  const onSubmit = async ({ referralCode }: Schema) => {
    try {
      const { isUnique } = await checkReferralCode({
        code: referralCode,
      });

      if (!isUnique) {
        form.setError('root.serverError', { message: t('already-exists') });

        return;
      }

      await createReferralCode({ referralCode });

      await queryClient.invalidateQueries({
        queryKey: getGetUserQueryKey(),
      });

      router.push(ROUTES.PROFILE.REFERRAL.INVITE);
    } catch (error) {
      form.setError('root.serverError', {
        message: getErrorMessage(error) ?? 'Referral code probably already exists.',
      });
    }
  };

  return {
    onSubmit,
    form,
  };
};
