'use client';

import emojiRegex from 'emoji-regex';
import { useTranslations } from 'next-intl';
import { type ChangeEvent } from 'react';

import { ErrorMessage } from '@/components/error-message';
import { Form, FormControl, FormField, FormItem } from '@/components/form/form';
import { Input } from '@/components/form/input';
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { REFERRAL_CODE_COOKIE_NAME } from '@/constants/config';
import { ROUTES } from '@/constants/routes';
import { env } from '@/env/client';
import { useRouter } from 'next/navigation';
import { useCreateInviteLinkDialogForm } from '@/module/invite/components/use-create-link-form';

const regex = emojiRegex();

export const CreateInviteLinkDialog = () => {
  const t = useTranslations('invite');
  const router = useRouter();

  const { form, onSubmit } = useCreateInviteLinkDialogForm();

  return (
    <AlertDialog
      open={true}
      onOpenChange={() => {
        router.push(ROUTES.PROFILE.REFERRAL.ROOT);
      }}
    >
      <AlertDialogContent className="bg-surface-primary">
        <AlertDialogHeader>
          <AlertDialogTitle>
            <span className="text-xl font-semibold leading-20 text-text-primary">{t('create-invite-link')}</span>
          </AlertDialogTitle>
        </AlertDialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex flex-col gap-3 rounded-md bg-surface-third-layer text-text-secondary drop-shadow-md">
              <div className="flex items-center gap-1 rounded-md px-2 py-1 drop-shadow-md">
                <div className="flex flex-col">
                  <span className="text-xs font-medium leading-12">{t('invite-link')}</span>
                  <span className="break-all text-md font-semibold">
                    {`${env.NEXT_PUBLIC_ACTION_URL}?${REFERRAL_CODE_COOKIE_NAME}=`}
                    <span className="text-white">
                      <FormField
                        control={form.control}
                        name="referralCode"
                        render={({ field }) => {
                          const onChange = (event: ChangeEvent<HTMLInputElement>) => {
                            const valueWithoutEmojis = event.target.value.replace(regex, '');

                            field.onChange(valueWithoutEmojis);
                          };

                          return (
                            <FormItem>
                              <FormControl>
                                <Input className="inline h-auto rounded-xs" {...field} onChange={onChange} />
                              </FormControl>
                            </FormItem>
                          );
                        }}
                      />
                    </span>
                  </span>
                </div>
              </div>
            </div>

            <div className="my-1 text-xs font-medium leading-12 text-text-secondary">{t('warning')}</div>

            {form.formState.errors.referralCode ? (
              <ErrorMessage className="my-1 text-left text-event-error">
                {form.formState.errors.referralCode.message}
              </ErrorMessage>
            ) : null}
            {form.formState.errors.root?.serverError ? (
              <ErrorMessage className="my-1 text-left text-event-error">
                {form.formState.errors.root.serverError.message}
              </ErrorMessage>
            ) : null}

            <Button className="w-full" disabled={form.formState.isSubmitting}>
              {t('cta')}
            </Button>
          </form>
        </Form>
      </AlertDialogContent>
    </AlertDialog>
  );
};
