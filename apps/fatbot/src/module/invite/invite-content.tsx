'use client';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@/components/ui/dialog';
import { Loader } from '@/components/ui/loader';
import { REFERRAL_CODE_COOKIE_NAME } from '@/constants/config';
import { env } from '@/env/client';
import { useRouter } from 'next/navigation';
import { useGetUser } from '@/lib/api';
import { Caption } from '@/module/invite/components/caption';
import { CreateInviteLinkDialog } from '@/module/invite/components/create-invite-link-dialog';
import { Heading } from '@/module/invite/components/heading';
import { Qr } from '@/module/invite/components/qr';
import { ReferralLinkCard } from '@/module/invite/components/referral-link-card';
import { Share } from '@/module/invite/components/share';
import { getPreviousRoute } from '@/utils/session-storage';

export const InviteContent = () => {
  const { push } = useRouter();
  const { data: userInfo, isLoading } = useGetUser();

  if (isLoading) {
    return <Loader />;
  }

  if (!userInfo?.referralCode) {
    return <CreateInviteLinkDialog />;
  }

  const referralCode = userInfo.referralCode;
  const link = `${env.NEXT_PUBLIC_ACTION_URL}?${REFERRAL_CODE_COOKIE_NAME}=${referralCode}`;

  return (
    <Dialog
      open={true}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          push(getPreviousRoute());
        }
      }}
    >
      <DialogTitle className="hidden" />
      <DialogDescription className="hidden" />
      <DialogContent className="gap-4 pt-2" hideClose={true} invert={true}>
        <div className="flex flex-col gap-y-2">
          <Heading />
          <Caption />
        </div>
        <Qr referralCode={referralCode} />
        <div className="flex flex-col gap-y-3">
          <ReferralLinkCard link={link} />
          <Share link={link} />
        </div>
      </DialogContent>
    </Dialog>
  );
};
