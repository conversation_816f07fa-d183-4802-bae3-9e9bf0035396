'use client';
import { useTranslations } from 'next-intl';

import { Mail } from '@/assets';
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';

interface Props {
  email: string;
  open: boolean;
  onOpenChange: (value: boolean) => void;
}

export const ChangeEmailDialog = ({ email, open, onOpenChange }: Props) => {
  const t = useTranslations('profile.settings');
  const router = useRouter();
  const onClose = () => {
    onOpenChange(false);
  };

  const handleResetPassword = (event: React.MouseEvent<HTMLAnchorElement>) => {
    event.preventDefault();
    router.push(ROUTES.RESET_PASSWORD);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="gap-5 rounded-lg pt-8 sm:gap-[29px] sm:bg-surface-primary">
        <AlertDialogHeader className="text-left font-bold">
          <AlertDialogTitle className="text-5xl leading-48 sm:text-xl">{t('title')}</AlertDialogTitle>
        </AlertDialogHeader>

        <div className="flex items-center gap-1 rounded-md bg-surface-third-layer px-2 py-1 drop-shadow-md">
          <Mail className="text-text-secondary" />
          <div className="flex flex-col">
            <span className="text-xs font-medium text-text-secondary">{t('email-address')}</span>
            <span className="text-md font-semibold">{email}</span>
          </div>
        </div>

        <div className="flex flex-col justify-center text-center">
          <span className="text-sm font-medium text-text-secondary">{t('need-to-change')}</span>

          <span className="cursor-pointer text-sm font-semibold text-text-active" onClick={handleResetPassword}>
            {t('reset-it')}
          </span>
        </div>

        <Button className="mb-[19px] mt-auto sm:my-auto" onClick={onClose}>
          {t('cta')}
        </Button>
      </AlertDialogContent>
    </AlertDialog>
  );
};
