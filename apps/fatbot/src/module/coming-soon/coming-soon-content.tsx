'use client';

import type { StaticImageData } from 'next/image';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';

import { AutomatedBots } from '@/assets/images';
import { useRouter } from 'next/navigation';

const REFETCH_INTERVAL = 30_000;

export const ComingSoonContent: React.FC = () => {
  const t = useTranslations('coming-soon');
  const router = useRouter();

  useEffect(() => {
    const interval = setInterval(() => {
      router.refresh();
    }, REFETCH_INTERVAL);

    return () => {
      clearInterval(interval);
    };
  }, [router]);

  return (
    <div className="relative flex h-screen flex-col items-center justify-center overflow-hidden bg-surface-background text-center">
      <div className="absolute bottom-[15%] left-[40%] z-0 size-[250vh] rounded-full bg-primary md:-bottom-1/4 md:left-[70%]" />
      <div className="z-10 grid gap-2 sm:gap-4">
        <Image alt="FatBot" className="h-36 sm:h-60" src={AutomatedBots as StaticImageData} />
        <div className="flex flex-col gap-0">
          <h1 className="text-display-l font-extrabold uppercase sm:text-display-xl">{t('title')}</h1>
          <p className="m-auto max-w-80 text-md font-semibold text-text-secondary sm:text-xl">{t('description')}</p>
        </div>
      </div>
    </div>
  );
};
