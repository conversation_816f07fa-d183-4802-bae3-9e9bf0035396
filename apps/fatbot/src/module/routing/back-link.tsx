'use client';

import Link from 'next/link';
import type { SyntheticEvent } from 'react';

import { ArrowLeft } from '@/assets';
import { Button, type ButtonProps } from '@/components/ui/button';
import { getValidRedirectUrl } from '@/module/routing/get-valid-redirect-url';

import { useRedirectUrl } from './use-redirect-url';

type Props = Omit<ButtonProps, 'asChild' | 'size' | 'type'> & {
  defaultUrl: string;
  onClick?: () => void;
};

export const BackLink: React.FC<Props> = ({
  defaultUrl,
  className,
  onClick,
  variant = 'secondary',
}) => {
  const redirectUrl = useRedirectUrl();

  const resultLink =
    redirectUrl.length > 0 ? getValidRedirectUrl(redirectUrl) : defaultUrl;

  const handleClick = (event: SyntheticEvent<HTMLButtonElement>) => {
    if (onClick) {
      event.preventDefault();
      onClick();
    }
  };

  return (
    <Button
      asChild
      className={className}
      size="icon"
      type="button"
      variant={variant}
      onClick={handleClick}
    >
      <Link href={resultLink}>
        <ArrowLeft className="size-3 shrink-0" />
      </Link>
    </Button>
  );
};
