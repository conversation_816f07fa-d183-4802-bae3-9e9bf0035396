import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { logout } from '@/api';
import { resetPassword } from '@/api/actions';
import { PASSWORD_REGEX, SIGNUP_CONSTRAINTS, SPECIAL_CHARACTERS } from '@/constants';
import { ROUTES } from '@/constants/routes';
import { getTokens } from '@/lib/firebase/get-tokens';
import { revokeTokens } from '@/module/auth/revoke-tokens';

const useSchema = () => {
  const t = useTranslations('reset-password.validations');
  const { maximumPasswordLength, minimumPasswordLength } = SIGNUP_CONSTRAINTS;

  return z
    .object({
      password: z
        .string()
        .min(1, { message: t('password-required') })
        .min(minimumPasswordLength, {
          message: t('password-min-length', {
            min: minimumPasswordLength,
          }),
        })
        .max(maximumPasswordLength, {
          message: t('password-max-length', {
            max: maximumPasswordLength,
          }),
        })
        .regex(PASSWORD_REGEX, {
          message: `${t('password-format')}${SPECIAL_CHARACTERS}`,
        }),
      passwordConfirm: z.string().min(1, { message: t('password-confirm-required') }),
    })
    .superRefine(({ password, passwordConfirm }, context) => {
      if (password !== passwordConfirm) {
        context.addIssue({
          code: 'custom',
          message: t('password-confirm-mismatch'),
          path: ['passwordConfirm'],
        });
      }
    });
};

type Schema = z.infer<ReturnType<typeof useSchema>>;

export const useNewPasswordForm = (user: string, oobCode: string) => {
  const { push } = useRouter();
  const schema = useSchema();

  const form = useForm<Schema>({
    resolver: zodResolver(schema),
    defaultValues: {
      password: '',
      passwordConfirm: '',
    },
  });

  const onSubmit = async ({ password }: Schema) => {
    try {
      const response = await resetPassword(user, password, oobCode);

      if (response?.error) {
        form.setError('root.serverError', {
          message: response.error,
        });
      } else {
        form.reset();

        const tokens = await getTokens();

        if (tokens) {
          await revokeTokens(tokens.decodedToken.email);
          await logout();
        }

        push(ROUTES.LOGIN);
      }
    } catch (error) {
      let errorMessage = 'An unknown error occurred';
      if (error instanceof AxiosError) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access -- TODO: Fix this error messages mess
        errorMessage = error.response?.data.message;
      } else if (error instanceof ReferenceError) {
        errorMessage = error.message;
      }

      form.setError('root.serverError', {
        message: errorMessage,
      });
    }
  };

  return { form, onSubmit };
};
