'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { getAllUserWalletsV3 } from '@/lib/api';
import { useRedirectUrl } from '@/module/routing/use-redirect-url';

import { loginAction } from './login-action';
import { useSignInMfaContext } from './sign-in-mfa-context';

export const useLoginSchema = () => {
  const t = useTranslations('sign-in.sign-up.form');

  return z.object({
    email: z
      .string()
      .min(1, { message: t('email-required') })
      .email({ message: t('email-wrong-format') }),
    password: z.string().min(1, { message: t('password-required') }),
  });
};

type SignInSchema = z.infer<ReturnType<typeof useLoginSchema>>;

export const useCredentialsForm = () => {
  const redirectUrl = useRedirectUrl();
  const { setMfaResolver } = useSignInMfaContext();

  const schema = useLoginSchema();
  const { push } = useRouter();

  const form = useForm<SignInSchema>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async ({ email, password }: SignInSchema) => {
    const result = await loginAction(email, password);

    if (result.error) {
      form.setError('root.serverError', { message: result.error.code });

      if (result.error.code === 'auth/multi-factor-auth-required') {
        setMfaResolver(result.error.mfaResolver);
      }
    } else {
      const wallets = await getAllUserWalletsV3({
        useSelectedChains: false,
      });

      if (wallets.length === 0) {
        push(ROUTES.SETUP_WALLET.ROOT);
      } else {
        push(redirectUrl);
      }
    }
  };

  return { form, onSubmit };
};
