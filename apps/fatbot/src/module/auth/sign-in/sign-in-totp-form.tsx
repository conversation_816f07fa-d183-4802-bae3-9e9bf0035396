'use client';

import { TotpMultiFactorGenerator } from 'firebase/auth';
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl';

import { finalizeLogin } from '@/api/actions';
import { ROUTES } from '@/constants/routes';
import { getAllUserWalletsV3 } from '@/lib/api';
import { TotpForm } from '@/module/auth/mfa/components/totp-form';
import { useSignInMfaContext } from '@/module/auth/sign-in/sign-in-mfa-context';
import { useRedirectUrl } from '@/module/routing/use-redirect-url';

export const SignInTotpForm = () => {
  const t = useTranslations('sign-in');
  const redirectUrl = useRedirectUrl();
  const { mfaResolver } = useSignInMfaContext();

  const { push } = useRouter();

  const onSubmit = async ({ code }: { code: string }) => {
    if (mfaResolver?.hints?.[0]?.factorId !== TotpMultiFactorGenerator.FACTOR_ID) {
      throw new Error(t('mfa-error'));
    }
    const multiFactorAssertion = TotpMultiFactorGenerator.assertionForSignIn(mfaResolver.hints[0].uid, code);

    const userCredential = await mfaResolver.resolveSignIn(multiFactorAssertion);
    const idToken = await userCredential.user.getIdToken();
    await finalizeLogin(idToken);

    const wallets = await getAllUserWalletsV3({
      useSelectedChains: false,
    });

    if (wallets.length === 0) {
      push(ROUTES.SETUP_WALLET.ROOT);
    } else {
      push(redirectUrl);
    }
  };

  return <TotpForm onSubmit={onSubmit} />;
};
