'use client';

import { multiFactor, TotpMultiFactorGenerator } from 'firebase/auth';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl';
import { useQueryState } from 'nuqs';

import { logout } from '@/api';
import Fatty from '@/assets/images/securing-purchase.webp';
import { Button } from '@/components/ui/button';
import Display from '@/components/ui/display';
import { ROUTES } from '@/constants/routes';
import { env } from '@/env/client';
import { getGetUserQueryOptions, useGetUser } from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { TotpForm } from '@/module/auth/mfa/components/totp-form';
import { revalidateProfile } from '@/module/auth/mfa/enroll/revalidate-profile.action';
import { sendMfaNotification } from '@/module/auth/mfa/enroll/send-mfa-notification';
import { revokeTokens } from '@/module/auth/revoke-tokens';

import { useMfa } from './mfa-context';

export const EnrollMfaTotpForm = () => {
  const t = useTranslations('mfa');
  const { totpSecret, user } = useMfa();
  const [, setSelectedTab] = useQueryState('step');
  const onContinue = () => setSelectedTab('success');
  const mfaDisplayName = env.NEXT_PUBLIC_MFA_DISPLAY_NAME;
  const queryClient = getQueryClient();
  const { data } = useGetUser();
  const router = useRouter();

  const onSubmit = async ({ code }: { code: string }) => {
    if (!totpSecret || !user) {
      return;
    }

    const multiFactorAssertion = TotpMultiFactorGenerator.assertionForEnrollment(totpSecret, code);

    await multiFactor(user).enroll(multiFactorAssertion, mfaDisplayName);
    await sendMfaNotification(user.email ?? '');

    await queryClient.invalidateQueries(getGetUserQueryOptions());
    void onContinue();

    void revalidateProfile();

    await revokeTokens(data?.email);
    await logout();

    router.push(ROUTES.LOGIN);
  };

  return (
    <div className="flex flex-col gap-4">
      <Image alt={t('fatty-security-alt')} className="m-auto w-[30%]" src={Fatty} />

      <Display size="S" weight="bold">
        {t('enter-verification-code')}
      </Display>

      <div className="text-md text-white">{t('verification-description')}</div>

      <TotpForm onSubmit={onSubmit} />
      <Button asChild className="w-full" variant="outline">
        <Link href={ROUTES.PROFILE.ROOT}>{t('cancel-and-return')}</Link>
      </Button>
    </div>
  );
};
