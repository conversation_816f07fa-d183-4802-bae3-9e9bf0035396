import type { TotpSecret, User } from 'firebase/auth';
import { useQueryState } from 'nuqs';
import { createContext, type ReactNode, useContext, useEffect, useMemo, useState } from 'react';

interface MFAContext {
  user: User | null;
  qrCodeUrl: string | null;
  secretKey: string | null;
  totpSecret: TotpSecret | null;

  setUser: (user: User) => void;
  setTotpSecret: (secret: TotpSecret) => void;
  setQrCodeUrl: (qrCodeUrl: string) => void;
  setSecretKey: (secretKey: string) => void;
}

const MFAContext = createContext<MFAContext | undefined>(undefined);

interface Props {
  children: ReactNode;
  initialStep?: string;
}

export const MFAProvider = ({ children, initialStep }: Props) => {
  const start = initialStep ?? 'setup-mfa';
  const [user, setUser] = useState<User | null>(null);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [secretKey, setSecretKey] = useState<string | null>(null);
  const [totpSecret, setTotpSecret] = useState<TotpSecret | null>(null);
  const [selectedTab, setSelectedTab] = useQueryState('step');

  useEffect(() => {
    if ([qrCodeUrl, secretKey, totpSecret].some((item) => !item) && selectedTab !== start) {
      void setSelectedTab(start);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- On mount only
  }, []);

  const value = useMemo(
    () => ({
      user,
      setUser,
      qrCodeUrl,
      setQrCodeUrl,
      secretKey,
      setSecretKey,
      totpSecret,
      setTotpSecret,
    }),
    [qrCodeUrl, secretKey, totpSecret, user],
  );

  return <MFAContext.Provider value={value}>{children}</MFAContext.Provider>;
};

export const useMfa = () => {
  const mfaContext = useContext(MFAContext);

  if (!mfaContext) {
    throw new Error('MFA Context not found.');
  }

  return mfaContext;
};
