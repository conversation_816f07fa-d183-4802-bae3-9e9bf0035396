'use client';
import { getAuth, TotpMultiFactorGenerator } from 'firebase/auth';
import Image from 'next/image';
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useTranslations } from 'next-intl';

import { logout } from '@/api';
import { disableMfa } from '@/api/actions';
import Fatty from '@/assets/images/securing-purchase.webp';
import { Button } from '@/components/ui/button';
import Display from '@/components/ui/display';
import { ROUTES } from '@/constants/routes';
import { app } from '@/firebase';
import { useGetUser } from '@/lib/api';
import { TotpForm } from '@/module/auth/mfa/components/totp-form';
import { revokeTokens } from '@/module/auth/revoke-tokens';
import { useSignInMfaContext } from '@/module/auth/sign-in/sign-in-mfa-context';

export const DisableMfaTotpForm = () => {
  const t = useTranslations('mfa');

  const { mfaResolver } = useSignInMfaContext();
  const { data: user } = useGetUser();
  const router = useRouter();

  const onSubmit = async ({ code }: { code: string }) => {
    if (mfaResolver?.hints[0]?.factorId !== TotpMultiFactorGenerator.FACTOR_ID) {
      throw new Error(t('disable-mfa-error'));
    }

    const multiFactorAssertion = TotpMultiFactorGenerator.assertionForSignIn(mfaResolver.hints[0].uid, code);

    await mfaResolver.resolveSignIn(multiFactorAssertion);

    const auth = getAuth(app);
    const currentUser = auth.currentUser;

    if (!currentUser) {
      return;
    }

    await disableMfa(currentUser.uid);

    await revokeTokens(user?.email ?? '');
    await logout();
    router.push(ROUTES.LOGIN);
  };

  return (
    <div className="flex flex-col gap-4">
      <Image alt={t('fatty-security-alt')} className="m-auto w-[30%]" src={Fatty} />

      <Display size="S" weight="bold">
        {t('enter-verification-code')}
      </Display>

      <div className="text-md text-white">{t('verification-description')}</div>
      <TotpForm onSubmit={onSubmit} />
      <Button asChild className="w-full" variant="outline">
        <Link href={ROUTES.PROFILE.ROOT}>{t('cancel-and-return')}</Link>
      </Button>
    </div>
  );
};
