import Link from 'next/link';
import type { FC } from 'react';

import { isTokenDexResult } from '@/api/typeguards/is-token-dexresult';
import { Logo } from '@/assets';
import { User } from '@/assets/user';
import { AppNavigation } from '@/components/app-header/app-navigation';
import { ROUTES } from '@/constants/routes';
import { type Chain, getGetTokenDetailAsAnonymousUserV2QueryOptions } from '@/lib/api';
import { getQueryClient } from '@/lib/query-client';
import { TokenDetailAvatar } from '@/module/token-detail/token-detail-avatar';

interface Props {
  chain: Chain;
  address: string;
  leagueSystemEnabled: boolean;
}

export const PublicTokenDetailHeader: FC<Props> = async ({ chain, address, leagueSystemEnabled }) => {
  const queryClient = getQueryClient();
  const data = await queryClient.fetchQuery(getGetTokenDetailAsAnonymousUserV2QueryOptions(chain, address));

  return (
    <div className="grid grid-cols-2 items-center py-2 sm:px-3 sm:pb-3 sm:pt-4 md:grid-cols-[1fr_auto_1fr]">
      <div className="flex">
        <Logo className="size-5 text-primary shrink-0" />
        {isTokenDexResult(data.tokenInfo) ? (
          <TokenDetailAvatar
            chain={chain}
            tokenImageUrl={data.tokenInfo.dexInfo.info?.imageUrl}
            tokenName={data.tokenInfo.dexInfo.baseToken.name}
            tokenSymbol={data.tokenInfo.dexInfo.baseToken.symbol}
          />
        ) : null}
      </div>

      <AppNavigation className="hidden sm:flex" leagueSystemEnabled={leagueSystemEnabled} />
      <div className="flex items-center justify-end gap-3">
        <Link className="shrink-0" data-testid="user-profile-icon" href={ROUTES.SIGN_UP}>
          <div className="rounded-full border-2 border-border-subtle p-1.5">
            <User />
          </div>
        </Link>
      </div>
    </div>
  );
};
