import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { ROUTES } from '@/constants/routes';
import type { SearchUserTransactionResult } from '@/lib/api';
import { LastTransactionsRow } from '@/module/manual-trading/last-transactions/last-transactions-row';

import { MyTransactionsListHeader } from './my-transactions-list-header';

interface Props {
  transactions: SearchUserTransactionResult[];
}

export const MyTransactions: React.FC<Props> = ({ transactions }) => {
  const pathname = usePathname();

  return (
    <div className="flex flex-col gap-y-2">
      <Link href={ROUTES.LAST_TRANSACTIONS.path(undefined, pathname)}>
        <MyTransactionsListHeader />
      </Link>
      {transactions.map((transaction) => (
        <LastTransactionsRow
          key={transaction.txHash}
          chain={transaction.transactionChain}
          containerClassName="md:grid-cols-[4fr_2fr_2fr_3fr] lg:grid-cols-[5fr_2fr_2fr_3fr] pr-1"
          createdAt={transaction.createdAt}
          currencyAmountUsd={transaction.currencyAmountUsd}
          currencyNativeAmount={transaction.currencyNativeAmount}
          explorerUrl={transaction.txDetailUrl}
          tokenAmount={transaction.tokenNativeAmount}
          tokenName={transaction.tokenName}
          tokenSymbol={transaction.tokenSymbol}
          transactionStatus={transaction.transactionStatus}
          transactionType={transaction.transactionType}
          walletCustomName={transaction.walletCustomName}
        />
      ))}
    </div>
  );
};
