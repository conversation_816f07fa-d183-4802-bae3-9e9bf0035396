import { VisuallyH<PERSON>den } from '@radix-ui/react-visually-hidden';
import { useQueryClient } from '@tanstack/react-query';
import { motion } from 'motion/react';
import type { StaticImageData } from 'next/image';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { match } from 'ts-pattern';

import { DEFAULT_PAGINATION_SIZE_SMALL, REFETCH_INTERVAL_5_SECONDS } from '@/api/constants';
import { CheckmarkIcon } from '@/assets/checkmark-icon';
import { SuccessfullyWithdrawn } from '@/assets/images';
import { PurchaseSuccessAnimation } from '@/components/animations/purchase-success';
import { TradeValidationAnimation } from '@/components/animations/trade-validation';
import { Button } from '@/components/ui/button';
import { CircularProgress } from '@/components/ui/circular-progress';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { ROUTES } from '@/constants/routes';
import {
  type Chain,
  getGetUserLifetimeTradeVolumeQueryOptions,
  getGetUserStreakQueryOptions,
  getSearchUserMarketPositionV3QueryOptions,
  getSearchUserTransactionOfAllTokenV3QueryOptions,
  getSearchUserWalletCurrencyPositionsQueryOptions,
  useGetTokenDetailAsUserV2,
} from '@/lib/api';
import { lastTransactionsRequestParams } from '@/module/manual-trading/last-transactions/last-transactions-request-params';
import fatboySad from '@/module/token-detail/manual-trading/fatboy-sad.webp';
import { useTradeStatusFetchCount } from '@/module/token-detail/manual-trading/utils/use-trade-status-fetch-count';

import { getTradeResult } from './get-trade-result';
import { getTransactionStatus } from './get-transaction-status';
import { useResultProgressLabels } from './use-progress-labels';

export enum DialogStatus {
  INITIATED,
  SUBMITTING,
  WAITING_FOR_TX,
  PENDING,
  SUCCESS,
  ERROR,
}

interface Props {
  isSubmitting?: boolean;
  submitError?: string | null;
  onClose: () => void;
  txHashes?: string[];
  tokenAddress: string;
  tokenChain: Chain;
  isSell?: boolean;
}

const TOO_LONG_TRANSACTION_FETCH_THRESHOLD = 50;

export const ResultDialog = ({
  submitError,
  isSubmitting,
  onClose,
  txHashes,
  tokenChain,
  tokenAddress,
  isSell = false,
}: Props) => {
  const t = useTranslations('token-detail.trading');
  const queryClient = useQueryClient();
  const { push } = useRouter();

  const progressLabels = useResultProgressLabels(tokenChain);
  const tokenInfoQuery = useGetTokenDetailAsUserV2(tokenChain, tokenAddress, {
    query: {
      refetchInterval: REFETCH_INTERVAL_5_SECONDS,
    },
  });

  const fetchCount = useTradeStatusFetchCount(tokenInfoQuery);

  const status = getTransactionStatus(
    tokenInfoQuery.data?.lastUserTransactionsOnThisToken.content,
    txHashes,
    submitError,
    isSubmitting,
  );

  const tradeResult = getTradeResult(tokenInfoQuery.data?.lastUserTransactionsOnThisToken.content, txHashes ?? []);

  const resultMessage = tradeResult
    ? match(tradeResult.isSell)
        .with(true, () =>
          t('success-message-sell', {
            amount: tradeResult.tokenAmount,
            tokenSymbol: tradeResult.tokenSymbol,
          }),
        )
        .with(false, () =>
          t('success-message-buy', {
            amount: tradeResult.tokenAmount,
            tokenSymbol: tradeResult.tokenSymbol,
          }),
        )
        .otherwise(() => null)
    : null;

  useEffect(() => {
    if (status === DialogStatus.SUCCESS) {
      void queryClient.invalidateQueries(
        getSearchUserTransactionOfAllTokenV3QueryOptions(lastTransactionsRequestParams({ useSelectedChains: true }), {
          size: DEFAULT_PAGINATION_SIZE_SMALL,
        }),
      );
      void queryClient.invalidateQueries(
        getSearchUserMarketPositionV3QueryOptions({ useSelectedChains: true }, { size: DEFAULT_PAGINATION_SIZE_SMALL }),
      );
      void queryClient.invalidateQueries(
        getSearchUserWalletCurrencyPositionsQueryOptions({
          useSelectedChains: false,
        }),
      );
      void queryClient.invalidateQueries(getGetUserLifetimeTradeVolumeQueryOptions());
      void queryClient.invalidateQueries(getGetUserStreakQueryOptions());
    }
  }, [status, queryClient]);

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent
        aria-describedby=""
        className="top-0 h-full gap-2 pb-5 sm:h-auto sm:gap-5 sm:pt-6"
        invert={status === DialogStatus.SUCCESS}
      >
        {status === DialogStatus.ERROR ? (
          <>
            <div className="text-center text-display-s font-bold text-text-secondary">{t('trade-failed')}</div>
            <div className="relative">
              <Image alt="Happy FatBoy" className="inset-x-0 top-6 mx-auto" src={fatboySad} width={180} />
              <div className="mt-5 break-words text-center font-semibold text-text-secondary">{submitError}</div>
            </div>
          </>
        ) : null}
        {status === DialogStatus.SUCCESS ? (
          <>
            <div className="text-center text-display-s font-bold text-text-invert">{resultMessage}</div>
            <div className="relative">
              <CircularProgress className="-my-2 sm:-my-0" progress={0} />
              {isSell ? (
                <Image
                  alt="Happy FatBoy"
                  className="absolute inset-x-0 top-1/2 mx-auto max-h-[250px] -translate-y-1/2"
                  src={SuccessfullyWithdrawn as StaticImageData}
                  width={200}
                />
              ) : (
                <PurchaseSuccessAnimation className="absolute inset-x-0 top-1/2 size-full -translate-y-1/2 p-5" />
              )}
            </div>
            <div className="flex flex-col gap-2">
              <Link href={ROUTES.MY_ASSETS.ROOT}>
                <Button className="w-full" variant="invertOutline">
                  {t('view-my-assets')}
                </Button>
              </Link>
              <Button variant="invert" onClick={onClose}>
                {t('back-to-market')}
              </Button>
            </div>
          </>
        ) : null}
        {[DialogStatus.INITIATED, DialogStatus.SUBMITTING, DialogStatus.PENDING, DialogStatus.WAITING_FOR_TX].includes(
          status,
        ) ? (
          <>
            <div className="text-center text-display-s font-bold text-text-secondary">
              {t(isSell ? 'securing-your-sale' : 'securing-your-purchase')}
            </div>
            <div className="relative overflow-hidden">
              <CircularProgress className="-my-1 sm:-my-0" infinite />
              <TradeValidationAnimation className="absolute inset-x-0 top-1/2 size-full -translate-y-1/2 p-8" />
            </div>

            {fetchCount >= TOO_LONG_TRANSACTION_FETCH_THRESHOLD ? (
              <div className="mb-2 flex flex-col items-center gap-3">
                <p className="text-center text-md text-text-secondary">{t('transaction-taking-longer')}</p>

                <Button
                  onClick={() => {
                    onClose();
                    push(ROUTES.MANUAL_TRADING.TOKEN_DETAIL(tokenAddress, tokenChain));
                  }}
                >
                  {t('back-to-market')}
                </Button>
              </div>
            ) : (
              <div className="flex flex-col items-center gap-1">
                {progressLabels.map(({ status: labelStatus, label }) => (
                  <motion.span
                    key={label}
                    animate={{ opacity: Number(status) >= labelStatus ? 1 : 0 }}
                    className="flex items-center gap-1 text-center text-md text-text-secondary"
                    initial={{ opacity: 0 }}
                  >
                    {label}
                    <div className="flex size-[20px] items-center justify-center rounded-full border-2 border-border-subtle">
                      <motion.div
                        animate={{
                          opacity: Number(status) > labelStatus ? 1 : 0,
                        }}
                        initial={{ opacity: 0 }}
                      >
                        <CheckmarkIcon className="size-1 text-surface-active" />
                      </motion.div>
                    </div>
                  </motion.span>
                ))}
              </div>
            )}
          </>
        ) : null}
        <VisuallyHidden>
          <DialogTitle />
        </VisuallyHidden>
      </DialogContent>
    </Dialog>
  );
};
