'use client';

import { ErrorMessage as FormErrorMessage } from '@hookform/error-message';
import BigNumber from 'bignumber.js';
import { useTranslations } from 'next-intl';
import { type FormEvent, useMemo, useState } from 'react';

import { ErrorMessage } from '@/components/error-message';
import { Form, FormControl, FormField, FormItem } from '@/components/form/form';
import { TokenInput } from '@/components/form/token-input';
import { LoadingButton } from '@/components/ui/loading-button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { type Chain, type DexDetail, type TokenAuditDto, useGetTaxV2 } from '@/lib/api';
import { formatUsd } from '@/lib/formatters/format-usd';
import { cn } from '@/lib/utils';
import { getChainConfig } from '@/module/multichain/get-chain-config';
import { BuyWarning } from '@/module/token-detail/buy-warning/buy-warning';
import { getTradeButtonVariant } from '@/module/token-detail/buy-warning/utils';
import { BuyWarningDialog } from '@/module/token-detail/buy-warning-dialog';
import { NativePresets } from '@/module/token-detail/manual-trading/native-presets';
import { ResultDialog } from '@/module/token-detail/manual-trading/result-dialog/result-dialog';
import { TokenPresets } from '@/module/token-detail/manual-trading/tokens-presets';
import { useManualTradingForm } from '@/module/token-detail/manual-trading/use-manual-trading-form';
import { makeAuditVariantFromRiskFactor } from '@/module/token-detail/token-audit/utils';

type TabOptions = 'buy' | 'sell';

interface Props {
  chain: Chain;
  tokenInfo: Required<DexDetail>;
  tokenAudit: TokenAuditDto | undefined;
  defaultTab?: TabOptions;
  isTokenContractVerified: boolean;
}

export const PublicManualTradingForm = ({
  chain,
  tokenInfo,
  defaultTab = 'buy',
  tokenAudit,
  isTokenContractVerified,
}: Props) => {
  const t = useTranslations('token-detail');

  const [isResultDialogOpen, setIsResultDialogOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState<TabOptions>(defaultTab);
  const [hideBuyWarning, setHideBuyWarning] = useState(false);
  const isSell = selectedTab === 'sell';
  const auditVariant = makeAuditVariantFromRiskFactor(tokenAudit?.result?.riskFactor);

  const {
    form,
    error: submitError,
    txHashes,
    isTokenAmountPending,
    isNativeAmountPending,
  } = useManualTradingForm({
    chain,
    dexPairInfo: tokenInfo.dexInfo.dexPairInfo,
    tokenAddress: tokenInfo.dexInfo.baseToken.address,
    defaultIsSell: defaultTab === 'sell',
    defaultWalletId: '',
  });

  const router = useRouter();

  const tokenAmount = form.watch('tokenAmount');
  const pricePerToken = tokenInfo.dexInfo.priceUsd;

  const tokenPriceInUsd = useMemo(() => {
    const tokenAmountBn = new BigNumber(tokenAmount);
    const pricePerTokenBn = new BigNumber(pricePerToken);

    return formatUsd(
      tokenAmountBn.gt(0) && pricePerTokenBn.gt(0) ? new BigNumber(tokenAmount).times(pricePerTokenBn).toString() : '0',
    );
  }, [tokenAmount, pricePerToken]);

  const nativeAmountHelpText = isSell ? tokenPriceInUsd : '';
  const chainConfig = getChainConfig(chain);

  const { data: tax } = useGetTaxV2(tokenInfo.dexInfo.baseToken.address, {
    dexPairInfo: tokenInfo.dexInfo.dexPairInfo,
    chain,
  });
  const onFakeSubmit = (event: FormEvent) => {
    event.preventDefault();
    router.push(ROUTES.LOGIN);
  };

  return (
    <div className="@container/main">
      {tax?.isHoneypot ? <BuyWarningDialog type="honeypot" /> : null}
      {!isTokenContractVerified ? <BuyWarningDialog type="scam" /> : null}
      <div className="mb-2 flex flex-col items-center justify-between gap-1.5 @xs:flex-row">
        <Tabs
          className="w-full"
          defaultValue={selectedTab}
          onValueChange={(value) => {
            setSelectedTab(value as TabOptions);
          }}
        >
          <TabsList className="w-full @xs:w-auto">
            <TabsTrigger value="buy">{t('buy')}</TabsTrigger>
            <TabsTrigger value="sell">{t('sell')}</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <Form {...form}>
        <form onSubmit={onFakeSubmit}>
          <div
            className={cn('w-full py-1 transition-transform', {
              'translate-y-full': isSell,
            })}
          >
            <FormField
              control={form.control}
              name="nativeAmount"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormControl>
                    <TokenInput
                      helpText={nativeAmountHelpText}
                      inputProps={{
                        autoFocus: true,
                        placeholder: '0',
                        value: field.value,
                        disabled: form.formState.isSubmitting || isSell,
                        onValueChange: ({ value }) => {
                          if (isSell) {
                            return;
                          }

                          form.setValue('nativeAmount', value);
                        },
                      }}
                      isLoading={isNativeAmountPending}
                      title={t(isSell ? 'earning' : 'spending')}
                      tokenImage={chainConfig.tokenImage}
                      tokenName={chainConfig.symbol}
                      variant={isSell ? 'primary' : 'secondary'}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <div
            className={cn('w-full py-1 transition-transform', {
              '-translate-y-full': isSell,
            })}
          >
            <FormField
              control={form.control}
              name="tokenAmount"
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormControl>
                    <TokenInput
                      helpText={tokenPriceInUsd}
                      inputProps={{
                        placeholder: '0',
                        value: field.value,
                        disabled: form.formState.isSubmitting || !isSell,
                        onValueChange: ({ value }) => {
                          if (!isSell) {
                            return;
                          }

                          form.setValue('tokenAmount', value);
                        },
                      }}
                      isLoading={isTokenAmountPending}
                      title={t(isSell ? 'selling-token' : 'buying-token')}
                      tokenImage={tokenInfo.dexInfo.info?.imageUrl}
                      tokenName={tokenInfo.dexInfo.baseToken.symbol}
                      variant={isSell ? 'secondary' : 'primary'}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          <FormErrorMessage
            errors={form.formState.errors}
            name="tokenAmount"
            render={({ message }) => <ErrorMessage variant="error">{message}</ErrorMessage>}
          />

          <FormErrorMessage
            errors={form.formState.errors}
            name="nativeAmount"
            render={({ message }) => <ErrorMessage variant="error">{message}</ErrorMessage>}
          />

          {isSell ? (
            <TokenPresets
              onSelect={(value) => {
                form.setValue('tokenAmount', value);
              }}
            />
          ) : (
            <NativePresets
              chain={chain}
              onSelect={(value) => {
                form.setValue('nativeAmount', value);
              }}
            />
          )}

          <BuyWarning
            auditVariant={auditVariant}
            className="mb-2"
            isDismissed={hideBuyWarning}
            isHoneyPot={false}
            isUnverified={!isTokenContractVerified}
            onClose={() => {
              setHideBuyWarning(true);
            }}
          >
            <LoadingButton
              className="w-full"
              disabled={!form.formState.isValid ? form.formState.isSubmitted : undefined}
              isLoading={form.formState.isSubmitting}
              variant={isSell ? 'primary' : getTradeButtonVariant(auditVariant)}
            >
              {isSell ? t('sell') : t('buy')}
            </LoadingButton>
          </BuyWarning>
        </form>
      </Form>

      {isResultDialogOpen ? (
        <ResultDialog
          isSubmitting={form.formState.isSubmitting}
          submitError={submitError}
          tokenAddress={tokenInfo.dexInfo.baseToken.address}
          tokenChain={chain}
          txHashes={txHashes}
          onClose={() => {
            form.reset();
            setIsResultDialogOpen(false);
          }}
        />
      ) : null}
    </div>
  );
};
