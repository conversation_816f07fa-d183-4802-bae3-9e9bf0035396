import { VisuallyH<PERSON>den } from '@radix-ui/react-visually-hidden';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { PurchaseSuccessAnimation } from '@/components/animations/purchase-success';
import { But<PERSON> } from '@/components/ui/button';
import { CircularProgress } from '@/components/ui/circular-progress';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { ROUTES } from '@/constants/routes';
import fatboySad from '@/module/token-detail/manual-trading/fatboy-sad.webp';

export enum ImportTokenDialogStatus {
  SUCCESS,
  ERROR,
}

interface Props {
  status: ImportTokenDialogStatus;
  onClose: () => void;
  tokenSymbol: string;
}

export const ImportTokenResultDialog = ({
  status,
  onClose,
  tokenSymbol,
}: Props) => {
  const t = useTranslations('token-detail');

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent
        aria-describedby=""
        className="top-0 h-full gap-2 pb-5 sm:h-auto sm:gap-5 sm:pt-6"
        invert={status === ImportTokenDialogStatus.SUCCESS}
      >
        {status === ImportTokenDialogStatus.ERROR ? (
          <>
            <div className="text-center text-display-s font-bold text-text-secondary">
              {t('advanced-settings.error-message-import', {
                tokenSymbol,
              })}
            </div>
            <div className="relative">
              <Image
                alt="Happy FatBoy"
                className="inset-x-0 top-6 mx-auto"
                src={fatboySad}
                width={180}
              />
            </div>
          </>
        ) : (
          <>
            <div className="text-center text-display-s font-bold text-text-invert">
              {t('advanced-settings.success-message-import', {
                tokenSymbol,
              })}
            </div>
            <div className="relative">
              <CircularProgress className="-my-2 sm:-my-0" progress={0} />
              <PurchaseSuccessAnimation className="absolute inset-x-0 top-1/2 size-full -translate-y-1/2 p-5" />
            </div>
            <div className="flex flex-col gap-2 ">
              <Link href={ROUTES.MY_ASSETS.ROOT}>
                <Button className="w-full" variant="invertOutline">
                  {t('trading.view-my-assets')}
                </Button>
              </Link>
              <Button variant="invert" onClick={onClose}>
                {t('trading.back-to-market')}
              </Button>
            </div>
          </>
        )}
        <VisuallyHidden>
          <DialogTitle />
        </VisuallyHidden>
      </DialogContent>
    </Dialog>
  );
};
