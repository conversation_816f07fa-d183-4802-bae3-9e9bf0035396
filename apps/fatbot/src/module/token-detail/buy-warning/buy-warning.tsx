import { Cross2Icon } from '@radix-ui/react-icons';
import { AlertTriangle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import React, { type ReactNode } from 'react';
import { match } from 'ts-pattern';

import { AnnotationX } from '@/assets';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import type { TokenAuditVariant } from '@/module/token-detail/token-audit/utils';

import {
  MessageAuditError,
  MessageAuditWarning,
  MessageHoneypot,
  MessageIsUnverified,
} from './buy-warning-messages';
import { getCardVariant } from './utils';

interface Props {
  className?: string;
  children: ReactNode;
  isHoneyPot?: boolean;
  isUnverified?: boolean;
  onClose?: () => void;
  isDismissed: boolean;
  auditVariant?: TokenAuditVariant;
}

export const BuyWarning: React.FC<Props> = ({
  className,
  children,
  isHoneyPot,
  isUnverified,
  onClose,
  isDismissed,
  auditVariant,
}) => {
  const t = useTranslations();
  const hasAuditWarning = ['error', 'warning'].includes(auditVariant ?? '');
  const isEnabled =
    !isDismissed && (isHoneyPot || isUnverified || hasAuditWarning);

  if (!isEnabled) {
    return <div className={className}>{children}</div>;
  }

  const icon = match({ auditVariant, isHoneyPot, isUnverified })
    .with({ auditVariant: 'error' }, () => <AnnotationX className="size-3" />)
    .with({ auditVariant: 'warning' }, () => (
      <AlertTriangle className="size-3" />
    ))
    .with({ isHoneyPot: true }, () => <AlertTriangle className="size-3" />)
    .with({ isUnverified: true }, () => <AlertTriangle className="size-3" />)
    .otherwise(() => null);

  const title = match({ auditVariant })
    .with({ auditVariant: 'error' }, () => t('token-detail.ai-audit.danger'))
    .with({ auditVariant: 'warning' }, () => t('token-detail.ai-audit.caution'))
    .otherwise(() => t('token-detail.buy-warning.title'));

  // warning message priority logic
  const warningMessage = match({ isHoneyPot, isUnverified, auditVariant })
    .with({ auditVariant: 'error' }, () => <MessageAuditError />)
    .with({ auditVariant: 'warning' }, () => <MessageAuditWarning />)
    .with({ isHoneyPot: true }, () => <MessageHoneypot />)
    .with({ isUnverified: true }, () => <MessageIsUnverified />)
    .otherwise(() => null);

  return (
    <Card
      className={cn('gap-1 rounded-md border-none p-1 pb-2', className)}
      variant={getCardVariant(auditVariant, isHoneyPot, isUnverified)}
    >
      <div
        className={cn(
          'grid grid-cols-[24px_1fr] gap-1 text-event-primary-content',
          {
            'text-event-warning-content':
              auditVariant === 'warning' || isHoneyPot || isUnverified,
            'text-event-error-content': auditVariant === 'error',
          }
        )}
      >
        {icon}
        <div className="mb-1">
          <div className="flex items-center justify-between">
            <span className="text-sm font-bold">{title}</span>
            {onClose ? <button type="button" onClick={onClose}>
                <Cross2Icon className="size-3 cursor-pointer" />
              </button> : null}
          </div>
          {warningMessage}
        </div>
      </div>
      {children}
    </Card>
  );
};
