'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { useMediaQueryMatch } from '@/lib/use-media-query-match';
import { scrollToAudit } from '@/module/token-detail/token-audit/scroll-to-audit';
import { usePublicMode } from '@/providers/public-mode-provider';

export const MessageHoneypot: React.FC = () => {
  const t = useTranslations();

  return <p className="text-sm font-medium">{t('token-detail.buy-warning.possible-honeypot')}</p>;
};

export const MessageIsUnverified: React.FC = () => {
  const t = useTranslations();

  return <p className="text-sm font-medium">{t('token-detail.buy-warning.possible-scam')}</p>;
};

export const MessageAuditError: React.FC = () => {
  const t = useTranslations();
  const router = useRouter();
  const { isPublic } = usePublicMode();
  const isSmallScreen = useMediaQueryMatch('medium');

  const handleClick = () => {
    if (isPublic) {
      router.push(ROUTES.LOGIN);
    } else {
      scrollToAudit(isSmallScreen);
    }
  };

  return (
    <div className="flex items-center gap-0.5">
      <span className="text-sm font-medium text-event-error-content">
        {t('token-detail.ai-audit.safety-issue-detected')}
      </span>
      <button
        className="text-sm font-semibold text-event-error-content underline bg-transparent border-none p-0"
        type="button"
        onClick={handleClick}
      >
        {t('token-detail.ai-audit.see-details')}
      </button>
    </div>
  );
};

export const MessageAuditWarning: React.FC = () => {
  const t = useTranslations();
  const router = useRouter();
  const { isPublic } = usePublicMode();
  const isSmallScreen = useMediaQueryMatch('medium');

  const handleClick = () => {
    if (isPublic) {
      router.push(ROUTES.LOGIN);
    } else {
      scrollToAudit(isSmallScreen);
    }
  };

  return (
    <div className="flex items-center gap-0.5">
      <span className="text-sm font-medium text-event-warning-content">
        {t('token-detail.ai-audit.potential-safety-issue-detected')}
      </span>
      <button
        className="text-sm font-semibold text-event-warning-content underline bg-transparent border-none p-0"
        type="button"
        onClick={handleClick}
      >
        {t('token-detail.ai-audit.see-details')}
      </button>
    </div>
  );
};
