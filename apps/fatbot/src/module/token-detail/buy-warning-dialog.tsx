'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { formatRichText } from '@/lib/formatters/format-rich-text';

interface Props {
  type: 'honeypot' | 'scam';
}

const useWarningTexts = ({ type }: Props) => {
  const t = useTranslations('token-detail');

  return type === 'honeypot'
    ? {
        title: t.rich('potential-honeypot-dialog.title', formatRichText),
        description: t.rich('potential-honeypot-dialog.description'),
        continue: t.rich('potential-honeypot-dialog.continue'),
        goBack: t.rich('potential-honeypot-dialog.go-back'),
      }
    : {
        title: t.rich('scam-warning.title', formatRichText),
        description: t.rich('scam-warning.description'),
        continue: t.rich('scam-warning.continue'),
        goBack: t.rich('scam-warning.go-back'),
      };
};

export const BuyWarningDialog = ({ type }: Props) => {
  const {
    description,
    goBack,
    title,
    continue: continueText,
  } = useWarningTexts({ type });
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(true);
  const router = useRouter();

  const handleClose = () => {
    setIsDialogOpen(false);
  };

  const handleBack = () => { router.replace(ROUTES.MANUAL_TRADING.ROOT); };

  return (
    <AlertDialog open={isDialogOpen} onOpenChange={handleClose}>
      <AlertDialogContent className="bg-surface-primary">
        <AlertDialogHeader className="mt-5 sm:mt-0">
          <AlertDialogTitle>
            <span className="text-display-xs font-bold">{title}</span>
          </AlertDialogTitle>
        </AlertDialogHeader>

        <AlertDialogDescription className="whitespace-pre-line text-xs font-medium text-text-secondary">
          {description}
        </AlertDialogDescription>

        <Button className="w-full" variant="outline" onClick={handleClose}>
          {continueText}
        </Button>
        <Button className="w-full" onClick={handleBack}>
          {goBack}
        </Button>
      </AlertDialogContent>
    </AlertDialog>
  );
};
