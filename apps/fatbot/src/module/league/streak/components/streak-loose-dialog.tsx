'use client';

import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import Image from 'next/image';
import { useTranslations } from 'next-intl';

import { FireGray } from '@/assets/league';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@/components/ui/dialog';
import { ROUTES } from '@/constants/routes';
import { useRemainingTime } from '@/hooks/use-remaining-time';
import { useRouter } from 'next/navigation';
import type { StreakConfig } from '@/module/league/streak/types';

import { DayStreak } from './day-streak';

export interface Props {
  streakExpiresAt: string;
  currentMultiplier: string;
  streakConfig: StreakConfig[];
  daysInStreak: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const StreakLooseDialog: React.FC<Props> = ({
  streakExpiresAt,
  currentMultiplier,
  streakConfig,
  daysInStreak,
  open,
  onOpenChange,
}) => {
  const t = useTranslations('streak');
  const timeLeft = useRemainingTime(streakExpiresAt);
  const { push } = useRouter();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <VisuallyHidden>
        <DialogTitle>
          {t('streak-loose-title', { count: daysInStreak })}
        </DialogTitle>
        <DialogDescription>{t('streak-loose-description')}</DialogDescription>
      </VisuallyHidden>
      <DialogContent
        bgVariant="secondary"
        className="h-full p-3 md:p-5 sm:min-w-110 items-center gap-0 sm:gap-0"
        hideClose
      >
        <div className="flex flex-col items-center gap-0">
          <Image alt="Fire Gray" className="mt-4" src={FireGray} width={145} />

          <div className="flex flex-col items-center my-2">
            <h3 className="text-display-l font-bold text-center max-w-80">
              {t('streak-loose-title', { count: daysInStreak })}
            </h3>
          </div>

          <div className="text-md font-semibold text-text-brand-1 mt-0.25 text-center mb-2 max-w-60">
            {t('streak-loose-time-left', {
              time: timeLeft,
              multiplier: currentMultiplier,
            })}
          </div>

          <DayStreak config={streakConfig} />

          <div className="text-md font-semibold text-text-secondary text-center mt-5">
            {t('streak-loose-info')}
          </div>
        </div>

        <DialogClose asChild>
          <Button
            className="w-full mt-5"
            variant="primary"
            onClick={() => {
              push(ROUTES.MANUAL_TRADING.ROOT);
            }}
          >
            {t('streak-loose-trade-now')}
          </Button>
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};
