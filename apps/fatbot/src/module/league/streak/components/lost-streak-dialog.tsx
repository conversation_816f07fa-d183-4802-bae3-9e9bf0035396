'use client';

import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { useTranslations } from 'next-intl';

import { FailedStreak } from '@/assets/league/failed-streak';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from '@/components/ui/dialog';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { formatRichText } from '@/lib/formatters/format-rich-text';
import type { StreakConfig } from '@/module/league/streak/types';

import { DayStreak } from './day-streak';

interface Props {
  currentMultiplier: string;
  streakConfig: StreakConfig[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export const LostStreakDialog: React.FC<Props> = ({
  currentMultiplier,
  streakConfig,
  open,
  onOpenChange,
}) => {
  const t = useTranslations('streak');
  const { push } = useRouter();

  const handleGetBackOnTrack = () => {
    onOpenChange(false);
    push(ROUTES.MANUAL_TRADING.ROOT);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <VisuallyHidden>
        <DialogTitle>{t('lost-streak-title')}</DialogTitle>
        <DialogDescription>
          {t('your-multiplier-is-gone', { multiplier: currentMultiplier })}
        </DialogDescription>
      </VisuallyHidden>
      <DialogContent
        bgVariant="secondary"
        className="p-3 md:p-5 sm:min-w-110"
        hideClose
      >
        <div className="flex flex-col items-center gap-2">
          <FailedStreak className="mb-0 mt-2 md:mt-0" />

          <h3 className="text-display-m font-bold text-center max-w-90 -mt-1">
            {t('lost-streak-title')}
          </h3>

          <h4 className="text-md font-semibold text-event-error-content">
            {t('your-multiplier-is-gone', {
              multiplier: currentMultiplier,
            })}
          </h4>

          <DayStreak className="mt-3" config={streakConfig} />

          <div className="text-md font-semibold text-text-secondary text-center mt-3">
            {t.rich('your-progress-is-not-lost-just-delayed', formatRichText)}
          </div>
        </div>

        <DialogClose asChild>
          <Button
            className="w-full mt-2"
            variant="primary"
            onClick={handleGetBackOnTrack}
          >
            {t('get-back-on-track')}
          </Button>
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
};
