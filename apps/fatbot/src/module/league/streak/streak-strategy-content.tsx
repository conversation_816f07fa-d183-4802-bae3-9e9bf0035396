'use client';

import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { EXTERNAL_LINKS } from '@/constants/external-links';
import { ROUTES } from '@/constants/routes';
import { useGetUserStreak } from '@/lib/api';
import { DayStreak } from '@/module/league/streak/components/day-streak';
import { DayStreakSkeleton } from '@/module/league/streak/components/day-streak-skeleton';
import { StreakRewards } from '@/module/league/streak/components/streak-rewards/streak-rewards';
import { useStreakConfig } from '@/module/league/streak/use-streak-config';

export const StreakStrategyContent = () => {
  const t = useTranslations('streak');
  const { data, isLoading } = useGetUserStreak();

  const streakConfig = useStreakConfig({
    streakDates: data?.streakDates ?? [],
    streakExpiresAt: data?.streakExpiresAt ?? '',
    isTresholdDay: data?.isThresholdDay ?? false,
  });

  return (
    <div className="grid grid-cols-1 max-w-(--breakpoint-lg) mx-auto items-start md:grid-cols-2 gap-y-5 gap-x-2">
      <Card
        className="shadow-primary border-none space-y-2 md:space-y-5"
        variant="area"
      >
        <div className="flex flex-col gap-2">
          <h1 className="text-display-m font-bold lg:text-display-l lg:mt-2">
            {t('streak-strategy-title')}
          </h1>
          <p className="text-md font-semibold text-text-secondary">
            {t('trade-daily-to-grow-your-streak')}
          </p>
        </div>

        <Card className="border-none shadow-primary" variant="primary">
          {isLoading ? (
            <DayStreakSkeleton />
          ) : (
            <DayStreak
              config={streakConfig}
              daysToNextStreak={data?.daysToNextStreak}
              radioGroupClassName="gap-0"
              streakExpiresAt={data?.streakExpiresAt}
              title={t('x-day-streak', { count: data?.daysInStreak ?? 0 })}
            />
          )}
        </Card>
      </Card>

      <Card
        className="shadow-primary border-none space-y-5 md:space-y-3"
        variant="area"
      >
        <div className="flex flex-col gap-2">
          <h3 className="text-display-xs font-bold">{t('streak-rewards')}</h3>
          <StreakRewards />
          <h3 className="text-display-xs font-bold">
            {t('stay-consistent-stack-more-earn-more')}
          </h3>
        </div>
        <div className="flex flex-col gap-2 lg:flex-row lg:gap-3">
          <Button className="w-full" variant="outline">
            <Link
              href={EXTERNAL_LINKS.FATBOT_WHITEPAPER}
              rel="noopener noreferrer"
              target="_blank"
            >
              {t('more-info')}
            </Link>
          </Button>
          <Button className="w-full" variant="primary">
            <Link href={ROUTES.MANUAL_TRADING.ROOT}>{t('trade-now')}</Link>
          </Button>
        </div>
      </Card>
    </div>
  );
};
