'use client';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useMemo } from 'react';

import { CloseableAlert } from '@/components/closeable-alert';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import Display from '@/components/ui/display';
import { Progress } from '@/components/ui/progress';
import { ROUTES } from '@/constants/routes';
import { useSearchUserFattyCards } from '@/lib/api';
import { RewardsCarousel } from '@/module/league/rewards/rewards-carousel';

interface Props {
  maximumRewardCardsCount?: number;
}

export const RewardsCard = ({ maximumRewardCardsCount = 10 }: Props) => {
  const t = useTranslations('league.cards');
  const { data: fattyCards } = useSearchUserFattyCards({
    claimed: false,
    displayed: false,
  });

  const maxCardCountReached = useMemo(
    () => (fattyCards?.length ?? 0) >= maximumRewardCardsCount,
    [fattyCards, maximumRewardCardsCount],
  );

  return (
    <>
      <div className="flex justify-between items-end">
        <Display size="XS" weight="bold">
          {t('title')}
        </Display>
        <span className="whitespace-nowrap text-md font-semibold capitalize text-text-secondary transition-colors hover:text-text-primary">
          <Link href={ROUTES.LEAGUE.CLAIMING}>{t('see-all')}</Link>
        </span>
      </div>
      <span className="font-semibold text-text-secondary">{t('text')}</span>
      <div className="flex flex-col gap-2">
        {maxCardCountReached ? (
          <CloseableAlert
            closeIconVisible
            title={t('all-cards-collected', {
              amount: maximumRewardCardsCount,
            })}
            variant="success"
          />
        ) : (
          <Card className="bg-surface-primary flex flex-col gap-2 shadow-2" variant="tertiary">
            <span className="text-sm font-semibold text-text-secondary">{t('trade-to-unlock', { amount: 150 })}</span>
            <Progress
              className="h-2 bg-surface-background shadow-primary-inner"
              value={(fattyCards?.length || 0) / maximumRewardCardsCount}
              variant="glossy"
            />
          </Card>
        )}
      </div>
      <RewardsCarousel />
      <Button variant={maxCardCountReached ? 'primary' : 'outline'}>{t('start-trading')}</Button>
    </>
  );
};
