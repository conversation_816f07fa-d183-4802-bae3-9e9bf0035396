import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { Info } from '@/assets';
import { Button } from '@/components/ui/button';
import Display from '@/components/ui/display';
import { ROUTES } from '@/constants/routes';
import { BotCharacter, BOTS_CONFIG } from '@/module/bot-trading/bots-config';

import { RankCard } from './rank-card';
import { TinyRankCard } from './tiny-rank-card';

const MOCK_TIME = '2D 6H left';
const PLACEHOLDER_ELEMENTS_COUNT = 5;

export const FattyLeagueCard = () => {
  const t = useTranslations('league.weekly-fatty-league');

  return (
    <>
      <div className="flex justify-between items-end">
        <Display size="XS" weight="bold">
          {t('weekly-fatty-league')}
        </Display>
        <span className="whitespace-nowrap text-md font-semibold capitalize text-text-secondary transition-colors hover:text-text-primary">
          <Link href={ROUTES.LEAGUE.LEADERBOARD(ROUTES.LEAGUE.ROOT)}>{t('see-all')}</Link>
        </span>
      </div>
      <span className="font-semibold text-text-secondary">{t('description')}</span>
      <TinyRankCard
        avatarFileId={BOTS_CONFIG[BotCharacter.FatMask].avatarFileId}
        badgeEndAdornment={<Info className="size-1.5" />}
        donutCount={4200}
        multiplier={1.5}
        rank={242}
        requiredTrades={500}
        volume={2800}
      />

      <div className="flex flex-col gap-2">
        {[...Array<number>(PLACEHOLDER_ELEMENTS_COUNT)].map((_, index) => (
          <RankCard key={index} donutCount={21353} email="j***@g**" multiplier={2.5} rank={index + 1} volume="12202" />
        ))}
      </div>
      <Button variant="outline">{t('remaining-time-text', { time: MOCK_TIME })}</Button>
    </>
  );
};
