'use client';
import { useQueryClient } from '@tanstack/react-query';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { toast } from 'sonner';

import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/tooltip';
import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes';
import { Chain, getGetReferralRewardsSummaryQueryOptions, useClaimRewards } from '@/lib/api';
import { cn } from '@/lib/utils';

import { ClaimReferralDialog } from './claim-referral-dialog';
import { useTabQuery } from './use-tab-query';

interface Props {
  showClaimButton: boolean;
  isClaimingButtonDisabled: boolean;
}

export const ClaimingControls = ({ showClaimButton, isClaimingButtonDisabled }: Props) => {
  const [isClaimDialogOpened, setIsClaimDialogOpened] = useState(false);
  const t = useTranslations('referral');
  const { mutate, data } = useClaimRewards();
  const queryClient = useQueryClient();

  const chain = useTabQuery() ?? Chain.EVM_MAINNET;

  const onClaimRewards = () => {
    mutate(
      { chain },
      {
        onSuccess: () => {
          void queryClient.invalidateQueries(getGetReferralRewardsSummaryQueryOptions());
          setIsClaimDialogOpened(true);
        },
        onError: () => toast.error(t('claim-error')),
      },
    );
  };

  const onTooltipTriggerClick = () => {
    if (isClaimingButtonDisabled) {
      toast.info(t('disabled-claim-tooltip'));
    }
  };

  return (
    <>
      <ClaimReferralDialog
        chain={chain}
        claimedAmountNative={data?.claimedAmountNative ?? '0'}
        open={isClaimDialogOpened}
        txHash={data?.txHash ?? ''}
        onOpenChange={setIsClaimDialogOpened}
      />

      <div className={cn('grid grid-cols-1 gap-2', showClaimButton ? 'sm:grid-cols-2' : 'grid-cols-1')}>
        {showClaimButton ? (
          <Tooltip open={isClaimingButtonDisabled ? undefined : false}>
            <TooltipTrigger asChild className="w-full" onClick={onTooltipTriggerClick}>
              <Button
                className="w-full"
                disabled={isClaimingButtonDisabled}
                type="button"
                variant="outline"
                onClick={onClaimRewards}
              >
                {t('claim-rewards')}
              </Button>
            </TooltipTrigger>
            <TooltipContent>{t('disabled-claim-tooltip')}</TooltipContent>
          </Tooltip>
        ) : null}

        <Link
          className="flex-1"
          href={`${ROUTES.PROFILE.REFERRAL.INVITE}?redirectUrl=${encodeURIComponent(ROUTES.PROFILE.REFERRAL.ROOT)}`}
        >
          <Button className="w-full">{t('invite-friends')}</Button>
        </Link>
      </div>
    </>
  );
};
