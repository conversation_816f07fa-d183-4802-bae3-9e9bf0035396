'use client';

import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import type { StaticImageData } from 'next/image';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { type FC, type PropsWithChildren, useMemo } from 'react';

import { Ellipse } from '@/assets/images';
import { ClaimSuccessAnimation } from '@/components/animations/claim-success';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import type { Chain } from '@/lib/api';
import { formatCurrency } from '@/lib/formatters/format-currency';
import { getChainConfig } from '@/module/multichain/get-chain-config';

import { ClaimReferralDialogLink } from './claim-referral-dialog-link';

interface Props {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  txHash: string;
  claimedAmountNative: string;
  chain: Chain;
}

export const ClaimReferralDialog: FC<PropsWithChildren<Props>> = ({
  children,
  open,
  onOpenChange,
  txHash,
  claimedAmountNative,
  chain,
}) => {
  const { push } = useRouter();
  const t = useTranslations('referral.claim-referral-dialog');

  const title = t.rich('title', {
    br: () => <br />,
    amount: formatCurrency(claimedAmountNative, chain),
  });

  const href = useMemo(() => getChainConfig(chain).explorer.tx(txHash), [chain, txHash]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children ? <DialogTrigger asChild>{children}</DialogTrigger> : null}

      <VisuallyHidden>
        <DialogTitle>{title}</DialogTitle>
      </VisuallyHidden>

      <DialogContent className="pt-2" hideClose={true} invert={true}>
        <div className="-mb-2 flex items-center justify-end">
          <ClaimReferralDialogLink href={href} />
        </div>

        <h1 className="w-full text-center font-goldplay text-3xl font-bold text-text-invert">{title}</h1>
        <div className="relative -mx-3 mt-4 sm:mt-0">
          <Image
            alt="ellipse"
            className="absolute inset-0 -top-5 aspect-square size-full h-[360px] sm:hidden"
            src={Ellipse as StaticImageData}
          />

          <ClaimSuccessAnimation className="h-[300px] w-full" />
        </div>

        <div className="mt-7 flex flex-col gap-2 sm:mt-0">
          <Button
            type="button"
            variant="invertOutline"
            onClick={() => {
              push(ROUTES.MY_ASSETS.ROOT);
            }}
          >
            {t('view-my-tokens')}
          </Button>
          <Button type="button" variant="invert" onClick={() => onOpenChange?.(false)}>
            {t('back-to-referral')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
