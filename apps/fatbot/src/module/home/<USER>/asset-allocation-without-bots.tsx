'use client';

import { useTranslations } from 'next-intl';
import React from 'react';

import { DEFAULT_STALE_TIME } from '@/api/constants';
import { DONUT_COLORS } from '@/components/charts/donut-chart/constants';
import { DonutChart } from '@/components/charts/donut-chart/donut-chart';
import { DataListHeader } from '@/components/data-list/data-list-header';
import { ProfitCard } from '@/components/profit-card';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { useMarketPositionOverviewV3 } from '@/lib/api';
import { formatPercentage } from '@/lib/formatters/format-percentage';
import { AllocationTitle } from '@/module/home/<USER>/allocation-title';

export const AssetAllocationWithoutBots = () => {
  const t = useTranslations();
  const router = useRouter();

  const { data } = useMarketPositionOverviewV3(
    { useSelectedChains: false },
    { query: { staleTime: DEFAULT_STALE_TIME } },
  );

  const manualTradingValue = Number(data?.totalValueAmountUsd ?? 0);
  const botTradingValue = 0; //TODO: update bot tranding data
  const manualTradingPercent = manualTradingValue ? manualTradingValue / (manualTradingValue + botTradingValue) : 0;
  const botTradingPercent = 0;

  const chartData = [
    {
      value: String(botTradingPercent),
      color: DONUT_COLORS[1] ?? '',
      formattedValue: formatPercentage(botTradingPercent, 0),
    },
    {
      value: String(manualTradingPercent),
      color: DONUT_COLORS[0] ?? '',
      formattedValue: formatPercentage(manualTradingPercent, 0),
      onClick: () => {
        router.push(ROUTES.ASSET_ALLOCATION.MANUALLY_TRADED());
      },
    },
  ];

  return (
    <div className="grid gap-2">
      <DataListHeader link={ROUTES.ASSET_ALLOCATION.MANUALLY_TRADED()} title={t('home-page.asset-allocation.title')} />

      <DonutChart data={chartData} />

      <div className="mt-2 flex flex-col gap-2 sm:flex-row">
        <ProfitCard
          className="flex-1"
          title={
            <AllocationTitle
              dotColor={manualTradingPercent !== 0 ? chartData[1]?.color : '#E0E0E0'}
              title={
                manualTradingPercent !== 0
                  ? t('home-page.asset-allocation.manually-traded-tokens')
                  : t('home-page.asset-allocation.awaits-allocation')
              }
            />
          }
          trend={String(data?.totalPnlAmountFraction ?? 0)}
          value={String(data?.totalValueAmountUsd ?? 0)}
        />
      </div>
    </div>
  );
};
