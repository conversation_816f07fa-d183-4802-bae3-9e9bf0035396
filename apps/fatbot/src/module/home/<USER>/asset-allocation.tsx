'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import React from 'react';

import { DONUT_COLORS } from '@/components/charts/donut-chart/constants';
import { DonutChart } from '@/components/charts/donut-chart/donut-chart';
import { DataListHeader } from '@/components/data-list/data-list-header';
import { ProfitCard } from '@/components/profit-card';
import { ROUTES } from '@/constants/routes';
import { formatPercentage } from '@/lib/formatters/format-percentage';
import { AllocationTitle } from '@/module/home/<USER>/allocation-title';

import { AssetAllocationSkeleton } from './asset-allocation-skeleton';
import { useAssetAllocationData } from './use-asset-allocation-data';

interface Props {
  seeAllLink?: string;
  titleClassName?: string;
}

export const AssetAllocation: React.FC<Props> = ({ seeAllLink, titleClassName }) => {
  const t = useTranslations();
  const router = useRouter();
  const pathname = usePathname();

  const {
    marketPositionOverview,
    botsOverviewData,
    manualTradingValue,
    botTradingValue,
    manualTradingPercent,
    botTradingPercent,
    isLoading,
  } = useAssetAllocationData();

  const chartData = [
    {
      value: botTradingPercent,
      color: DONUT_COLORS[1] ?? '',
      formattedValue: formatPercentage(botTradingPercent, 0),
    },
    {
      value: manualTradingPercent,
      color: DONUT_COLORS[0] ?? '',
      formattedValue: formatPercentage(manualTradingPercent, 0),
      onClick: () => {
        router.push(ROUTES.ASSET_ALLOCATION.MANUALLY_TRADED(pathname));
      },
    },
  ];

  if (isLoading) {
    return <AssetAllocationSkeleton />;
  }

  return (
    <div className="grid gap-2">
      <DataListHeader link={seeAllLink} title={t('home-page.asset-allocation.title')} titleClassName={titleClassName} />

      <DonutChart data={chartData} />

      <div className="mt-2 flex flex-col gap-2 sm:flex-row">
        <ProfitCard
          className="flex-1"
          title={
            <AllocationTitle dotColor={chartData[0]?.color} title={t('home-page.asset-allocation.bot-traded-tokens')} />
          }
          trend={botsOverviewData?.botsTotalPnlAmountFraction ?? '0'}
          value={botTradingValue}
        />
        <ProfitCard
          className="flex-1"
          title={
            <AllocationTitle dotColor={chartData[1]?.color} title={t('home-page.asset-allocation.manually-traded')} />
          }
          trend={marketPositionOverview?.totalPnlAmountFraction ?? '0'}
          value={manualTradingValue}
          onClick={() => {
            router.push(ROUTES.ASSET_ALLOCATION.MANUALLY_TRADED(pathname));
          }}
        />
      </div>
    </div>
  );
};
