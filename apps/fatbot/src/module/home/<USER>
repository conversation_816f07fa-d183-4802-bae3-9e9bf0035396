import Link from 'next/link';
import { getTranslations } from 'next-intl/server';

import { Card } from '@/components/ui/card';
import Display from '@/components/ui/display';
import { ROUTES } from '@/constants/routes';
import { BotCharacter, BOTS_CONFIG } from '@/module/bot-trading/bots-config';
import { TinyRankCard } from '@/module/league/components/tiny-rank-card';

export const LeagueOverview = async () => {
  const t = await getTranslations('league.league-overview');

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-end justify-between">
        <Display size="XS" weight="bold">
          {t('heading')}
        </Display>
        <span className="whitespace-nowrap text-md font-semibold capitalize text-text-secondary transition-colors hover:text-text-primary">
          <Link href={ROUTES.LEAGUE.ROOT}>{t('see-all')}</Link>
        </span>
      </div>
      <span className="text-text-secondary font-semibold text-md">
        {t('description')}
      </span>
      <Card
        className="bg-surface-primary flex flex-col gap-0.5 shadow-primary"
        variant="tertiary"
      >
        <span className="text-text-elevated text-sm font-md">
          {t('my-donuts-this-month')}
        </span>
        <Display size="XS" weight="bold">
          🍩 120,250
        </Display>
      </Card>

      <TinyRankCard
        avatarFileId={BOTS_CONFIG[BotCharacter.FatMask].avatarFileId}
        donutCount={4200}
        multiplier={1.5}
        rank={242}
        requiredTrades={500}
        variant="overview"
        volume={2800}
      />

      <div className="flex gap-2">
        <Card
          className="bg-surface-primary flex flex-1 flex-col gap-0.5 shadow-primary"
          variant="tertiary"
        >
          <span className="text-text-elevated text-sm font-md">
            {t('streak')}
          </span>
          <Display size="XS" weight="bold">
            {t('x-day-streak', { days: 12 })}
          </Display>
        </Card>
        <Card
          className="bg-surface-primary flex flex-1 flex-col gap-0.5 shadow-primary"
          variant="tertiary"
        >
          <span className="text-text-elevated text-sm font-md">
            {t('claimable-cards')}
          </span>
          <Display size="XS" weight="bold">
            🃏 3
          </Display>
        </Card>
      </div>
    </div>
  );
};
