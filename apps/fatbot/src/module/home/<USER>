import Link from 'next/link';
import { getTranslations } from 'next-intl/server';

import { Share02 } from '@/assets';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ROUTES } from '@/constants/routes';

export const Referral: React.FC = async () => {
  const t = await getTranslations();

  return (
    <div className="grid gap-2 ">
      <h3 className="text-display-xs font-bold">{t('home-page.referral')}</h3>
      <Card className="flex flex-col gap-2 shadow-primary" variant="primary">
        <div className="grid gap-1">
          <h4 className="text-xl font-semibold">
            {t('home-page.earn-rewards')}
          </h4>
          <p className="text-sm font-medium text-text-elevated">
            {t('home-page.get-30-of-friends-fees')}
          </p>
        </div>
        <Button asChild className="h-7 px-0" variant="outline">
          <Link
            href={`${ROUTES.PROFILE.REFERRAL.INVITE}?redirectUrl=${encodeURIComponent(ROUTES.HOME)}`}
          >
            <Share02 />
            {t('home-page.invite-friends')}
          </Link>
        </Button>
      </Card>
    </div>
  );
};
