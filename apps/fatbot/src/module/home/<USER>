import { Shield } from 'lucide-react';
import Link from 'next/link';
import { getTranslations } from 'next-intl/server';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ROUTES } from '@/constants/routes';

export const MFA: React.FC = async () => {
  const t = await getTranslations('mfa');

  return (
    <div className="grid gap-2 ">
      <h3 className="text-display-xs font-bold">{t('mfa')}</h3>
      <Card className="flex flex-col gap-2 shadow-primary" variant="primary">
        <div className="grid gap-1">
          <h4 className="text-xl font-semibold">{t('setup-mfa')}</h4>
          <p className="text-sm font-medium text-text-elevated">
            {t('description')}
          </p>
        </div>
        <Button asChild className="h-7 px-0" variant="outline">
          <Link
            href={`${ROUTES.PROFILE.SETUP_MFA.ROOT}?redirectUrl=${encodeURIComponent(ROUTES.HOME)}`}
          >
            <Shield />
            {t('link')}
          </Link>
        </Button>
      </Card>
    </div>
  );
};
