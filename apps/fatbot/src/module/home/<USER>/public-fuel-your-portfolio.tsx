'use client';

import { PlusIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';

export const PublicFuelYourPortfolio: React.FC = () => {
  const t = useTranslations();
  const { push } = useRouter();

  const redirectToSignUp = () => {
    push(`${ROUTES.SIGN_UP}?type=sign-up`);
  };

  return (
    <div className="flex items-center justify-between gap-1 rounded-xs border border-dashed border-border-subtle p-1">
      <div className="flex items-center justify-start gap-2">
        <button
          className="flex size-5 shrink-0 items-center justify-center gap-1 rounded-xs bg-surface-third-layer hover:bg-surface-primary/60"
          onClick={redirectToSignUp}
        >
          <PlusIcon className="text-primary" />
        </button>

        <p className="text-left text-md font-medium text-text-primary">
          {t('home-page.fuel-your-portfolio')}
        </p>
      </div>

      <Button className="h-5 px-3" variant="primary" onClick={redirectToSignUp}>
        {t('home-page.deposit')}
      </Button>
    </div>
  );
};
