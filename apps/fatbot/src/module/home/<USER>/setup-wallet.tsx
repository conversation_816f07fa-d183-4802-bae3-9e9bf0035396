'use client';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import type { FC } from 'react';

import { AnnotationInfo } from '@/assets';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ROUTES } from '@/constants/routes';
import { SELECTED_TAB_QUERY } from '@/module/wallets/use-tabs-literal';

interface Props {
  isPublic?: boolean;
}

export const SetupWallet: FC<Props> = ({ isPublic = false }) => {
  const t = useTranslations('home-page.asset-allocation');
  const selectedChain = useSearchParams().get(SELECTED_TAB_QUERY) ?? '';

  return (
    <Card className="gap-1 rounded-md sm:bg-surface-brand-1-alpha-cards sm:p-1">
      <div className="grid grid-cols-[24px_1fr] gap-1 text-text-active">
        <AnnotationInfo />
        <div className="flex flex-col gap-y-0.5">
          <span className="text-sm font-bold">{t('set-up-wallet')}</span>
          <p className="text-sm font-medium">
            {t('set-up-wallet-description')}
          </p>
        </div>
      </div>

      <Link
        className="p-1"
        href={
          isPublic
            ? ROUTES.SIGN_UP
            : ROUTES.SETUP_WALLET.path(selectedChain, ROUTES.WALLETS.ROOT)
        }
      >
        <Button className="w-full">{t('set-up-wallet-cta')}</Button>
      </Link>
    </Card>
  );
};
