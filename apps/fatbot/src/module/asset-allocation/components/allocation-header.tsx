import type { StaticImageData } from 'next/image';
import Image from 'next/image';

import profile from '@/assets/profile.svg';
import { AppNavigation } from '@/components/app-header/app-navigation';
import { ROUTES } from '@/constants/routes';
import { BackLink } from '@/module/routing/back-link';
import Link from 'next/link';

export const AllocationHeader = ({ leagueSystemEnabled }: { leagueSystemEnabled: boolean }) => (
  <div className="grid grid-cols-2 items-center px-0 py-2 sm:pb-3 sm:pt-4 md:grid-cols-[1fr_auto_1fr]">
    <BackLink defaultUrl={ROUTES.HOME} />
    <AppNavigation leagueSystemEnabled={leagueSystemEnabled} />
    <div className="flex items-center justify-end gap-3">
      <Link className="shrink-0" href={ROUTES.PROFILE.ROOT}>
        <Image alt="Logo" className="m-auto w-5" src={profile as StaticImageData} />
      </Link>
    </div>
  </div>
);
