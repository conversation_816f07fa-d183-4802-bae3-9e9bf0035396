'use client';

import type { StaticImageData } from 'next/image';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { type FC, useEffect } from 'react';

import { SmarterWayToTrade } from '@/assets/images';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

const REFETCH_INTERTAL = 30_000;

export const MaintenanceContent: FC = () => {
  const t = useTranslations('maintenance');
  const router = useRouter();

  useEffect(() => {
    const interval = setInterval(() => {
      router.refresh();
    }, REFETCH_INTERTAL);

    return () => {
      clearInterval(interval);
    };
  }, [router]);

  return (
    <div className="relative flex h-screen flex-col items-center justify-center overflow-hidden bg-surface-background text-center">
      <div className="absolute bottom-5 left-[40%] z-0 size-[250vh] rounded-full bg-primary md:left-[60%]" />
      <div className="z-10 grid gap-2 px-4 sm:gap-4">
        <Image alt="FatBot" className="h-36 sm:h-60" src={SmarterWayToTrade as StaticImageData} />
        <div className="flex flex-col gap-0">
          <h1 className="text-display-l font-extrabold uppercase sm:text-display-xl">{t('title')}</h1>
          <p className="m-auto max-w-[80%] text-md font-semibold text-text-secondary sm:max-w-[435px] sm:text-xl">
            {t('description')}
          </p>
        </div>
      </div>
      <Button
        className="mt-2 w-auto"
        size="md"
        onClick={() => {
          router.refresh();
        }}
      >
        {t('reload')}
      </Button>
    </div>
  );
};
