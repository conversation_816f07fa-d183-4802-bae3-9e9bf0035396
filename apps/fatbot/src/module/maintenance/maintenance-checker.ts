'use client';

import { useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';

import { useRouter } from 'next/navigation';
import { getMaintenanceConfig } from '@/module/maintenance/get-maintenance-config';

const REFETCH_INTERVAL = 30_000;

export const MaintenanceChecker = () => {
  const router = useRouter();

  const { data: isMaintenanceActive } = useQuery({
    queryKey: ['remote-config', 'maintenance_page_enabled'],
    queryFn: () => getMaintenanceConfig(),
    refetchInterval: REFETCH_INTERVAL,
  });

  useEffect(() => {
    if (isMaintenanceActive) {
      router.refresh();
    }
  }, [isMaintenanceActive, router]);

  return null;
};
