'use client';

import { keepPreviousData } from '@tanstack/react-query';
import { usePathname, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { DEFAULT_PAGINATION_SIZE_SMALL, REFETCH_INTERVAL_15_SECONDS } from '@/api/constants';
import { DataListHeader } from '@/components/data-list/data-list-header';
import { ErrorMessage } from '@/components/error-message';
import { ROUTES } from '@/constants/routes';
import { type TransactionStatus, useSearchUserTransactionOfAllTokenV3 } from '@/lib/api';
import { getErrorMessage } from '@/lib/get-error-message';

import { lastTransactionsRequestParams } from './last-transactions-request-params';
import { LastTransactionsRow } from './last-transactions-row';

const HIDE_TRANSACTION_STATUSES: TransactionStatus[] = ['PENDING', 'FAILED'];

export const LastTransactions: React.FC = () => {
  const t = useTranslations();
  const router = useRouter();
  const pathname = usePathname();

  const { data, error } = useSearchUserTransactionOfAllTokenV3(
    lastTransactionsRequestParams({ useSelectedChains: true }),
    { size: DEFAULT_PAGINATION_SIZE_SMALL },
    {
      query: {
        refetchInterval: REFETCH_INTERVAL_15_SECONDS,
        placeholderData: keepPreviousData,
      },
    },
  );

  const hasLoadingError = !!error;

  return (
    <div className="grid grid-cols-1 gap-2">
      <DataListHeader
        link={hasLoadingError ? '' : ROUTES.LAST_TRANSACTIONS.path(undefined, pathname)}
        title={t('manual-trading.last-transactions')}
      />

      {hasLoadingError ? (
        <ErrorMessage className="text-left" variant="error">
          {getErrorMessage(error) ?? t('manual-trading.errors.loading-failed')}
        </ErrorMessage>
      ) : null}

      {!hasLoadingError
        ? data?.content.map((item) => {
            const tokenAddress = item.tokenAddress;

            return (
              <LastTransactionsRow
                key={item.txId}
                chain={item.transactionChain}
                currencyAmountUsd={item.currencyAmountUsd}
                currencyNativeAmount={item.currencyNativeAmount}
                explorerUrl={item.txDetailUrl}
                hideStatusLabel={HIDE_TRANSACTION_STATUSES.includes(item.transactionStatus)}
                tokenAmount={item.tokenNativeAmount}
                tokenName={item.tokenName ?? ''}
                tokenSymbol={item.tokenSymbol ?? ''}
                transactionStatus={item.transactionStatus}
                transactionType={item.transactionType}
                {...(tokenAddress && {
                  onClick: () => {
                    router.push(ROUTES.MANUAL_TRADING.TOKEN_DETAIL(tokenAddress, item.transactionChain, pathname));
                  },
                })}
              />
            );
          })
        : null}
    </div>
  );
};
