'use client';

import { useVirtualizer } from '@tanstack/react-virtual';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { type FC, useRef, useState } from 'react';
import { useDebounceValue } from 'usehooks-ts';

import { DEFAULT_OVERSCAN } from '@/api/constants';
import { EndOfListMessage } from '@/components/data-list/messages/end-of-list-message';
import { LoadingDataMessage } from '@/components/data-list/messages/loading-data-message';
import { NoDataMessage } from '@/components/data-list/messages/no-data-message';
import { MyAssetsHeader } from '@/components/data-list/my-assets-list/my-assets-header';
import { MyAssetsRow } from '@/components/data-list/my-assets-list/my-assets-row';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { useMyAssets } from '@/module/assets/use-my-assets';
import { isNativeToken } from '@/module/assets/utils';
import { SearchTokenDialog } from '@/module/manual-trading/search-token/search-token-dialog';

import { BuyAssetsButton } from './buy-assets-button';

const ROW_GAP = 16;
const ROW_HEIGHT = 62;

const DEFAULT_DEBOUNCE_MS = 500;

export const MyAssetsDetailVirtualList: FC = () => {
  const t = useTranslations('manual-trading');
  const router = useRouter();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const searchParams = useSearchParams();
  const selectedWalletId = searchParams.get('walletId') ?? undefined;
  const searchString = searchParams.get('searchString') ?? undefined;
  const [searchStringDebounced] = useDebounceValue(searchString, DEFAULT_DEBOUNCE_MS);
  const parentRef = useRef(null);

  const { data, isLoading, isFetching } = useMyAssets({
    useSelectedChains: true,
    // TODO: in order to sort all assets reliably on FE we need to fetch them all
    // https://cleevio.atlassian.net/browse/CXFB-1966
    size: 10_000,
    walletId: selectedWalletId,
    searchString: searchStringDebounced || undefined,
  });

  const rowVirtualizer = useVirtualizer({
    count: data.length,
    estimateSize: () => ROW_HEIGHT + ROW_GAP,
    getScrollElement: () => parentRef.current,
    measureElement: (element) => {
      const naturalHeight = element.children[0]?.getBoundingClientRect().height ?? 0;
      return naturalHeight + ROW_GAP;
    },
    overscan: DEFAULT_OVERSCAN,
  });

  const virtualItems = rowVirtualizer.getVirtualItems();

  return (
    <div className="flex h-full flex-col">
      <div ref={parentRef} className="-mr-1.5 h-full space-y-2 overflow-auto pr-1.5">
        <div className="sticky top-0 z-10 hidden bg-surface-area md:grid">
          <MyAssetsHeader />
        </div>
        <div
          className="relative mt-2 transition-all duration-75"
          style={{ height: `${rowVirtualizer.getTotalSize()}px` }}
        >
          {virtualItems.map((virtualRow) => {
            const token = data[virtualRow.index];
            if (!token) {
              return null;
            }

            if (!token) return;

            return (
              <div
                key={virtualRow.index}
                ref={rowVirtualizer.measureElement}
                className="absolute left-0 top-0 w-full transition-transform-ease"
                data-index={virtualRow.index}
                style={{
                  paddingBottom: `${ROW_GAP}px`,
                  transform: `translateY(${virtualRow.start}px)`,
                }}
              >
                <MyAssetsRow
                  key={token.id}
                  chain={token.tokenChain}
                  currentValueChangeFraction={token.currentValueChangeFraction}
                  currentValueChangeUsd={token.currentValueChangeUsd}
                  currentValueUsd={token.currentValueUsd}
                  image={token.tokenImageUrl ?? ''}
                  isLoading={isFetching}
                  oneDayChangeFraction={token.oneDayChangeFraction ?? ''}
                  oneHourChangeFraction={token.oneHourChangeFraction ?? ''}
                  pricePerTokenInUsd={token.pricePerTokenInUsd}
                  tokenAmount={token.tokenNativeAmount}
                  tokenDetailUrl={token.tokenDetailUrl}
                  tokenName={token.tokenName}
                  tokenSymbol={token.tokenSymbol}
                  onClick={
                    !isNativeToken(token)
                      ? () => {
                          router.push(
                            ROUTES.MANUAL_TRADING.TOKEN_DETAIL(
                              token.tokenAddress,
                              token.tokenChain,
                              ROUTES.MY_ASSETS.path(selectedWalletId),
                            ),
                          );
                        }
                      : undefined
                  }
                />
              </div>
            );
          })}
        </div>

        {isLoading ? <LoadingDataMessage className="mt-2" message={t('loading-tokens')} /> : null}

        <BuyAssetsButton
          onClick={() => {
            setIsDialogOpen(true);
          }}
        />

        {virtualItems.length > 0 ? <EndOfListMessage /> : null}
        {!isLoading && !virtualItems.length ? <NoDataMessage message={t('no-tokens')} /> : null}
      </div>

      {isDialogOpen ? (
        <SearchTokenDialog
          onClose={() => {
            setIsDialogOpen(false);
          }}
        />
      ) : null}
    </div>
  );
};
