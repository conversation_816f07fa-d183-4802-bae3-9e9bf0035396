import { Search, XIcon } from 'lucide-react';
import { parseAsString, parseAsStringEnum, type ParserBuilder, type SetValues, useQueryStates } from 'nuqs';

import { ClearButton } from '@/components/clear-button';
import { Input } from '@/components/form/input';
import { InputAdornment } from '@/components/form/input-adornment';
import { Button } from '@/components/ui/button';
import { WalletSelect } from '@/components/wallet-select';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import { type GetAllUserWalletsResult } from '@/lib/api';
import { sortAlphabeticallyAsc } from '@/lib/sort/sort-alphabetically';

interface Props {
  wallets: GetAllUserWalletsResult[];
  loading?: boolean;
}

enum ViewState {
  SEARCH = 'search',
  SELECT = 'select',
}

interface SearchViewProps {
  searchString: string | null;
  setQuery: SetValues<{
    searchString: Parser<PERSON>uilder<string>;
    walletId: ParserBuilder<string>;
    viewState: ParserBuilder<ViewState>;
  }>;
}

const SearchView = ({ searchString, setQuery }: SearchViewProps) => (
  <div className="relative flex w-full">
    <Input
      className="rounded-full pr-6"
      icon={<Search className="size-3 text-text-active-secondary" />}
      value={searchString ?? ''}
      wrapperClassName="flex grow"
      onChange={(event) => void setQuery({ searchString: event.target.value.trim() })}
    />

    {searchString ? (
      <InputAdornment className="absolute right-2 z-10">
        <ClearButton onClear={() => void setQuery({ searchString: null })} />
      </InputAdornment>
    ) : null}
  </div>
);

interface SelectViewProps {
  searchString: string | null;
  setQuery: SetValues<{
    searchString: ParserBuilder<string>;
    walletId: ParserBuilder<string>;
    viewState: ParserBuilder<ViewState>;
  }>;
  loading?: boolean;
  wallets: GetAllUserWalletsResult[];
  walletId: string | null;
}

const SelectView = ({ searchString, setQuery, loading, wallets, walletId }: SelectViewProps) => {
  const { replace } = useRouter();

  return (
    <div className="flex w-full flex-col-reverse items-end gap-2 md:flex-row md:items-center">
      <div className="relative hidden w-full justify-end md:flex">
        <Input
          className="rounded-full pr-6"
          icon={<Search className="size-3 text-text-active-secondary" />}
          value={searchString ?? ''}
          wrapperClassName="flex w-3/4 md:w-full"
          onChange={(event) => void setQuery({ searchString: event.target.value.trim() })}
        />

        {searchString ? (
          <InputAdornment className="absolute right-2 z-10">
            <ClearButton onClear={() => void setQuery({ searchString: null })} />
          </InputAdornment>
        ) : null}
      </div>

      <div className="flex items-center justify-end  gap-x-1 md:-order-1 md:gap-x-0">
        <WalletSelect
          displayWalletBalance={true}
          loading={loading}
          selected={walletId ?? ''}
          wallets={wallets.sort((a, b) => sortAlphabeticallyAsc(a.customName ?? '', b.customName ?? ''))}
          onSelect={(walletId) => void setQuery({ walletId })}
        />
        {walletId ? (
          <button onClick={() => void setQuery({ walletId: null })}>
            <XIcon className="size-3 text-text-secondary" />
          </button>
        ) : null}

        <Button
          className="size-3 p-0 md:hidden"
          variant="ghost"
          onClick={() => {
            replace(ROUTES.MY_ASSETS.search('search', ROUTES.MY_ASSETS.ROOT));
          }}
        >
          <Search className="size-3 text-text-active-secondary" />
        </Button>
      </div>
    </div>
  );
};

export const WalletSelectSearch: React.FC<Props> = ({ wallets, loading }) => {
  const [{ searchString, walletId, viewState }, setQuery] = useQueryStates({
    searchString: parseAsString,
    walletId: parseAsString,
    viewState: parseAsStringEnum(Object.values(ViewState)).withDefault(ViewState.SELECT),
  });

  const isSearchView = viewState === ViewState.SEARCH;

  return isSearchView ? (
    <SearchView searchString={searchString} setQuery={setQuery} />
  ) : (
    <SelectView
      loading={loading}
      searchString={searchString}
      setQuery={setQuery}
      walletId={walletId}
      wallets={wallets}
    />
  );
};
