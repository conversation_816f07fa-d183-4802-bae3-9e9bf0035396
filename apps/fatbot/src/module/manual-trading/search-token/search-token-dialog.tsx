import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogOverlay,
  DialogTitle,
} from '@/components/ui/dialog';
import { Loader } from '@/components/ui/loader';
import { ROUTES } from '@/constants/routes';
import { useSearchToken } from '@/lib/api';
import { FeaturedTokens } from '@/module/manual-trading/featured-tokens/featured-tokens';
import { HotTokens } from '@/module/manual-trading/hot-tokens/hot-tokens';
import { PublicHotTokens } from '@/module/manual-trading/hot-tokens/public-hot-tokens';
import { SearchList } from '@/module/manual-trading/search-token/search-list';
import { SearchTokenForm } from '@/module/manual-trading/search-token/search-token-form';

import { SearchResultsHeader } from './search-results-header';

interface Props {
  onClose: () => void;
  isPublic?: boolean;
}

export const SearchTokenDialog: React.FC<Props> = ({ onClose, isPublic }) => {
  const t = useTranslations('manual-trading');
  const tCommon = useTranslations('common');
  const [address, setAddress] = useState('');

  const {
    data: searchResults,
    isLoading,
    isFetched,
  } = useSearchToken(address, { query: { enabled: !!address } });

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogOverlay className="bg-black/30 backdrop-blur-lg" />
      <DialogContent className="gap-2 sm:gap-4 sm:bg-surface-background md:max-w-(--breakpoint-lg)">
        <DialogHeader className="space-y-0">
          <DialogTitle className="text-left text-4xl font-bold sm:mt-0 md:text-5xl">
            {t('search-tokens')}
          </DialogTitle>
          <DialogDescription />
        </DialogHeader>
        <div className="flex w-full flex-col gap-4">
          <SearchTokenForm
            disabled={isLoading}
            onChange={setAddress}
            onClear={() => { setAddress(''); }}
          />
          {isLoading ? <Loader /> : null}
          <div className="flex flex-col gap-2">
            {isFetched ? <>
                <h3 className="text-display-xs font-bold capitalize text-text-primary">
                  {t('best-results')}
                </h3>
                <SearchResultsHeader />
              </> : null}
            {!searchResults?.length && isFetched ? <div className="flex flex-col gap-2">
                <p className="text-md">{tCommon('no-results')}</p>
              </div> : null}
            {searchResults?.map((searchResult) => (
              <Link
                key={`${searchResult.token.address}-${searchResult.chain}`}
                href={ROUTES.MANUAL_TRADING.TOKEN_DETAIL(
                  searchResult.token.address,
                  searchResult.chain
                )}
              >
                <SearchList
                  address={searchResult.token.address}
                  chain={searchResult.chain}
                  image={searchResult.imageUrl}
                  name={searchResult.token.name}
                  priceChange={searchResult.priceChangeHours24}
                  priceUsd={searchResult.priceUsd}
                  volume1hUsd={searchResult.oneHourVolumeUsd}
                />
              </Link>
            ))}
          </div>
        </div>

        <FeaturedTokens />

        {isPublic ? (
          <PublicHotTokens
            oneDayVolumeLabel={tCommon('1h-volume-fat-bot')}
            refetchInterval={0}
          />
        ) : (
          <HotTokens
            oneDayVolumeLabel={tCommon('1h-volume-fat-bot')}
            refetchInterval={0}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};
