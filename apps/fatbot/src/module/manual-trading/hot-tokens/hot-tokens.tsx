'use client';

import { useRouter , usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { DEFAULT_PAGINATION_SIZE_SMALL, REFETCH_INTERVAL_MINUTE } from '@/api/constants';
import { DataListHeader } from '@/components/data-list/data-list-header';
import { ErrorMessage } from '@/components/error-message';
import { ROUTES } from '@/constants/routes';
import { getErrorMessage } from '@/lib/get-error-message';
import { useHotTokens } from '@/module/manual-trading/use-hot-tokens';

import { HotTokensHeader } from './hot-tokens-header';
import { HotTokensRow } from './hot-tokens-row';

interface Props {
  oneDayVolumeLabel?: string;
  refetchInterval?: number;
}

export const HotTokens: React.FC<Props> = ({ oneDayVolumeLabel, refetchInterval = REFETCH_INTERVAL_MINUTE }) => {
  const t = useTranslations();
  const router = useRouter();
  const pathname = usePathname();

  const hotTokens = useHotTokens({
    refetchInterval,
    size: DEFAULT_PAGINATION_SIZE_SMALL,
    isPublic: false,
  });

  const hasLoadingError = !!hotTokens.error;
  const hasData = !!hotTokens.dataset.length;

  return (
    <div className="grid grid-cols-1 gap-2">
      <DataListHeader
        link={ROUTES.HOT_TOKENS.path(undefined, pathname)}
        title={t('manual-trading.hot-tokens-right-now')}
      />

      {hasLoadingError ? (
        <ErrorMessage className="text-left" variant="error">
          {getErrorMessage(hotTokens.error) ?? t('manual-trading.errors.loading-failed')}
        </ErrorMessage>
      ) : null}

      {hotTokens.isFetched && !hasLoadingError && !hasData ? (
        <ErrorMessage className="text-left">{t('manual-trading.errors.no-data')}</ErrorMessage>
      ) : null}

      {hasData ? <HotTokensHeader oneDayVolumeLabel={oneDayVolumeLabel} /> : null}
      {hasData
        ? hotTokens.dataset.map((item) => (
            <HotTokensRow
              key={item.id}
              chain={item.tokenChain}
              change24h={item.change24h ?? null}
              id={item.id}
              priceUsd={item.priceUsd}
              tokenAddress={item.tokenAddress}
              tokenDetailUrl={item.tokenDetailUrl}
              tokenImageUrl={item.tokenImageUrl ?? ''}
              tokenName={item.tokenName}
              volume24hUsd={item.volume24hUsd}
              onClick={() => {
                router.push(ROUTES.MANUAL_TRADING.TOKEN_DETAIL(item.tokenAddress, item.tokenChain, pathname));
              }}
            />
          ))
        : null}
    </div>
  );
};
