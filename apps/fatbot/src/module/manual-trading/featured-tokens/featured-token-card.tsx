import { usePathname, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { ExternalLink } from '@/assets/external-link';
import { IconLink } from '@/components/icon-link';
import { ROUTES } from '@/constants/routes';
import { type Chain } from '@/lib/api';
import { TokenAvatar } from '@/module/manual-trading/components/token-avatar';

interface FeaturedTokenCardProps {
  chain: Chain;
  imageUrl: string;
  tokenName: string;
  tokenDetailUrl: string;
  tokenAddress: string;
  tokenSymbol: string;
}

export const FeaturedTokenCard = ({
  chain,
  imageUrl,
  tokenName,
  tokenDetailUrl,
  tokenAddress,
}: FeaturedTokenCardProps) => {
  const t = useTranslations('manual-trading.featured-tokens');
  const router = useRouter();
  const pathname = usePathname();

  const handleClick = () =>
    { router.push(
      ROUTES.MANUAL_TRADING.TOKEN_DETAIL(tokenAddress, chain, pathname)
    ); };

  return (
    <button
      className="flex-none bg-surface-primary rounded-xs p-1 flex gap-1 shadow-sm"
      type="button"
      onClick={handleClick}
    >
      <TokenAvatar
        chain={chain}
        className="my-0"
        imageUrl={imageUrl}
        name={tokenName}
      />
      <div className="flex flex-col gap-0">
        <div className="flex gap-1">
          <span className="text-text-primary text-md">{tokenName}</span>
          <IconLink
            href={tokenDetailUrl}
            rel="noopener noreferrer"
            target="_blank"
            onClick={(event) => { event.stopPropagation(); }}
          >
            <ExternalLink />
          </IconLink>
        </div>
        <span className="text-text-elevated text-left text-sm">
          {t('zero-percent-fees')}
        </span>
      </div>
    </button>
  );
};
