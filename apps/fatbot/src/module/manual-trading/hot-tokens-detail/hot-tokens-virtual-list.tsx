'use client';

import { useVirtualizer } from '@tanstack/react-virtual';
import { useTranslations } from 'next-intl';
import { useEffect, useRef } from 'react';

import { DEFAULT_PAGINATION_SIZE, REFETCH_INTERVAL_MINUTE } from '@/api/constants';
import { EndOfListMessage } from '@/components/data-list/messages/end-of-list-message';
import { LoadingDataMessage } from '@/components/data-list/messages/loading-data-message';
import { ErrorMessage } from '@/components/error-message';
import { ROUTES } from '@/constants/routes';
import { useRouter } from 'next/navigation';
import type { Chain } from '@/lib/api';
import { elementHasScrollbar } from '@/lib/dom/element-has-scrollbar';
import { cn } from '@/lib/utils';
import { useHotTokens } from '@/module/manual-trading/use-hot-tokens';

import { HotTokensDetailRow } from './hot-tokens-detail-row';
import { HotTokensListHeader } from './hot-tokens-list-header';

const ROW_GAP = 16;
const ROW_HEIGHT = 62;

interface Props {
  quickBuyAmountUsd?: string;
  isPublic?: boolean;
  onQuickBuy?: (tokenAddress: string, chain: Chain) => void;
}

export const HotTokensVirtualList: React.FC<Props> = ({ quickBuyAmountUsd, onQuickBuy, isPublic }) => {
  const t = useTranslations();
  const router = useRouter();

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, status, isLoading, isFetchedAfterMount } = useHotTokens(
    {
      size: DEFAULT_PAGINATION_SIZE,
      refetchInterval: REFETCH_INTERVAL_MINUTE,
      isPublic,
    },
  );

  const allRows = data?.pages.flatMap((page) => page.content) ?? [];
  const parentRef = useRef<HTMLDivElement>(null);

  const rowVirtualizer = useVirtualizer({
    count: allRows.length + (hasNextPage ? 1 : 0),
    getScrollElement: () => parentRef.current,
    estimateSize: () => ROW_HEIGHT + ROW_GAP,
    measureElement: (element) => {
      const naturalHeight = element.children[0]?.getBoundingClientRect().height ?? 0;
      return naturalHeight + ROW_GAP;
    },
    overscan: 5,
  });

  const virtualItems = rowVirtualizer.getVirtualItems();

  useEffect(() => {
    const lastItem = virtualItems[virtualItems.length - 1];

    const lastItemIndex = lastItem?.index ?? 0;
    const isLastItem = lastItemIndex >= allRows.length - 1;
    const shouldFetchMore = !isFetchingNextPage && hasNextPage && isLastItem;

    if (shouldFetchMore) {
      void fetchNextPage();
    }
  }, [allRows.length, fetchNextPage, hasNextPage, isFetchingNextPage, virtualItems]);

  return (
    <div className="size-full">
      <div className={cn('hidden md:flex', elementHasScrollbar(parentRef.current) && 'md:pr-2')}>
        <HotTokensListHeader buyEnabled={!isPublic} />
      </div>

      {isFetchedAfterMount && status === 'success' && virtualItems.length === 0 ? (
        <ErrorMessage>{t('manual-trading.errors.no-data')}</ErrorMessage>
      ) : null}
      {status === 'success' ? (
        <div ref={parentRef} className="size-full overflow-auto pt-2">
          <div className="relative size-full" style={{ height: `${rowVirtualizer.getTotalSize()}px` }}>
            {virtualItems.map((virtualRow) => {
              const isLoaderRow = virtualRow.index > allRows.length - 1;
              const hotToken = allRows[virtualRow.index];
              const isEnd = !hasNextPage && virtualRow.index === allRows.length - 1;

              if (!hotToken) return null;

              return (
                <div
                  key={virtualRow.index}
                  ref={rowVirtualizer.measureElement}
                  className="absolute left-0 top-0 w-full"
                  data-index={virtualRow.index}
                  style={{
                    paddingBottom: `${ROW_GAP}px`,
                    transform: `translateY(${virtualRow.start}px)`,
                  }}
                >
                  {isLoaderRow && hasNextPage ? (
                    <LoadingDataMessage className="m-3" />
                  ) : (
                    <>
                      <HotTokensDetailRow
                        buyEnabled={!isPublic}
                        chain={hotToken.tokenChain}
                        change24h={hotToken.change24h ?? null}
                        id={hotToken.id}
                        priceUsd={hotToken.priceUsd}
                        quickBuyAmountUsd={quickBuyAmountUsd}
                        tokenAddress={hotToken.tokenAddress}
                        tokenImageUrl={hotToken.tokenImageUrl ?? ''}
                        tokenName={hotToken.tokenName}
                        tokenSymbol={hotToken.tokenSymbol}
                        volume24hUsd={hotToken.volume24hUsd}
                        onQuickBuy={() => onQuickBuy?.(hotToken.tokenAddress, hotToken.tokenChain)}
                        {...(!isLoaderRow &&
                          hotToken.tokenAddress && {
                            onClick: () => {
                              router.push(
                                ROUTES.MANUAL_TRADING.TOKEN_DETAIL(
                                  hotToken.tokenAddress,
                                  hotToken.tokenChain,
                                  ROUTES.HOT_TOKENS.ROOT,
                                ),
                              );
                            },
                          })}
                      />
                      {isEnd ? <EndOfListMessage /> : null}
                    </>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      ) : null}
      {isLoading ? <LoadingDataMessage message={t('manual-trading.loading-tokens')} /> : null}
    </div>
  );
};
